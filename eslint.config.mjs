import { defineConfig } from 'eslint/config';
import js from '@eslint/js';
import globals from 'globals';
import pluginReact from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';

export default defineConfig([
  {
    ignores: ['**/node_modules', '**/build', '**/release', '**/_docs'],
  },
  {
    files: ['**/*.{js,mjs,cjs,jsx}'],
    plugins: { js },
    extends: ['js/recommended'],
  },
  {
    files: ['**/*.js'],
    languageOptions: { sourceType: 'module' },
  },
  {
    files: ['**/*.{js,mjs,cjs,jsx}'],
    languageOptions: { globals: globals.browser },
  },
  pluginReact.configs.flat.recommended,
  pluginReact.configs.flat['jsx-runtime'],
  reactHooks.configs['recommended-latest'],
  reactRefresh.configs.recommended,
  {
    rules: {
      'quotes': [1, 'single'], // 强制使用单引号
      'eqeqeq': 1, // 必须使用 === 和 !==
      'no-empty-function': 1, // 禁止空函数
      'no-trailing-spaces': 1, // 禁止禁用行尾空格
      'space-infix-ops': 1, // 要求操作符周围有空格
      'space-in-parens': 1, // 强制在圆括号内使用一致的空格
      'no-var': 2, // 要求使用 let 或 const 而不是 var,
      'no-unused-vars': 1, // 禁止出现未使用过的变量
      'react/prop-types': 0, // 防止在react组件定义中缺少props验证

      'react-hooks/rules-of-hooks': 'error', // 检查 Hooks 的使用规则
      'react-hooks/exhaustive-deps': 'warn', // 检查依赖项的声明
      'react-refresh/only-export-components': 'off',
    },
  }
]);
