{
  "compilerOptions": {
    "jsx": "react-jsx",
    "module": "esnext",
    "allowJs": true,
    "checkJs": false,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "baseUrl": ".",
    "paths": {
      "@titd/publics/*": ["./src/*"],
      "~/*": ["./src/*"],
      "~/assets/*": ["./src/assets/*"],
      "~/components/*": ["./src/components/*"],
      "~/context/*": ["./src/context/*"],
      "~/libs/*": ["./src/libs/*"],
      "~/pages/*": ["./src/pages/*"],
      "~/utils/*": ["./src/utils/*"],
    }
  },
  "include": ["src"],
  "exclude": ["node_modules", "dist"]
}
