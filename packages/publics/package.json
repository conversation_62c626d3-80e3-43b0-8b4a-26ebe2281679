{"name": "@titd/publics", "version": "0.0.1", "description": "Public components for TITD projects", "type": "module", "main": "./src/index.js", "module": "./src/index.js", "exports": {".": "./src/index.js", "./layouts": "./src/layouts/index.js", "./assets": "./src/assets/index.js", "./components": "./src/components/index.js", "./utils": "./src/utils/index.js", "./context": "./src/context/index.js", "./libs": "./src/libs/index.js", "./pages": "./src/pages/index.js", "./states": "./src/states/index.js", "./layouts/*": "./src/layouts/*", "./assets/*": "./src/assets/*", "./components/*": "./src/components/*", "./utils/*": "./src/utils/*", "./context/*": "./src/context/*", "./libs/*": "./src/libs/*", "./pages/*": "./src/pages/*", "./states/*": "./src/states/*"}, "files": ["src"], "sideEffects": false, "dependencies": {"@ant-design/charts": "^2.6.2", "@ant-design/icons": "^6.0.0", "@ctrl/tinycolor": "^4.1.0", "@umijs/route-utils": "^4.0.1", "ahooks": "^3.9.3", "antd": "catalog:", "antd-style": "catalog:", "axios": "^1.11.0", "dayjs": "^1.11.13", "flag-icons": "^7.5.0", "keepalive-for-react": "^4.0.3", "keepalive-for-react-router": "^2.0.3", "mac-scrollbar": "^0.13.8", "omit.js": "^2.0.2", "path-to-regexp": "^8.2.0", "qs": "^6.14.0", "radash": "^12.1.1", "rc-util": "^5.44.4", "rc-resize-observer": "^1.4.3", "react": "catalog:", "react-dom": "catalog:", "react-helmet-async": "^2.0.5", "react-intl": "^7.1.11", "react-router": "^7.8.1", "recoil": "^0.7.7", "screenfull": "^6.0.2"}, "peerDependencies": {"react": "catalog:", "react-dom": "catalog:", "antd": "catalog:", "antd-style": "catalog:"}}