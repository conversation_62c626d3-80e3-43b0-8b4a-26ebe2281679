// antd 组件国际化
import zhCN from 'antd/es/locale/zh_CN';
import enUS from 'antd/es/locale/en_US';

// dayjs 时间控件国际化
import 'dayjs/locale/zh-cn';

// 自定义国际化
import zh_CN from './zh-CN';
import en_US from './en-US';

function loadLocale(lang) {
  let locale = null;
  let message = null;
  let antLocale = null;
  let dayjsLocale = null;

  switch (lang) {
    case 'en-US':
      locale = 'en-US';
      message = en_US;
      antLocale = enUS;
      dayjsLocale = 'en';
      break;
    case 'zh-CN':
      locale = 'zh-CN';
      message = zh_CN;
      antLocale = zhCN;
      dayjsLocale = 'zh-cn';
      break;
    default:
      break;
  }

  return { locale, message, antLocale, dayjsLocale }
}

export {
  loadLocale,
}
