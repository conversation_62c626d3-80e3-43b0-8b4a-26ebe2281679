export default {
  // menu
  'app.menu.db.userclient': '用户股东代码',
  'app.menu.db.userclient.desc': '用户与股东代码对应关系信息',
  'app.menu.mem.user.muserclient': '用户股东代码',
  'app.menu.mem.user.commimargin': '手续费信息',
  'app.menu.mem.user.commimargin.desc': '合约手续费信息',
  'app.menu.mem.instrument': '证券信息',
  'app.menu.mem.ord.orderinsert': '报单信息',
  // options
  'app.options.clientid': '股东代码',
  'app.options.clienttype': '股东代码类型',
  'app.options.userclient.tip': '手动输入股东代码',
  'app.options.instrumentid': '证券代码',
  'app.options.db.userinfo.clientid.tip': '注：点击“行”进行“股东代码”权限分配',
  'app.options.instrument.instrumentname': '证券名称',
  'app.options.instrument.settlementprice': '昨日收盘价',
  'app.options.instrument.marginprice': '保证金价格',
  'app.options.instrument.undermarginprice': '标的保证金价格',
  'app.options.instrument.undermarginprice.long': '计算期权保证金的标的价格',
  'app.options.instrument.volumemultiple': '证券乘数',
  'app.options.order.quote.offsetflag': '平仓标识',
};
