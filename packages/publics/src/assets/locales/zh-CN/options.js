export default {
  'app.options.userid': '用户代码',
  'app.options.userid.msg': '小写字母、数字或下划线',
  'app.options.userid.error': '交易用户代码已存在，请重新输入',
  'app.options.newpassword': '密码',
  'app.options.repassword': '确认密码',
  'app.options.repassword.error': '两次新密码不一致',
  'app.options.action': '操作',
  'app.options.clientid': '交易编码',
  'app.options.clienttype': '交易编码类型',
  'app.options.exchangeid': '交易所',
  'app.options.serverid': '允许交易服务器',
  'app.options.serverid.long': '允许该交易编码交易的服务器',
  'app.options.accountid': '资金账户',
  'app.options.accounttype': '资金账户类型',
  'app.options.bizpbu': '业务PBU',
  'app.options.salesnumb': '营业部代码',
  'app.options.frozenfunds': '分配资金',
  'app.options.usertype': '交易用户类型',
  'app.options.productid': '品种代码',
  'app.options.productid.tip': '选择交易所，显示相应的品种',
  'app.options.templateid': '权限模版',
  'app.options.templateid.error': '权限模版已存在，请重新输入',
  'app.options.rightid': '交易权限',
  'app.options.tradingday': '交易日',
  'app.options.sessionno': 'SESSION序号',
  'app.options.userclient': '交易用户',
  'app.options.userclient.tip': '手动输入交易编码',
  'app.options.instrumentid': '合约代码',
  'app.options.underlyinginstrid': '标的商品代码',
  'app.options.clusterid': '集群',
  'app.options.segmentid': '集群分区',
  'app.options.defaultsvrid': '默认交易服务器',

  // ------ 场下管理 ------

  // 用户信息
  'app.options.db.userinfo.name': '用户名称',
  'app.options.db.userinfo.status': '用户状态',
  'app.options.db.userinfo.usertype': '用户类型',
  'app.options.db.userinfo.tradeflow': '交易流控',
  'app.options.db.userinfo.tradeflow.long': '每秒允许交易的最大次数',
  'app.options.db.userinfo.logincount': '并发登录数',
  'app.options.db.userinfo.loginsuccess': '允许登录成功数',
  'app.options.db.userinfo.loginsuccess.long': '一个交易日内允许登录成功的数量',
  'app.options.db.userinfo.loginfailed': '允许登录失败数',
  'app.options.db.userinfo.loginfailed.long': '一个交易日内允许登录失败的数量',
  'app.options.db.userinfo.lastloginip': '最后登录的IP地址',
  'app.options.db.userinfo.lastlogintime': '最后登录时间',
  'app.options.db.userinfo.clientid.tip': '注：点击“行”进行“交易编码”权限分配',
  'app.options.db.userinfo.right.none': '未有',
  'app.options.db.userinfo.right.have': '已有',
  'app.options.db.userinfo.minlocalid': '最小本地报单编号',
  'app.options.db.userinfo.maxlocalid': '最大本地报单编号',

  // APPID
  'app.options.db.appid.msg': '请按格式规范填写',
  'app.options.db.appid.extra': '格式: 3-10位厂商名称_3-10位软件名称_2-8位版本号(字母、数字，版本号可加“.”)',
  'app.options.db.appid.placeholder': 'xxxxxxxxxx_xxxxxxxxxx_x.x.x',
  'app.options.db.appid.identify': '验证码',
  'app.options.db.appid.identify.msg': '10-20位数字或字母',
  'app.options.db.appid.identify.extra': '验证码只显示一次，如若忘记请重新生成！',
  'app.options.db.appid.error': 'APPID已存在，请重新输入',

  // 资金分配
  'app.options.db.funds.allocttype': '分配模式',
  'app.options.db.funds.allocttype.child': '内部分配方式',
  'app.options.db.funds.scalemode1': '主次席分配模式',
  // 'app.options.db.funds.scalemode1.tip': '正表示次席可用金额或比例, 负表示主席预留金额或比例',
  'app.options.db.funds.scalemode1.tip': '按金额：正表示次席可用金额, 负表示主席预留金额',
  'app.options.db.funds.scalemode2': '次席内部分配方式',
  'app.options.db.funds.scalemode2.tip': '“分配剩余全部” 仅能分配一个交易所',
  'app.options.db.funds.scale1': '次席分配资金',
  'app.options.db.funds.scale2': '次席内部分配详情',
  'app.options.db.funds.scale2.long': '“#” 表示分配剩余全部资金',
  'app.options.db.funds.isremaining': '分配剩余全部',
  'app.options.db.funds.symbol': '¥',
  'app.options.db.funds.unit': '元',
  'app.options.db.funds.rights': '分配权益',
  'app.options.db.funds.available': '分配可用资金',
  'app.options.db.funds.scaledetail': '分配详情',

  // 上场确认
  'app.options.db.log.importdate': '导入日期',
  'app.options.db.log.suredate': '确认日期',
  'app.options.db.log.sure': '交易日确认',
  'app.options.db.log.sure.success': '确认成功',
  'app.options.db.log.sure.again': '重新确认',
  'app.options.db.log.lastdate': '最后确认日期',
  'app.options.db.log.sured': '已确认',
  'app.options.db.log.nosure': '未确认',

  // 数据校验
  'app.options.db.datacompare.account': '资金',
  'app.options.db.datacompare.position': '持仓',
  'app.options.db.datacompare.tips': '仅支持 CSV 文件上传，请注意区分，对应上传',
  'app.options.db.datacompare.check': '校验',
  'app.options.db.datacompare.file': '文件',
  'app.options.db.datacompare.please': '请上传',
  'app.options.db.datacompare.title': '( TITD <-> CTP )',
  'app.options.db.datacompare.error': ' 不是一个 CSV 文件',
  'app.options.db.datacompare.uploadnum': '上传数据 {titdnum} 条，系统数据 {ctpnum} 条',
  'app.options.db.datacompare.overview': '共校验 {total} 条数据，相同 {same} 条，不同 {diff} 条',
  'app.options.db.datacompare.onlynum': 'TITD 独有 {titdonly} 条，',
  'app.options.db.datacompare.onlynum.account': '忽略 {ctponly} 条，仅开户 {openonly} 条',
  'app.options.db.datacompare.onlynum.position': 'CTP 独有 {ctponly} 条，忽略 {openonly} 条',
  'app.options.db.datacompare.account.titdonly.tip': '只有在 TITD 中有的资金',
  'app.options.db.datacompare.account.ctponly.tip': 'CTP 结算文件中有但未在 TITD 柜台开户的账号条数',
  'app.options.db.datacompare.account.openonly.tip': '已在 TITD 柜台开户但场上没有资金详情的数量',
  'app.options.db.datacompare.position.titdonly.tip': '只有 TITD 中有的数据',
  'app.options.db.datacompare.position.ctponly.tip': '只有上传的结算文件中有的数据',
  'app.options.db.datacompare.position.openonly.tip': '结算文件中有但未在 TITD 柜台开户的持仓',
  'app.options.db.datacompare.common': '相同数据',
  'app.options.db.datacompare.accountid': '投资者代码',
  'app.options.db.datacompare.accountname': '投资者名称',
  'app.options.db.datacompare.distribfund': '期末权益',
  'app.options.db.datacompare.futmargin': '维持保证金',
  'app.options.db.datacompare.datasrc': '系统',
  'app.options.db.datacompare.clientid': '证券账户',
  'app.options.db.datacompare.longvolume': '权利仓持仓量',
  'app.options.db.datacompare.shortvolume': '义务仓持仓量',
  'app.options.db.datacompare.longcomb': '组合权利仓持仓量',
  'app.options.db.datacompare.shortcomb': '组合义务仓持仓量',
  'app.options.db.datacompare.margin': '单腿保证金',
  'app.options.db.datacompare.discount': '投资者保证金',

  // 风控用户
  'app.options.db.userrisk.userid': '风控用户代码',
  'app.options.db.userrisk.isglobal': '是否全局',
  'app.options.db.userrisk.traderight': '是否有交易权限',
  'app.options.db.userrisk.ipmac': 'IP-MAC 绑定',
  'app.options.db.userrisk.ipmac.msg': '请按格式规范填写',
  'app.options.db.userrisk.ipmac.extra': '格式: IP_MAC|127.0.0.1_00-00-00-00-00-00',
  'app.options.db.userrisk.lastlogintime': '创建时间',
  'app.options.db.userrisk.clientid': '绑定交易编码',
  'app.options.db.userrisk.none': '未绑定',
  'app.options.db.userrisk.done': '已绑定',

  // 看穿式监管
  'app.options.db.exsubmit.serverid': '服务器',
  'app.options.db.exsubmit.loginid': '登录账号',
  'app.options.db.exsubmit.submitinfo': '上报信息',

  // 网关管理
  'app.options.db.gateway.id': '网关号',
  'app.options.db.gateway.type': '网关类型',
  'app.options.db.gateway.ispreorder': '只用于报送预埋单',
  'app.options.db.gateway.front': '平台类型',
  'app.options.db.gateway.address': '网关地址',
  'app.options.db.gateway.address.msg': '请填写正确的IP地址',
  'app.options.db.gateway.status': '连接状态',
  'app.options.db.gateway.action': '运行状态',
  'app.options.order.gateway': '报单网关',

  // ------ 场上管理 ------

  // 登录账户
  'app.options.mem.userinfo.maxloginsuccess': '登录成功次数',
  'app.options.mem.userinfo.maxloginsuccess.long': '一个交易日内允许登录成功的数量',
  'app.options.mem.userinfo.maxloginfailed': '登录失败次数',
  'app.options.mem.userinfo.maxloginfailed.long': '一个交易日内允许登录失败的数量',
  'app.options.mem.userinfo.loginsuccess': '登录成功数量',
  'app.options.mem.userinfo.loginfailed': '登录失败数量',
  'app.options.mem.userinfo.timeout': '用户超时数量',
  'app.options.mem.userinfo.logout': '用户登出数量',
  'app.options.mem.userinfo.orderinsert': '报单数量',
  'app.options.mem.userinfo.ordertierr': '错单数量',
  'app.options.mem.userinfo.orderexerr': '交易所错单数量',
  'app.options.mem.userinfo.ordercancel': '撤单数量',
  'app.options.mem.userinfo.canceltierr': '撤单错数量',
  'app.options.mem.userinfo.cancelexerr': '交易所撤单错数量',
  'app.options.mem.userinfo.cancelmax': '最大错单数量',
  'app.options.mem.userinfo.sequenceno': '私有流数量',

  // 用户交易编码
  'app.options.mem.userclient.status': '交易状态',
  'app.options.mem.userclient.status.tip': '设成正常后，该股东代码针对所有证券都可交易， 请谨慎操作！',

  // Session 信息
  'app.options.mem.sessionno.istimeout': '用户状态',
  'app.options.mem.sessionno.reqipaddr': '发送请求的IP地址',
  'app.options.mem.sessionno.reqport': '发送请求的端口号',
  'app.options.mem.sessionno.rspipaddr': '接收应答的IP地址',
  'app.options.mem.sessionno.rspport': '接收应答的端口号',
  'app.options.mem.sessionno.lastrecmsg': '最后收到消息的时间',
  'app.options.mem.sessionno.apiversion': 'API版本信息',
  'app.options.mem.sessionno.prvsub': '是否订阅了私有流',
  'app.options.mem.sessionno.pubsub': '是否订阅了公有流',
  'app.options.mem.sessionno.offline': '您确定要离线该 Session 吗？',
  'app.options.mem.sessionno.offline.tip': '注：离线后用户状态会更改为“禁止登录”，请前往登录账户设置以恢复登录',

  // 资金信息
  'app.options.mem.account.prebalance': '上次结算准备金',
  'app.options.mem.account.distribfund': '本系统分配资金',
  'app.options.mem.account.availusedfund': '可用资金',
  'app.options.mem.account.dailyprofit': '每日盈亏',
  'app.options.mem.account.commi': '手续费',
  'app.options.mem.account.futmargin': '期货保证金',
  'app.options.mem.account.futmargin.long': '当前期货保证金总额',
  'app.options.mem.account.optmargin': '期权保证金',
  'app.options.mem.account.optmargin.long': '当前期权保证金总额',
  'app.options.mem.account.combmargin': '保证金优惠',
  'app.options.mem.account.combmargin.long': '当前保证金优惠总额',
  'app.options.mem.account.closeprofit': '平仓盈亏',
  'app.options.mem.account.posiprofit': '持仓盈亏',
  'app.options.mem.account.premium': '期权权利金收支',
  'app.options.mem.account.deposit': '入金金额',
  'app.options.mem.account.withdraw': '出金金额',
  'app.options.mem.account.frozenmargin': '冻结保证金',
  'app.options.mem.account.frozenpremium': '冻结权利金',
  'app.options.mem.account.frozencommi': '冻结手续费',
  'app.options.mem.account.entryfees': '当日申报费用',
  'app.options.mem.account.buypremium': '占用权利金',
  'app.options.mem.account.buypremium.long': '权力仓当前占用的权利金',
  'app.options.mem.account.amount.msg': '请输入{label}金额',
  'app.options.mem.account.recvmargin': '实收保证金',
  'app.options.mem.account.recvmargin.long': '实收的期权保证金',
  'app.options.mem.account.depositwithdraw': '出入金',
  'app.options.mem.account.deposited': '已入金',
  'app.options.mem.account.withdrawed': '已出金',
  'app.options.mem.account.amount': '金额',

  // 手续费保证金
  'app.options.mem.commimargin.percent': '(‰)',
  'app.options.mem.commimargin.openbymoney': '开仓手续费率',
  'app.options.mem.commimargin.openbyvolume': '开仓手续费',
  'app.options.mem.commimargin.closebymoney': '平仓手续费率',
  'app.options.mem.commimargin.closebyvolume': '平仓手续费',
  'app.options.mem.commimargin.closetodaybymoney': '平今手续费率',
  'app.options.mem.commimargin.closetodaybyvolume': '平今手续费',
  'app.options.mem.commimargin.openmax.short': '开仓最高',
  'app.options.mem.commimargin.openmax': '开仓单笔最高手续费',
  'app.options.mem.commimargin.openmin.short': '开仓最低',
  'app.options.mem.commimargin.openmin': '开仓单笔最低手续费',
  'app.options.mem.commimargin.closemax.short': '平仓最高',
  'app.options.mem.commimargin.closemax': '平仓单笔最高手续费',
  'app.options.mem.commimargin.closemin.short': '平仓最低',
  'app.options.mem.commimargin.closemin': '平仓单笔最低手续费',
  'app.options.mem.commimargin.closetodaymax.short': '平今最高',
  'app.options.mem.commimargin.closetodaymax': '平今单笔最高手续费',
  'app.options.mem.commimargin.closetodaymin.short': '平今最低',
  'app.options.mem.commimargin.closetodaymin': '平今单笔最低手续费',
  'app.options.mem.commimargin.margin': '保证金',
  'app.options.mem.commimargin.marginratioparam': '保证金参数',
  'app.options.mem.commimargin.marginrate': '收取比例',
  'app.options.mem.commimargin.marginrate.long': '在交易所基础上收取的比例',
  'app.options.mem.commimargin.longbymoney': '多头保证金率',
  'app.options.mem.commimargin.longbyvolume': '多头保证金费',
  'app.options.mem.commimargin.shortbymoney': '空头保证金率',
  'app.options.mem.commimargin.shortbyvolume': '空头保证金费',

  // 认购、认沽期权计算
  'app.options.mem.margincalc.call': '认购期权保证金计算',
  'app.options.mem.margincalc.call.tip': '说明：认购期权义务仓维持保证金 = [ 期权价 + Max( 12% x 标的价 - 认购期权虚值, 7% x 标的价)] x 合约单位',
  'app.options.mem.margincalc.put': '认沽期权保证金计算',
  'app.options.mem.margincalc.put.tip': '说明：认沽期权义务仓维持保证金 = Min( 期权价 + Max( 12% x 标的价 - 认沽期权虚值, 7% x 行权价格), 行权价格) x 合约单位',
  'app.options.mem.margincalc.instrumentname': '期权合约',
  'app.options.mem.margincalc.exerciseprice': '行权价格',
  'app.options.mem.margincalc.lastprice': '期权价',
  'app.options.mem.margincalc.stockprice': '标的价',
  'app.options.mem.margincalc.outmoney': '虚值',
  'app.options.mem.margincalc.number': '合约数量',
  'app.options.mem.margincalc.header': '以下取大Max',
  'app.options.mem.margincalc.call.marginratioparam1': '12% x 标的价 - 认购期权虚值',
  'app.options.mem.margincalc.call.marginratioparam2': '7% x 标的价',
  'app.options.mem.margincalc.put.marginratioparam1': '12% x 标的价 - 认沽期权虚值',
  'app.options.mem.margincalc.put.marginratioparam2': '7% x 行权价格',
  'app.options.mem.margincalc.margin': '保证金计算结果',
  'app.options.mem.margincalc.notopt': '非期权合约',
  'app.options.mem.margincalc.error': '暂无保证金核对数据',

  // 风控信息
  'app.options.risk.tdstatus': '交易状态',
  'app.options.risk.buyhold': '权利仓持仓数量',
  'app.options.risk.tothold': '总持仓数量',
  'app.options.risk.today': '当日买入开仓数量',
  'app.options.risk.amount': '期权限购额度',

  // 合约信息
  'app.options.instrument.producttype': '产品类型',
  'app.options.instrument.optionstype': '期权类型',
  'app.options.instrument.expiredate': '到期日',
  'app.options.instrument.instrumentname': '合约名称',
  'app.options.instrument.lastprice': '最新价',
  'app.options.instrument.settlementprice': '昨日结算价',
  'app.options.instrument.marginprice': '期权价',
  'app.options.instrument.marginprice.long': '计算期权保证金所用期权价',
  'app.options.instrument.undermarginprice': '标的价',
  'app.options.instrument.undermarginprice.long': '计算期权保证金所用标的价',
  'app.options.instrument.upperlimitprice': '涨停板价格',
  'app.options.instrument.lowerlimitprice': '跌停板价格',
  'app.options.instrument.volumemultiple': '合约乘数',
  'app.options.instrument.stockprice': '标的最新价',
  'app.options.instrument.deliveryyear': '交割年份',
  'app.options.instrument.deliverymonth': '交割月份',
  'app.options.instrument.advancemonth': '提前月份',
  'app.options.instrument.createdate': '创建日',
  'app.options.instrument.opendate': '上市日',
  'app.options.instrument.startdelivdate': '开始交割日',
  'app.options.instrument.enddelivdate': '最后交割日',
  'app.options.instrument.basisprice': '挂牌基准价',
  'app.options.instrument.strikeprice': '行权价格',
  'app.options.instrument.maxmarketordervolume': '市价单最大下单量',
  'app.options.instrument.minmarketordervolume': '市价单最小下单量',
  'app.options.instrument.maxlimitordervolume': '限价单最大下单量',
  'app.options.instrument.minlimitordervolume': '限价单最小下单量',
  'app.options.instrument.pricetick': '最小变动价位',
  'app.options.instrument.allowdelivpersonopen': '交割月自然人开仓',
  'app.options.instrument.tradedays': '剩余交易日',
  'app.options.instrument.tradedays.long': '距到期日还剩几个交易日',
  'app.options.instrument.tplus1': 'T+1 交易',

  // 持仓
  'app.options.postion.l': '多头',
  'app.options.postion.s': '空头',
  'app.options.postion.c': '备兑',
  'app.options.postion.position': '今日持仓',
  'app.options.postion.position.long': '现有持仓中属于今日持仓的',
  'app.options.postion.ydposition': '上日持仓',
  'app.options.postion.ydposition.long': '现有持仓中属于昨仓的',
  'app.options.postion.total': '总持仓',
  'app.options.postion.frozen': '冻结',
  'app.options.postion.buy': '当日买入',
  'app.options.postion.sell': '当日卖出',
  'app.options.postion.holdprice': '持仓均价',
  'app.options.postion.holdprice.long': '开仓价的加权平均，其中昨仓按昨结价计算',
  'app.options.postion.margin': '持仓保证金',
  'app.options.postion.margin.long': '期权会随着行情的变化而变化, 期货的保证金买入后不变',
  'app.options.postion.tips': '总持仓 = 今日持仓 + 上日持仓',
  'app.options.postion.tradeid': '成交编号',
  'app.options.postion.openday': '开仓日',
  'app.options.postion.side': '买卖方向',
  'app.options.postion.covereduncovered': '备兑标签',
  'app.options.postion.volume': '持仓数量',
  'app.options.postion.price': '持仓价格',
  'app.options.postion.volumemultiple': '合约乘数',
  'app.options.postion.combinstid': '组合编码',
  'app.options.postion.combinstid.long': '组合申报时为空，拆分申报时为拟拆分组合的组合编码',
  'app.options.postion.omlid': '组合类型',
  'app.options.postion.frozenvolume': '冻结数量',
  'app.options.postion.frozenvolume.long': '单边平仓时被冻结的数量',
  'app.options.postion.legmargin': '改变的保证金',
  'app.options.postion.noleges': '成分合约数量',
  'app.options.postion.noleges.long': '取值不超过4，后接重复组',
  'app.options.postion.legsecurityid': '合约编码',
  'app.options.postion.legside': '持仓方向',
  'app.options.postion.legorderqty': '申报数量',

  // 报单
  'app.options.order.ordersysid': '报单编号',
  'app.options.order.ordersysid.long': '交易所报单编号',
  'app.options.order.begtime': '起始时间',
  'app.options.order.endtime': '结束时间',
  'app.options.order.time.long': '本功能设置的时间段包含起始日期与结束日期的全天（自起始日00:00至结束日24:00），该时间段内所有时间点均需执行指标控制。',
  'app.options.order.buy': '买方向',
  'app.options.order.sell': '卖方向',
  'app.options.order.status': '状态',
  'app.options.order.ordstatus': '报单状态',
  'app.options.order.bordstatus': '买方状态',
  'app.options.order.sordstatus': '卖方状态',
  'app.options.order.userrange': '用户范围',
  'app.options.order.userrange.long': '“所有用户”根据交易编码筛选，与交易用户代码无关',
  'app.options.order.bssystemflag': '系统标识',
  'app.options.order.cmdtype': '操作类型',
  'app.options.order.ordtime': '报单时间',
  'app.options.order.exrequestid': '柜台本地报单编号',
  'app.options.order.exrequestno': '柜台本地报单序号',
  'app.options.order.ordrequestno': '请求编号',
  'app.options.order.localorderno': '本地报单编号',
  'app.options.order.ordresponsecode': '报单错误代码',
  'app.options.order.rec2send': '内部耗时(微秒)',
  'app.options.order.rec2exrsp': '交易所耗时(毫秒)',
  'app.options.order.cpuid': 'CPUID',
  'app.options.order.cancelvolume': '订单撤销数量',
  'app.options.order.ssepartyid': '交易所席位',
  'app.options.order.bfrozenvolume': '冻结数量',
  'app.options.order.bthawvolume': '已解冻数量',
  'app.options.order.quoteid': '报价号',
  'app.options.order.frontno': '席位序号',
  'app.options.order.frontno.long': '本次报单的席位序号，当所填的序号在系统中不存在时系统会优选最佳席位进行报单，否则根据用户所选席位进行报单',
  'app.options.order.pricetype': '报单价格条件',
  'app.options.order.pricetype.long': '报单价格条件限价单市价单等',
  'app.options.order.offsetflag': '开平标记',
  'app.options.order.hedgeflag': '投机套保标记',
  'app.options.order.timeinforce': '有效期类型',
  'app.options.order.volumecondition': '成交量类型',
  'app.options.order.trigcondition': '触发条件',
  'app.options.order.volume': '数量',
  'app.options.order.minvolume': '最小成交量',
  'app.options.order.price': '价格',
  'app.options.order.stopprice': '止损价',
  'app.options.order.ownertype': '订单所有类型',
  'app.options.order.cancel.ordstatus': '撤单状态',
  'app.options.order.orilocalorderno': '原始本地报单编号',
  'app.options.order.orisessionno': '原始SESSION序号',
  'app.options.order.orisessionno.long': '原始报单的sessionno，如果通过localorderno撤单，需要填写；不填写则默认撤销本次登录后的localorderno',
  'app.options.order.oriexrequestid': '原始报单请求编号',
  'app.options.order.oriexrequestid.long': '原始柜台本地报单编号',
  'app.options.order.oriordersysid': '原始交易所报单编号',
  'app.options.order.cancel.cmdtype': '撤销类型',
  'app.options.order.cancel.ordtime': '撤单时间',
  'app.options.order.cancel.ordresponsecode': '撤单错误代码',
  'app.options.order.securityid': '组合策略编码',
  'app.options.order.oml.side': '组合与拆分',
  'app.options.order.oml.legside': '合约方向',
  'app.options.order.bfrozen': '买方冻结',
  'app.options.order.sfrozen': '卖方冻结',
  'app.options.order.comb.volume': '申报数量',
  'app.options.order.quotereqid': '报价请求编号',
  'app.options.order.quote.ordtime': '报价时间',
  'app.options.order.quote.px': '报价',
  'app.options.order.quote.offsetflag': '开平标记',

  // 成交
  'app.options.trade.tradeid': '成交编号',
  'app.options.trade.transtime': '成交时间',
  'app.options.trade.tradeprice': '成交价格',
  'app.options.trade.tradevolume': '成交数量',
  'app.options.trade.leavesvolume': '申报余量',
  'app.options.trade.leavesvolume.long': '本次成交后申报余额数量',
  'app.options.trade.premium': '权利金',

  // 回报
  'app.options.rtn.sequenceno': '回报序号',
  'app.options.rtn.transtime': '发生时间',
  'app.options.rtn.begseqno': '起始序号',
  'app.options.rtn.begseqno.long': '起始回报流序号',
  'app.options.rtn.endseqno': '结束序号',
  'app.options.rtn.endseqno.long': '结束回报流序号',
  'app.options.rtn.leavesvolume': '订单剩余数量',
  'app.options.rtn.userid.long': '原始报单交易用户代码',
  'app.options.rtn.changemargin': '保证金变化值',
  'app.options.rtn.changemargin.long': '组合或拆分后保证金的变化值(组合为正，拆分为负)',
  'app.options.rtn.bidorderid': '买方编号',
  'app.options.rtn.bidorderid.long': '买方交易所订单编号',
  'app.options.rtn.askorderid': '卖方编号',
  'app.options.rtn.askorderid.long': '卖方交易所订单编号',

  // 自成交
  'app.options.self.price': '报单价格',
  'app.options.self.ordertime': '报单时间',
  'app.options.self.buymax': '买单最大价格',
  'app.options.self.sellmin': '卖单最小价格',

  // 组合
  'app.options.order.leg': '腿',

  // ------ 系统管理 ------

  'app.options.sys.adminid': '管理账号',
  'app.options.sys.logid': '日志序号',
  'app.options.sys.logip': 'IP 地址',
  'app.options.sys.logmac': 'MAC 地址',
  'app.options.sys.logtime': '日志时间',
  'app.options.sys.logtype': '日志类型',
  'app.options.sys.logdetail': '日志详情',
  'app.options.sys.loginname': '管理账号',
  'app.options.sys.role': '角色',
  'app.options.sys.roleid': '角色标识',
  'app.options.sys.rolename': '角色名称',
  'app.options.sys.rolemenu': '菜单权限',

  // ------ 场上历史 ------

  'app.options.hist.result': '登录状态',
  'app.options.hist.loginip': '登录IP',
  'app.options.hist.macaddr': 'MAC地址',
  'app.options.hist.logtime': '登录时间',
  'app.options.hist.logevent': '登录详情',
  'app.options.hist.message': '备注',
};
