export default {
  'app.stock.userclient.ordernum': '委托数量',
  'app.stock.userclient.islimit': '每秒笔数限制',
  'app.stock.userclient.return': '优先归还负债',

  'app.stock.trade.premium': '资金收支',
  'app.stock.trade.frozenpremium': '冻结资金',

  // 现货手续费
  'app.stock.template.templateid': '模版号',
  'app.stock.template.productid': '证券类别',
  'app.stock.template.productother': '其他类别',
  'app.stock.template.product.other': '其他',

  // 报单
  'app.stock.order.exrequestno': '柜台本地报单序号',
  'app.stock.order.price': '成交金额',
  'app.stock.order.frozen': '冻结金额',
  'app.stock.order.price.tip': '设置最大成交金额后有数据',
  'app.stock.order.exexecutno': '交易所执行编号',
  'app.stock.order.ordinstrumentno': '原始证券代码',
  'app.options.order.unaction': '撤单已锁定',

  // 现货非交易业务报单信息
  'app.stock.businsert.businesstype': '业务类型',
  'app.stock.businsert.destinstrumentid': '目标证券代码',

  // 资金划转
  'app.stock.account.transferno': '划转编号',
  'app.stock.account.direct': '划转方向',
  'app.stock.account.destsvrid': '划转对象',
  'app.stock.account.status': '状态',
  'app.stock.account.tradeday': '操作日期',
  'app.stock.account.logtime': '操作时间',
  'app.stock.account.logmessage': '消息',
  'app.stock.account.seat': '主席',
  'app.stock.account.localsys': '本系统',
  'app.stock.account.source': '出金系统',
  'app.stock.account.dest': '入金系统',
  'app.stock.account.err': '出入金系统不能相同',
  // 股份划转
  'app.stock.account.positionuse': '可划转持仓',
  'app.stock.account.volume': '划转数量',
  'app.stock.account.flag': '划拨标志',
  'app.stock.account.pub': '席位',
  'app.stock.account.reversal': '是否充正帐',
  'app.stock.account.loguserid': '操作者',
  'app.stock.account.bclient': '发起端',
  'app.stock.share.source': '转出系统',
  'app.stock.share.dest': '转入系统',

  // 证券信息
  'app.stock.instrument.buyordertick': '买入数量单位',
  'app.stock.instrument.sellordertick': '卖出数量单位',
  'app.stock.instrument.remark': '备注',
  'app.stock.instrument.allowedascollateral': '是否担保品',
  'app.stock.instrument.collateralconvrate': '折算率',
  'app.stock.instrument.collateralconvrate.long': '担保品折算率',
  'app.stock.instrument.allowedfi': '是否融资',
  'app.stock.instrument.fimargin': '融资比例',
  'app.stock.instrument.fimargin.long': '融资保证金比例',
  'app.stock.instrument.allowedsl': '是否融券',
  'app.stock.instrument.slmargin': '融券比例',
  'app.stock.instrument.slmargin.long': '融券保证金比例',

  // 风控信息
  'app.stock.risk.tothold': '持仓限制',
  'app.stock.risk.buymoney': '买入金额',
  'app.stock.risk.sellmoney': '卖出金额',
  'app.stock.risk.totalmoney': '总金额',

  'app.stock.tradevolume': '成交数量',
  'app.stock.trademoney': '成交金额',
  'app.stock.tradecommi': '成交手续费',

  // 持仓详情
  'app.stock.position.kcdzqsz': '充抵保证金',
  'app.stock.position.kcdzqsz.long': '可充抵保证金的证券市值 x 折算率',
  'app.stock.position.rzyl': '融资盈亏',
  // 'app.stock.position.rzyl.long': '(融资买入证券市值 - 融资买入金额) x 折算率',
  'app.stock.position.rqyl': '融券盈亏',
  // 'app.stock.position.rqyl.long': '(融券卖出金额 - 融券卖出证券市值) x 折算率',
  'app.stock.position.rqfz': '融券合约金额',
  'app.stock.position.rzbzj': '两融保证金',
  // 'app.stock.position.rzbzj.long': '融资买入证券金额 x 融资保证金比例',
  // 'app.stock.position.rqzj': '融券保证金',
  // 'app.stock.position.rqzj.long': '融券卖出证券市值 x 融券保证金比例',
  'app.stock.position.interest': '利息',
  'app.stock.postion.ydvolume': '昨日数量',
  'app.stock.postion.ydvolume.long': '日初持仓数量',
  'app.stock.postion.allvolume': '总数量',
  'app.stock.postion.allvolume.long': '当前持仓数量',
  'app.stock.postion.returnvolume': '未还数量',
  'app.stock.position.toreturnprice': '未还本金',
  'app.stock.position.returncommi': '未还手续费',
  'app.stock.postion.lrvolume': '两融数量',
  'app.stock.postion.lrvolume.long': '按未还金额折算出融资持仓数量',
  'app.stock.postion.instrvolume': '合约数量',
  'app.stock.position.allmargin.long': '可用保证金加 = 充抵保证金 + 融资盈亏 - 两融保证金 - 未还手续费 - 未还利息',

  // 资金信息
  'app.stock.account.availusedmargin': '净保证金',
  'app.stock.account.availusedmargin.long': '持仓详情“可用保证金”之和',
  'app.stock.account.kcdzqsz.long': '持仓详情“充抵保证金”之和',
  'app.stock.account.rzbzj.long': '持仓详情“两融保证金”之和',
  'app.stock.account.allmargin': '可用保证金',
  'app.stock.account.allmargin.long': '可用保证金 = 净保证金 + 可用资金',
  'app.stock.account.returnmargin': '归还保证金',
  'app.stock.account.xycommi': '两融合约费用',
  'app.stock.account.forzenxycommi': '冻结两融合约费用',
  'app.stock.account.interest': '两融合约利息',
  'app.stock.account.respstr1': '维持担保比(%)',
  'app.stock.account.respstr2': '证券市值',
  'app.stock.account.respstr3': '融资买入总和',
  'app.stock.account.respstr3.long': '融资未还本金',
  'app.stock.account.respstr4': '融券卖出市值总和',
  'app.stock.account.forzenenrzbzj': '冻结两融保证金',
  'app.stock.account.contremainamt': '融资金额',
  'app.stock.account.contremainamt.long': '融资未还本金 + 融券未还金额',
  'app.stock.account.frozencombmargin': '未成交充抵保证金',
  'app.stock.account.allamt': '总负债',
  'app.stock.account.allprice': '总资产',
  'app.stock.account.allprice.long': '总资产 = 可用资金 + 证券市值 + 冻结资金 + 冻结手续费',
  'app.stock.account.calc': '计算',

  'app.stock.limit.order.volume': '最大委托数量',
  'app.stock.mem.commimargin.percent': '(‱)',

  // 黑名单
  'app.stock.blacklist.instrument.groupid': '合约组名',
  'app.stock.blacklist.user.groupid':' 用户组名',
  'app.stock.blacklist.instrument':'合约',
  'app.stock.blacklist.user':'用户',
  'app.stock.blacklist.syncto':'同步至场上',
  'app.stock.blacklist.tip': '实时同步至场上',
  'app.stock.blacklist.errmsg': '错误日志',
  'app.stock.blacklist.range': '查询范围',
  'app.stock.blacklist.download': '下载模板',

  // --------- 两融 ---------
  'app.stock.concentrate.group': '组名',

  // 持仓汇总
  'app.stock.position.checksingle': '单一证券集中度',
  'app.stock.position.checkgroup': '组内证券集中度',
  'app.stock.position.check.cents': '证券集中度(%)',
  'app.stock.position.check.limits': '维持担保比R',
  'app.stock.position.check.ratiomaxs': '最大证券集中度(%)',
  'app.stock.position.check.limitmin': '维持担保比最小值',
  'app.stock.position.check.limitmax': '维持担保比最大值',
  'app.stock.position.check.nodebt': '无负债',
  'app.stock.position.check.tomax': '维持担保比取上限',

  // 两融持仓
  'app.stock.cdposition.openingdate': '开仓日期',
  'app.stock.cdposition.closingdate': '合约了结日期',
  'app.stock.cdposition.expdate': '合约结束日期',
  'app.stock.cdposition.orderno': '委托序号',
  'app.stock.cdposition.orderid': '委托编号',
  'app.stock.cdposition.side': '交易行为',
  'app.stock.cdposition.contqty': '合约数量',
  'app.stock.cdposition.contamt': '合约金额',
  'app.stock.cdposition.contfee': '合约费用',
  'app.stock.cdposition.contintrate': '合约利率',
  'app.stock.cdposition.contintblnaccu': '合约利息积数',
  'app.stock.cdposition.contint': '合约利息',
  'app.stock.cdposition.repaidqty': '已还数量',
  'app.stock.cdposition.repaidamt': '已还金额',
  'app.stock.cdposition.repaidint': '已还利息',
  'app.stock.cdposition.rltrepaidqty': '当日已还数量',
  'app.stock.cdposition.rltrepaidamt': '当日已还金额',
  'app.stock.cdposition.rltrepaidint': '当日已还利息',
  'app.stock.cdposition.contremainamt': '未还金额',
  'app.stock.cdposition.contremainqty': '未还数量',
  'app.stock.cdposition.pendrepayint': '未还利息',
  'app.stock.cdposition.freeint': '合约罚息',
  'app.stock.cdposition.debtamt': '负债金额',
  'app.stock.cdposition.marginratio': '保证金比例',
  'app.stock.cdposition.marginamt': '占用保证金',
  'app.stock.cdposition.rltrepaidcommi': '当日已还手续费',
  // 'app.stock.cdposition.tmpstr': '',
  'app.stock.cdposition.allamt': '合约负债',

  // 信用额度
  'app.stock.common.deposit': '转入',
  'app.stock.common.withdraw': '转出',
  'app.stock.common.account': '金额',
  'app.stock.common.number': '数量',
  'app.stock.credit.rz': '融资信用额度',
  'app.stock.credit.rq': '融券信用额度',
  'app.stock.credit.precredit': '系统设置授信额度',
  'app.stock.credit.availcredit': '可用授信额度',
  'app.stock.credit.usedcredit': '已使用授信额度',
  'app.stock.credit.frzcredit': '冻结授信额度',
  'app.stock.credit.type': '操作类型',

  // 资金头寸
  'app.stock.cashaccount.type': '头寸性质',
  'app.stock.cashaccount.prebalance': '总授权资金头寸',
  'app.stock.cashaccount.availused': '可用头寸',
  'app.stock.cashaccount.money': '资金占用',
  'app.stock.cashaccount.commi': '手续费占用',
  'app.stock.cashaccount.frozenmoney': '冻结资金',
  'app.stock.cashaccount.frozencommi': '冻结手续费',
  'app.stock.cashaccount.interest': '利息占用',
  'app.stock.cashaccount.preavailused': '日初可用头寸',

  // 股份头寸
  'app.stock.cashstock.preposition': '总授权数量',
  'app.stock.cashstock.availused': '可用数量',
  'app.stock.cashstock.position': '占用数量',
  'app.stock.cashstock.frozenvolume': '冻结数量',

  // 额度头寸分配
  'app.stock.assign.details': '分配详情',

  // 还款详情
  'app.stock.repay.orderid': '还款合同编号',
  'app.stock.repay.type': '偿还类型',
  'app.stock.repay.contrddate': '合约交易日期',
  // 'app.stock.repay.conorderid': '合约合同编号',
  'app.stock.repay.orderno': '合约委托编号',
  'app.stock.repay.firstmatched': '首次成交时间',
  // 'app.stock.repay.rltrepayamt': '实时偿还本金及费用',
  // 'app.stock.repay.rltrepayint': '实时偿还利息',
  'app.stock.repay.rltrepay': '发生金额',
  'app.stock.repay.debtstype': '归还类型',
  'app.stock.repay.time': '偿还时间',

  // 公允价
  'app.stock.instr.fairprice': '公允价',
  'app.stock.instr.price': '当前价格',
  'app.stock.instr.lastprice': '收盘价格',
  'app.stock.instr.fairratio': '公允价格比例',

  // 交易所集中度维保控制参数
  'app.stock.exconctparam.title': '交易所集中度维保控制参数',
  'app.stock.exconctparam.title1': '交易所集中度控制',
  'app.stock.exconctparam.title2': '现金证券划转控制',
  'app.stock.exconctparam.trigdbratio': '担保物比例触发线',
  'app.stock.exconctparam.ctrlconcratio': '集中度控制线',
  'app.stock.exconctparam.ctrlmaintratio': '维保控制线',
  'app.stock.exconctparam.ctrltranfmratio': '维保控制线',
  'app.stock.exconctparam.ctrltranfcratio': '集中度控制线',
  'app.stock.exconctparam.text': '交易所持仓集中度控制：',
  'app.stock.exconctparam.parag1': '单一股票担保物比例在 {trigDbratio}% 以上且该股票静态市盈率在 300 倍以上或者为负数的，对于该股票的客户担保物集中度在 {ctrlConcratio}% 以上且维持担保比例在 {ctrlMaintratio}% 以下的客户，会员应当暂停接受其融资买入该股票的委托，并可以采取其他风险控制措施。',
  'app.stock.exconctparam.parag2': '客户提取信用账户内现金或证券时，需满足提取前后维持担保比例均不低于 {ctrlTranfmratio}% ，提取证券还应满足特殊组别板块集中度不超过 {ctrlTranfcratio}% 。',
};
