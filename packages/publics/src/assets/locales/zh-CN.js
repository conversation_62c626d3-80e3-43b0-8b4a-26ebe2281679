import general from './zh-CN/general';
import auth from './zh-CN/auth';
import options from './zh-CN/options';
import future from './zh-CN/future';
import stock from './zh-CN/stock';
import exception from './zh-CN/exception';
import menu from './zh-CN/menu';
import dict from './zh-CN/dict';

import stockPatch from './zh-CN/stock-patch';

let zhCN = {
  'navbar.lang': '简体中文',
  ...general,
  ...auth,
  ...options,
  ...future,
  ...stock,
  ...exception,
  ...menu,
  ...dict,
};

if (Number(import.meta.env.VITE_PUBLIC_SYSTEM_TYPE) === 2) {
  zhCN = {
    ...zhCN,
    ...stockPatch,
  };
}

export default zhCN;
