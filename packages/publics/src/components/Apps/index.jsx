import { useRef, useState, useMemo } from 'react';
import { Popover } from 'antd';
import { AppstoreOutlined } from '@ant-design/icons';

const Apps = props => {
  const {
    appList,
    appListRender,
    onItemClick: itemClick,
  } = props;

  const ref = useRef(null);
  const popoverRef = useRef(null);

  const [open, setOpen] = useState(false);

  const cloneItemClick = app => itemClick?.(app, popoverRef);
  const defaultDomContent = useMemo(() => {
    <AppContent
      appList={appList}
      itemClick={itemClick ? cloneItemClick : undefined}
    />
  }, [appList]);

  return (<>
    <div
      ref={ref}
      onClick={e => {
        e.stopPropagation();
        e.preventDefault();
      }}
    />
    <Popover
      placement="bottomRight"
      trigger={['click']}
      zIndex={9999}
      {...popoverOpenProps}
      overlayClassName={styles.popover}
      content={popoverContent}
      getPopupContainer={() => ref.current || document.body}
    >
      <span
        ref={popoverRef}
        onClick={e => e.stopPropagation()}
        className={cx(
          [styles.icon],
          { [styles.iconActive]: open },
        )}
      ><AppstoreOutlined /></span>
    </Popover>
  </>);
}

export { Apps };
