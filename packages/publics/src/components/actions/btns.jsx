import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ClearOutlined,
  ImportOutlined,
  ExportOutlined,
  SwapOutlined,
  ExpandAltOutlined,
} from '@ant-design/icons';

// default | primary | danger
// PresetColors = blue | purple | cyan | green | magenta | pink | red | orange | yellow | volcano | geekblue | lime | gold

export const insertBtn = funcs => ({
  id: 'app.general.insert',
  name: '新增',
  icon: <PlusOutlined />,
  style: { color: 'primary', variant: 'solid', },
  onClick: () => funcs()
});

export const updateBtn = (funcs, data, keys) => ({
  id: 'app.general.update',
  name: '修改',
  icon: <EditOutlined />,
  disabled: keys.length === 1 ? false : true,
  style: { color: 'primary', variant: 'outlined', },
  onClick: () => {
    const record = data.find(item => item.id === keys[0]);
    funcs(record);
  }
});

export const deleteBtn = (funcs, data, keys) => ({
  id: 'app.general.delete',
  name: '删除',
  icon: <DeleteOutlined />,
  disabled: keys.length > 0 ? false : true,
  style: { color: 'danger', variant: 'solid', },
  onClick: () => {
    const records = [...data].filter(item => keys.some(k => k === item.id));
    funcs(records);
  }
});

export const emptyBtn = funcs => ({
  id: 'app.general.empty',
  name: '清空',
  icon: <ClearOutlined />,
  style: { color: 'danger', variant: 'solid', },
  onClick: () => funcs()
});

export const importBtn = funcs => ({
  id: 'app.general.import',
  name: '导入',
  icon: <ImportOutlined />,
  style: { color: 'cyan', variant: 'outlined', },
  onClick: () => funcs()
});

export const exportBtn = funcs => ({
  id: 'app.general.export',
  name: '导出',
  icon: <ExportOutlined />,
  style: { color: 'warning', variant: 'outlined', },
  onClick: () => funcs()
});

export const exportAllBtn = funcs => ({
  id: 'app.general.exportAll',
  name: '导出全部',
  icon: <ExportOutlined />,
  style: { color: 'orange', variant: 'outlined', },
  onClick: () => funcs()
});

export const depositBtn = (funcs, data, keys) => ({
  id: 'app.general.deposit',
  name: '入金',
  icon: <ImportOutlined />,
  disabled: keys.length === 1 ? false : true,
  style: { color: 'cyan', variant: 'solid', },
  onClick: () => {
    const record = data.find(item => item.id === keys[0]);
    funcs(record);
  }
});

export const withdrawBtn = (funcs, data, keys) => ({
  id: 'app.general.withdraw',
  name: '出金',
  icon: <ExportOutlined />,
  disabled: keys.length === 1 ? false : true,
  style: { color: 'orange', variant: 'solid', },
  onClick: () => {
    const record = data.find(item => item.id === keys[0]);
    funcs(record);
  }
});

export const transferBtn = funcs => ({
  id: 'app.general.transfer',
  name: '资金划转',
  icon: <SwapOutlined />,
  style: { color: 'primary', variant: 'solid', },
  onClick: () => funcs()
});

export const shareBtn = funcs => ({
  id: 'app.menu.fund.share',
  name: '股份划拨',
  icon: <SwapOutlined />,
  style: { type: 'primary' },
  onClick: () => funcs()
});

export const expandBtn = (funcs, ifExpand) => ({
  id: ifExpand ? 'app.general.fold' : 'app.general.expand',
  name: ifExpand ? '折叠' : '展开',
  icon: <ExpandAltOutlined />,
  style: { color: 'primary', variant: 'outlined', },
  onClick: () => funcs()
});
