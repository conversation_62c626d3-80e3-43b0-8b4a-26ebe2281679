import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
  CloseOutlined,
  SearchOutlined,
  ImportOutlined,
  ExportOutlined,
  ToolOutlined,
  LogoutOutlined,
  ZoomInOutlined,
  SwapOutlined,
  ArrowRightOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';

export const insertLink = (funcs, record) => ({
  id: 'app.general.insert',
  name: '新增',
  icon: <PlusOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});

export const updateLink = (funcs, record) => ({
  id: 'app.general.update',
  name: '修改',
  icon: <EditOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});

export const deleteLink = (funcs, record) => ({
  id: 'app.general.delete',
  name: '删除',
  icon: <DeleteOutlined />,
  style: { color: 'danger' },
  onClick: () => funcs(record)
});

export const resetPwdLink = (funcs, record) => ({
  id: 'app.general.resetpwd',
  name: '重置密码',
  icon: <KeyOutlined />,
  style: { color: 'cyan' },
  onClick: () => funcs(record)
});

export const cancelLink = (funcs, record) => ({
  id: 'app.general.cancel',
  name: '取消',
  icon: <CloseOutlined />,
  style: { color: 'orange' },
  onClick: () => funcs(record)
});

export const detailLink = (funcs, record) => ({
  id: 'app.general.detail',
  name: '详情',
  icon: <SearchOutlined />,
  style: { color: 'cyan' },
  onClick: () => funcs(record)
});

export const depositLink = (funcs, record) => ({
  id: 'app.general.deposit',
  name: '入金',
  icon: <ImportOutlined />,
  style: { color: 'cyan' },
  onClick: () => funcs(record)
});

export const withdrawLink = (funcs, record) => ({
  id: 'app.general.withdraw',
  name: '出金',
  icon: <ExportOutlined />,
  style: { color: 'orange' },
  onClick: () => funcs(record)
});

export const chgStatusLink = (funcs, record) => ({
  id: 'app.general.chgstatus',
  name: '更改状态',
  icon: <ToolOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});

export const rtnLink = (funcs, record) => ({
  id: 'app.menu.mem.rtn.rtnorder',
  name: '委托回报',
  icon: <SearchOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});

export const infoLink = (funcs, record) => ({
  id: 'app.menu.mem.rtn.rtntrade',
  name: '成交回报',
  icon: <SearchOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});

export const offlineLink = (funcs, record) => ({
  id: 'app.general.offline',
  name: '离线',
  icon: <LogoutOutlined />,
  style: { color: 'danger' },
  onClick: () => funcs(record)
});

export const showCalcLink = (funcs, record) =>({
  id: 'app.general.showcalc',
  name: '保证金核对',
  icon: <ZoomInOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});

export const transferLink = (funcs, record) =>({
  id: 'app.general.transfer',
  name: '资金划转',
  icon: <SwapOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});

export const continueLink = (funcs, record) => ({
  id: 'app.general.continue',
  name: '继续划转',
  icon: <ArrowRightOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});

export const assignUsersLink = (funcs, record) => ({
  id: 'app.general.assignuser',
  name: '分配用户',
  icon: <UsergroupAddOutlined />,
  style: { color: 'primary' },
  onClick: () => funcs(record)
});
