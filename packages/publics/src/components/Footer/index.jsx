import { Fragment } from 'react';
import { Layout } from 'antd';

import GlobalFooter from '../GlobalFooter';
import Copyright from './Copyright';

const { Footer } = Layout;

const DefaultFooter = ({
  links,
  copyright,
  style,
  propsFormatMessage,
}) => (
  <Footer
    style={{
      padding: 0,
      zIndex: 1,
      backgroundColor: 'transparent',
      ...style
    }}
  >
    <GlobalFooter
      links={links}
      copyright={
        copyright === false ? null : (
          <Fragment>
            <Copyright formatMessage={propsFormatMessage} /> {copyright}
          </Fragment>
        )
      }
    />
  </Footer>
);

export default DefaultFooter;
