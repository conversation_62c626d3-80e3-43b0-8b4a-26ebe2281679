import { Link } from 'react-router';
import { Typography } from 'antd';
import { CopyrightOutlined } from '@ant-design/icons';

const Copyright = ({ formatMessage }) => {
  const createYear = 2022;
  const currentYear = new Date().getFullYear();
  const companyName = formatMessage('app.company.name');

  return (<>
    <CopyrightOutlined />
    {currentYear === createYear ? ` ${currentYear} ` : ` ${createYear} - ${currentYear} `}
    {formatMessage('app.company.copyright', { name: <Link to="/" component={Typography.Link}>{companyName}</Link> })}
  </>);
}

export default Copyright;
