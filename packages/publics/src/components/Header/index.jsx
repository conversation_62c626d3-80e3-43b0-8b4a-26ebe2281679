import { useCallback } from 'react';
import { Layout } from 'antd';
import { ThemeProvider } from 'antd-style';

import GlobalHeader from '../GlobalHeader';
import TopNavHeader from '../TopNavHeader';

import { clearMenuItem } from '@titd/publics/components';

import useStyles from './style';

const { Header } = Layout;

const DefaultHeader = props => {
  const {
    isMobile,
    fixedHeader,
    className: propsClassName,
    style,
    collapsed,
    onCollapse,
    layout,
    headerRender,
    headerContentRender,
  } = props;

  const { styles, cx } = useStyles();

  const isTop = layout === 'top';
  const needFixedHeader = fixedHeader || layout === 'mix';

  const renderContent = useCallback(() => {
    const clearMenuData = clearMenuItem(props.menuData || []);

    let defaultDom = (
      <GlobalHeader
        onCollapse={onCollapse}
        {...props}
        menuData={clearMenuData}
      >
        {headerContentRender && headerContentRender(props, null)}
      </GlobalHeader>
    );
    if (isTop && !isMobile) {
      defaultDom = (
        <TopNavHeader
          mode="horizontal"
          onCollapse={onCollapse}
          {...props}
          menuData={clearMenuData}
        />
      );
    }
    if (headerRender && typeof headerRender === 'function') {
      return headerRender(props, defaultDom);
    }
    return defaultDom;
  }, [headerContentRender, headerRender, isMobile, isTop, onCollapse, props]);

  const classNames = cx(
    propsClassName,
    { [styles.headerFixed]: needFixedHeader },
    // { [styles.headerFixedScroll]: isFixedHeaderScroll },
    { [styles.headerMix]: layout === 'mix' },
    { [styles.headerFixedAction]: !collapsed },
    { [styles.headerTopMenu]: isTop },
    styles.header,
  );

  // 隐藏
  if (layout === 'side' && !isMobile) return null;

  return (
    <ThemeProvider
      theme={{
        components: {
          Layout: {
            headerBg: 'transparent',
            bodyBg: 'transparent',
          },
        },
      }}
    >
      {needFixedHeader && (
        <Header className={styles.headerHolder} style={style} />
      )}
      <Header className={classNames} style={style}>
        {renderContent()}
      </Header>
    </ThemeProvider>
  );
}

export default DefaultHeader;
