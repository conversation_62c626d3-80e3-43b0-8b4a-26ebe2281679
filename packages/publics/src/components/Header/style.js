import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  header: css`
    height: ${token.header?.heightLayoutHeader || 56}px;
    line-height: ${token.header?.heightLayoutHeader || 56}px;
    width: 100%;
    padding-block: 0px;
    padding-inline: 0px;
    border-block-end: 1px solid ${token.colorSplit};
    background-color: ${token.header?.colorBgHeader || rgba(255, 255, 255, 0.4)};
    z-index: 19;
    backdrop-filter: blur(8px);
    transition: background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  `,
  headerFixed: css`
    position: fixed;
    inset-block-start: 0;
    inset-inline-end: 0;
    width: 100%;
    z-index: 100;
  `,
  headerFixedScroll: css`
    background-color: ${token.header.colorBgScrollHeader || rgba(255, 255, 255, 0.8)};
  `,
  headerMix: css``,
  headerFixedAction: css``,
  headerTopMenu: css``,
  headerHolder: css `
    height: ${token.header.heightLayoutHeader}px;
    line-height: ${token.header.heightLayoutHeader}px;
    background-color: transparent;
    z-index: 19;
  `,
  headerActions: css`
    display: flex;
    align-items: center;
    font-size: 16px;
    cursor: pointer;
  `,
  headerActionsItem: css`
    padding-block: 0px;
    padding-inline: 8px;

    &:hover {
      color: ${token.colorText};
    }
  `,
}));

export default useStyles;
