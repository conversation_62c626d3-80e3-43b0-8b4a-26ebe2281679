import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css, prefixCls }) => ({
  tiTableContent: css`
    margin-top: 16px;
    .${prefixCls}-table-pagination.${prefixCls}-pagination {
      margin-bottom: 0;
    }
  `,
  tiTableSearch: css`
    .${prefixCls}-card-body {
      padding-bottom: 0;
    }
  `,
  tiHasBorder: css`
    border-bottom: 1px solid #eee;
    margin-bottom: 16px;
  `,
}));

export default useStyles;
