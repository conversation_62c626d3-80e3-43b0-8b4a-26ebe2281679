import { Card } from 'antd';

import { QueryFilter } from '../form';
import { TiTable } from '../Table';
import { HeadTitle } from '../widgets';

import useStyles from './style';

const PageContent = props => {
  const {
    title,
    combine = false,
    filterProps,
    tableProps,
  } = props;

  const { styles } = useStyles();

  return (<>
    <HeadTitle title={title} />

    {combine ? (
      <Card className={styles.tiTableContent}>
        <div className={styles.tiHasBorder}>
          <QueryFilter { ...filterProps } />
        </div>

        <TiTable { ...tableProps } />
      </Card>
      ) : (<>
      <Card className={styles.tiTableSearch}>
        <QueryFilter { ...filterProps } />
      </Card>

      <Card className={styles.tiTableContent}>
        <TiTable { ...tableProps } />
      </Card>
    </>)}
  </>);
}

const PageWithOther = props => {
  const {
    title,
    filterProps,
    tableProps,
    children
  } = props;

  const { styles } = useStyles();

  return <>
    <HeadTitle title={title} />

    <Card className={styles.tiTableSearch}>
      <QueryFilter { ...filterProps } />
    </Card>

    {children}

    <Card className={styles.tiTableContent}>
      <TiTable { ...tableProps } />
    </Card>
  </>;
}

export { PageContent, PageWithOther };
