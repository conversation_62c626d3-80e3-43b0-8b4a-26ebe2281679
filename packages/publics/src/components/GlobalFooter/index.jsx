import { Typography } from 'antd';

import useStyles from './style';

const { Link } = Typography;

const GlobalFooter = ({
  links,
  copyright,
  style,
}) => {
  const { styles } = useStyles();

  if ((links == null || links === false || (Array.isArray(links) && links.length === 0)) && (copyright == null || copyright === false)) {
    return null;
  }

  return (
    <div className={styles.footer} style={style}>
      {links && (
        <div className={styles.list}>
          {links.map(link => (
            <Link
              className={styles.listLink}
              key={link.key}
              title={link.key}
              target={link.blankTarget ? '_blank' : '_self'}
              href={link.href}
              rel="noreferrer"
            >{link.title}</Link>
          ))}
        </div>
      )}
      {copyright && (
        <div className={styles.copyright}>
          {copyright}
        </div>
      )}
    </div>
  )
}

export default GlobalFooter;
