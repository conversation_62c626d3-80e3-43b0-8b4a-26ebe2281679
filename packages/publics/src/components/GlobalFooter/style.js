import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  footer: css`
    margin-block: 0;
    margin-block-start: 24px;
    margin-block-end: 12px;
    margin-inline: 0;
    padding-block: 0;
    padding-inline: 16px;
    text-align: center;
  `,
  list: css`
    margin-block-end: 8px;
    color: ${token.colorTextSecondary};

    *:not(:last-child) {
      margin-inline-end: 8px;
    }
    &:hover {
      color: ${token.colorPrimary};
    }
  `,
  listLink: css`
    color: ${token.colorTextSecondary};
    text-decoration: ${token.linkDecoration};
  `,
  copyright: css`
    font-size: 14px;
    color: ${token.colorText};
  `,
}));

export default useStyles;
