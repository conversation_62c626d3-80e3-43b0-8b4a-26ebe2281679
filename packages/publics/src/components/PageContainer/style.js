import { createStyles } from 'antd-style';

const [sm, md, lg, xl] = [576, 768, 992, 1200].map(bp => `@media (min-width: ${bp}px)`);

export const useStyles = createStyles(({ token, css, prefixCls, cx }) => {
  const wrap = cx(css``);

  return {
    pageContainer: css`
      position: relative;
    `,
    childrenContainer: css`
      padding-block-start: 0;
      padding-block-end: ${token.pageContainer?.paddingBlockPageContainerContent}px;
      padding-inline: ${token.pageContainer?.paddingInlinePageContainerContent}px;
    `,
    childrenNoHeader: css`
      padding-block-start: ${token.pageContainer?.paddingBlockPageContainerContent}px;
    `,
    affix: css`
      .${prefixCls}-affix {
        .${wrap} {
          background-color: ${token.pageContainer?.colorBgPageContainerFixed};
          transition: 'background-color 0.3s';
          box-shadow: '0 2px 8px #f0f1f2';
        }
      }
    `,
    warpPageHeader: css`
      padding-block-start: ${(token.pageContainer?.paddingBlockPageContainerContent ?? 40) / 4}px;
      padding-block-end: ${(token.pageContainer?.paddingBlockPageContainerContent ?? 40) / 2}px;
      padding-inline-start: ${token.pageContainer?.paddingInlinePageContainerContent}px;
      padding-inline-end: ${token.pageContainer?.paddingInlinePageContainerContent}px;
    `,
    headerDetail: css`
      display: flex;
      [${sm}] {
        display: block;
      }
    `,
    headerMain: css`
      width: 100%;
    `,
    headerRow: css`
      display: flex;
      width: 100%;
      [${sm}] {
        display: block;
      }
    `,
    headerContent: css`
      flex: auto;
      width: 100%;
    `,
    headerExtraContent: css`
      flex: 0 1 auto;
      min-width: 242px;
      margin-inline-start: 88px;
      text-align: end;
      [${xl}] {
        margin-inline-start: 44px;
      }
      [${lg}] {
        margin-inline-start: 20px;
      }
      [${md}] {
        margin-inline-start: 0;
        text-align: start;
      }
      [${sm}] {
        margin-inline-start: 0;
      }
    `,
  };
});
