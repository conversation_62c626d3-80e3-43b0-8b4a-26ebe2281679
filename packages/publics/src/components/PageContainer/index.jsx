import React, { useContext, useEffect, useMemo } from 'react';
import { Affix, Breadcrumb, Tabs, Watermark } from 'antd';

import { RouteContext } from '@titd/publics/context';
import { PageHeader } from '../PageHeader';
import { PageLoading } from '../PageLoading';

import { useStyles } from './style';

function genLoading(spinProps) {
  if (typeof spinProps === 'object') {
    return spinProps;
  }
  return { spinning: spinProps };
}

const renderFooter = ({
  tabList,
  tabActiveKey,
  onTabChange,
  tabBarExtraContent,
  tabProps,
  styles,
}) => {
  if (Array.isArray(tabList) || tabBarExtraContent) {
    return (
      <Tabs
        className={styles.tabs}
        activeKey={tabActiveKey}
        onChange={key => {
          if (onTabChange) {
            onTabChange(key);
          }
        }}
        tabBarExtraContent={tabBarExtraContent}
        items={tabList?.map((item, index) => ({
          label: item.tab,
          ...item,
          key: item.key?.toString() || index?.toString(),
        }))}
        {...tabProps}
      />
    );
  }
  return null;
};

const renderPageHeader = (content, extraContent, styles) => {
  if (!content && !extraContent) {
    return null;
  }
  return (
    <div className={styles.headerDetail}>
      <div className={styles.headerMain}>
        <div className={styles.headerRow}>
          {content && (
            <div className={styles.headerContent}>
              {content}
            </div>
          )}
          {extraContent && (
            <div className={styles.headerExtraContent}>
              {extraContent}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

const TiBreadcrumb = props => {
  const value = useContext(RouteContext);
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        height: '100%',
      }}
    >
      <Breadcrumb
        {...value?.breadcrumb}
        {...value?.breadcrumbProps}
        {...props}
      />
    </div>
  );
};

const memoRenderPageHeader = props => {
  const {
    title,
    content,
    pageHeaderRender,
    header,
    extraContent,
    // childContentStyle,
    // style,
    value,
    breadcrumbRender,
    styles,
    ...restProps
  } = props;

  const getBreadcrumbRender = () => {
    if (!breadcrumbRender) {
      return undefined;
    }
    return breadcrumbRender;
  };

  if (pageHeaderRender === false) {
    return null;
  }
  if (pageHeaderRender) {
    return pageHeaderRender({...props, ...value});
  }

  let pageHeaderTitle = title;
  if (!title && title === false) {
    pageHeaderTitle = value?.title;
  }
  const pageHeaderProps = {
    ...value,
    title: pageHeaderTitle,
    ...restProps,
    footer: renderFooter({
      ...restProps,
      breadcrumbRender,
      styles,
    }),
    ...header,
  };

  const { breadcrumb } = pageHeaderProps;
  const noHasBreadCrumb = (!breadcrumb || (!breadcrumb?.itemRender && !breadcrumb?.items?.length)) && !breadcrumbRender;

  if (['title', 'subtitle', 'extra', 'tags', 'footer', 'avatar', 'backIcon'].every(item => !pageHeaderProps[item]) && noHasBreadCrumb) {
    return null;
  }

  return (
    <PageHeader
      {...pageHeaderProps}
      className={styles.warpPageHeader}
      breadcrumb={
        breadcrumbRender === false
          ? undefined
          : {
            ...pageHeaderProps.breadcrumb,
            ...value.breadcrumbProps,
          }
      }
      breadcrumbRender={getBreadcrumbRender()}
    >
      {header?.children || renderPageHeader(content, extraContent, styles)}
    </PageHeader>
  );
};

const PageContainerBase = props => {
  const {
    children,
    loading = false,
    className,
    style,
    footer,
    affixProps,
    fixedHeader,
    breadcrumbRender,
    // footerToolBarProps,
    childrenContentStyle,
    ...restProps
  } = props;

  const { styles, cx, theme } = useStyles();

  const value = useContext(RouteContext);

  useEffect(() => {
    if (!value || !value?.setHasPageContainer) {
      return () => {};
    }
    value?.setHasPageContainer?.(num => num + 1);
    return () => {
      value?.setHasPageContainer?.(num => num - 1);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const memoBreadcrumbRender = useMemo(() => {
    if (breadcrumbRender === false) return false;
    return breadcrumbRender || restProps?.header?.breadcrumbRender;
  }, [breadcrumbRender, restProps?.header?.breadcrumbRender]);

  const pageHeaderDom = memoRenderPageHeader({
    ...restProps,
    breadcrumbRender: memoBreadcrumbRender,
    ghost: true,
    value,
    styles,
  });

  const loadingDom = useMemo(() => {
    if (React.isValidElement(loading)) {
      return loading;
    }
    if (typeof loading === 'boolean' && !loading) {
      return null;
    }

    const spinProps = genLoading(loading);
    return spinProps?.spinning ? <PageLoading {...spinProps} /> : null;
  }, [loading]);

  const content = useMemo(() => {
    return children ? (
      <div
        className={cx(
          styles.childrenContainer,
          !pageHeaderDom ? styles.childrenNoHeader : '',
        )}
        style={childrenContentStyle}
      >{children}</div>
    ) : null;
  }, [children, childrenContentStyle]);

  const renderContentDom = useMemo(() => {
    const dom = loadingDom || content;
    if (props.waterMarkProps || value.waterMarkProps) {
      const waterMarkProps = {
        ...props.waterMarkProps,
        ...value.waterMarkProps,
      };
      return <Watermark {...waterMarkProps}>{dom}</Watermark>;
    }
    return dom;
  }, [props.waterMarkProps, value.waterMarkProps, loadingDom, content]);

  const containerClassName = cx(
    styles.pageContainer,
    className,
    footer ? styles.withFooter : '',
    fixedHeader && pageHeaderDom ? styles.withAffix : '',
  );

  return (<>
    <div className={containerClassName} style={style}>
      {fixedHeader && pageHeaderDom ? (
        <Affix
          offsetTop={
            value.hasHeader && value.fixedHeader
              ? theme.layout?.header?.heightLayout
              : 1
          }
          {...affixProps}
          className={styles.affix}
        >
          <div className={styles.wrap}>{pageHeaderDom}</div>
        </Affix>
      ) : pageHeaderDom}
      {/* {renderContentDom && <GridContent>{renderContentDom}</GridContent>} */}
      {renderContentDom}
    </div>
    {/* {footer && (
      <FooterToolbar
        {...footerToolBarProps}
      >{footer}</FooterToolbar>
    )} */}
    {footer}
  </>);
};

const PageContainer = props => (
  // <ProConfigProvider needDeps>
  <PageContainerBase {...props} />
  // </ProConfigProvider>
);

const TiPageHeader = props => {
  const { styles } = useStyles();
  const value = useContext(RouteContext);
  return memoRenderPageHeader({
    ...props,
    ...value,
    styles,
  });
};

export { PageContainer, TiBreadcrumb, TiPageHeader };
