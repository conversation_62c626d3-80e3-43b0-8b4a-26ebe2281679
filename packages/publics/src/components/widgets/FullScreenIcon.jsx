import { useState } from 'react';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons';
import screenfull from 'screenfull';

import { ActionItem } from './ActionItem';

const FullScreenIcon = ({ propsFormatMessage: $t }) => {
  const [fullscreen, setFullscreen] = useState(false);

  const toggleFullscreen = () => {
    if (screenfull.isEnabled) {
      screenfull.toggle();

      setFullscreen(!screenfull.isFullscreen);
    }
  }

  return (
    ActionItem({
      title: $t(fullscreen ? 'app.header.fullscreen.quit' : 'app.header.fullscreen'),
      onClick: toggleFullscreen,
      children: fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />
    })
  );
}

export { FullScreenIcon };
