import { Typography, Image } from 'antd';

import { Logo, LogoCNW, LogoCN, LogoENW, LogoEN } from '@titd/publics/assets';

import useStyles from './style';

const { Link } = Typography;

const Branding = props => {
  const {
    isMobile,
    theme,
    lang = 'zh-CN',
    className,
  } = props;

  const { styles, cx } = useStyles();

  const logo = lang === 'en-US' ? [LogoEN, LogoENW] : [LogoCN, LogoCNW];
  const logoHeight = 28;

  return (
    <div className={cx(styles.logo, className)}>
      <Link href="/">
        <Image
          src={Logo}
          height={logoHeight}
          alt="Logo"
        />
        {isMobile ? '' : (
          <Image
            src={theme === 'dark' ? logo[1] : logo[0]}
            height={logoHeight}
            className={styles.title}
            alt="Logo Name"
          />
        )}
      </Link>
    </div>
  );
}

export { Branding };
