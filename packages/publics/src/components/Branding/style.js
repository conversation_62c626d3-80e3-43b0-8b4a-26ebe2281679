import { createStyles, css } from 'antd-style';

const useStyles = createStyles({
  logo: css`
    position: relative;
    transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

    > a {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 28px;
    }

    img {
      display: inline-block;
      vertical-align: middle;

      + img {
        margin-left: 3px;
      }
    }
  `,
  title: css`
    @keyframes title-hide {
      0% {
        display: none;
        opacity: 0;
      }
      80% {
        display: none;
        opacity: 0;
      }
      100% {
        display: unset;
        opacity: 1;
      }
    }
    animation: title-hide .3s;
  `
});

export default useStyles;
