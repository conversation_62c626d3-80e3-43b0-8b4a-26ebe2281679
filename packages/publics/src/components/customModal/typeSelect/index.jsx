import { Modal, Segmented, Avatar } from 'antd';
import { UserOutlined } from '@ant-design/icons';

import { dicts } from '@titd/publics/utils';

const { userTypeDict } = dicts;

const avaColor = ['#389e0d', '#d46b08'];

export const typeModal = onSelect => {
  let typeValue = 0;
  const userList = userTypeDict.map((item, idx) => ({
    label: (
      <div style={{ padding: 6 }}>
        <Avatar style={{ backgroundColor: avaColor[idx] }} icon={<UserOutlined />} />
        <div>{item}</div>
      </div>
    ),
    value: idx
  }));

  function onOk() {
    // console.log(typeValue);
    onSelect && onSelect(typeValue);
  }

  Modal.info({
    title: '请选择用户类型',
    content: (<div style={{
      paddingTop: '16px',
      textAlign: 'center',
    }}>
      <Segmented
        options={userList}
        onChange={(value) => typeValue = value}
      />
    </div>),
    closable: true,
    okText: '确定',
    onOk: onOk
  });
}
