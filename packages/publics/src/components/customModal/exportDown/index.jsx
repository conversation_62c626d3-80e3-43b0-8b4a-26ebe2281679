import { Modal, Result } from 'antd';

import { downloadLink } from '@titd/publics/utils';

export const exportDownModal = path => {
  Modal.info({
    icon: undefined,
    closable: true,
    // title: '导出文件生成',
    content: () => {
      const pathGroup = path.split('/');

      return (
        <Result
          status="success"
          title="成功生成文件"
          subTitle={pathGroup[pathGroup.length - 1]}
        />
      );
    },
    okText: '下载',
    onOk: () => downloadLink(path),
  });
}
