import { useState } from 'react';
import { Modal, Typography } from 'antd';
import { useMount } from 'ahooks';

import { TableSimple, TableFields } from '@titd/publics/components';

import { calcFunc } from './tools';

const { Title, Text } = Typography;
const { NegaNumber } = TableFields;

// eslint-disable-next-line react-refresh/only-export-components
const MarginCalc = props => {
  const {
    $t,
    respData,
  } = props;

  const [dataSource, setDataSource] = useState([]);

  const dataColumns = [{
    title: $t('app.options.mem.margincalc.instrumentname'),
    dataIndex: 'instrumentname',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.instrumentid}]`}
    </>,
  }, {
    title: $t('app.options.mem.margincalc.exerciseprice'),
    dataIndex: 'exerciseprice',
    align: 'right',
  }, {
    title: $t('app.options.mem.margincalc.lastprice'),
    dataIndex: 'lastprice',
    align: 'right',
  }, {
    title: $t('app.options.mem.margincalc.stockprice'),
    dataIndex: 'stockprice',
    align: 'right',
  }, {
    title: $t('app.options.instrument.volumemultiple'),
    dataIndex: 'pricemultiple',
    align: 'right',
  }, {
    title: $t('app.options.mem.margincalc.outmoney'),
    dataIndex: 'outmoney',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: $t('app.options.mem.margincalc.number'),
    dataIndex: 'number',
    align: 'right',
  }, {
    title: $t('app.options.mem.commimargin.marginrate'),
    dataIndex: 'marginrate',
    align: 'right',
  }];

  const calcColumns = [{
    title: $t('app.options.mem.margincalc.header'),
    children: [{
      title: $t('app.options.mem.margincalc.call.marginratioparam1'),
      dataIndex: 'marginratioparam1',
      align: 'center',
      render: (text, record) => calcFunc(text, record.value1),
    }, {
      title: $t('app.options.mem.margincalc.call.marginratioparam2'),
      dataIndex: 'marginratioparam2',
      align: 'center',
      render: (text, record) => calcFunc(text, record.value2),
    }]
  }, {
    title: $t('app.options.mem.margincalc.margin'),
    dataIndex: 'margin',
    align: 'right',
    render: text => NegaNumber(text, 2)
  }];

  useMount(() => setMarginData());

  const setMarginData = () => {
    setDataSource([{
      ...respData,
      id: 'calc',
      number: 1,
      value1: respData.marginratioparam1 / 100 * respData.stockprice - respData.outmoney,
      value2: respData.marginratioparam2 / 100 * respData.stockprice,
    }]);
  }

  return (<>
    <SimpleTable
      columns={dataColumns}
      dataSource={dataSource}
      title={() => <Title level={4} style={{ textAlign: 'center' }}>{$t('app.options.mem.margincalc.call')}</Title>}
    />

    <div style={{ marginTop: '20px' }}>
      <SimpleTable
        columns={calcColumns}
        dataSource={dataSource}
        footer={() => $t('app.options.mem.margincalc.call.tip')}
      />
    </div>
  </>);
}

export const callOptionModal = props => {
  Modal.info({
    icon: undefined,
    width: 1050,
    content: (
      <MarginCalc { ...props } />
    ),
    okText: '关闭',
  });
}
