import { Row, Col, Typography } from 'antd';

import { TableFields } from '@titd/publics/components';
import { currency } from '@titd/publics/utils';

const { Text } = Typography;
const { NegaNumber } = TableFields;
const { getFloat } = currency;

export const calcFunc = (label, value, sign = '%') => (<Row>
  <Col span={12} style={{ borderRight: '1px solid #ddd' }}>{label && getFloat(label, 4) + sign}</Col>
  <Col span={12} style={{ textAlign: 'right' }}>{
    isNaN(value) ? '' : (
      <Text type={value < 0 ? 'danger' : ''}>{NegaNumber(value, 6)}</Text>
    )
  }</Col>
</Row>);
