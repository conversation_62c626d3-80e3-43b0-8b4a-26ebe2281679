import { useMemo } from 'react';
import { Flex } from 'antd';
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';

import { Branding, clearMenuItem } from '@titd/publics/components';

import TopNavHeader from '../TopNavHeader';
import ActionsContent from './ActionsContent';

import useStyles from './style';

const GlobalHeader = props => {
  const {
    isMobile,
    collapsed,
    onCollapse,
    layout,
    splitMenus,
    menuData,
    rightContentRender,
    children,
  } = props;

  const { styles } = useStyles();

  if (layout === 'mix' && !isMobile && splitMenus) {
    const noChildrenMenuData = (menuData || []).map(item => ({
      ...item,
      children: undefined,
    }));
    const clearMenuData = clearMenuItem(noChildrenMenuData);

    return (
      <TopNavHeader
        mode="horizontal"
        {...props}
        splitMenus={false}
        menuData={clearMenuData}
      />
    );
  }

  return (
    <Flex
      className={styles.headerContainer}
      gap="small"
      justify="start"
      align="center"
    >
      {isMobile && (
        <span className={styles.collapsedBtn} onClick={() => onCollapse?.(!collapsed)}>
          {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </span>
      )}
      <Branding {...props} />
      <div style={{ flex: 1 }}>{children}</div>
      {(rightContentRender || props.actionsArray || props.avatarProps) && (
        <ActionsContent rightContentRender={rightContentRender} {...props} />
      )}
    </Flex>
  );
}

export default GlobalHeader;
