import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  actionsAvatar: css`
    height: 44px;
    color: ${token.colorTextSecondary};
    padding-inline: ${token.paddingXS}px;
    padding-block: ${token.paddingXS}px;
    cursor: pointer;
    display: flex;
    align-items: center;
    line-height: 44px;
    border-radius: ${token.borderRadius}px;

    &:hover {
      background-color: ${token.colorBgTextHover};
    }
  `,
  dropMenuContent: css``,
  unknowName: css`
    color: ${token.colorTextSecondary}
  `
}));

export default useStyles;
