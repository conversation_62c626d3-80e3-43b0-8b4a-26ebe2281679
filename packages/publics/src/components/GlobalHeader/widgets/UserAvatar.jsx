import { Dropdown, Avatar } from 'antd';

import { dicts } from '@titd/publics/utils';

import useStyles from './avatarStyle';

const { tagNormalColors } = dicts;

const UserAvatar = props => {
  const {
    isMobile,
    userInfo,
    dropMenu,
    menuClick,
    propsFormatMessage: $t,
  } = props;

  const { styles } = useStyles();

  // const { data: role } = useRequest();
  const avatarDom = userInfo ? (
    <div className={styles.actionsAvatar}>
      {!isMobile && <Avatar key="avatar" style={{ backgroundColor: `var(--ant-${tagNormalColors[userInfo.role - 1]})` }}>{userInfo.rolename?.slice(0, 2) || userInfo.role}</Avatar>}
      <span key="name" style={{ marginInlineStart: 8, whiteSpace: 'nowrap' }}>{userInfo.name || userInfo.id}</span>
    </div>
  ) : undefined;

  return (<>
    {avatarDom ? (
      dropMenu?.length > 0 ? (
        <Dropdown
          className={styles.dropMenuContent}
          menu={{
            items: dropMenu,
            onClick: menuClick,
          }}
          placement="bottomRight"
        >{avatarDom}</Dropdown>
      ) : (
        avatarDom
      )
    ) : (
      <span className={styles.unknowName}>
        {$t('header.user.unknown')}
      </span>
    )}
  </>);
}

export default UserAvatar;
