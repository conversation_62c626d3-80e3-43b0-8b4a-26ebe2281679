import { Flex } from 'antd';

import { ActionItem, FullScreenIcon } from '@titd/publics/components';
import UserAvatar from './widgets/UserAvatar';

import useStyles from './actionStyle';

const ActionsContent = props => {
  const {
    rightContentRender,
    actionsArray,
    avatarProps,
  } = props;

  const { styles } = useStyles();

  const rightActionsRender = actionsArray || avatarProps ? () => {
    return (<>
      {actionsArray?.length > 0 ? actionsArray.map((dom, index) => (
        <ActionItem key={index} {...dom} >{dom.children}</ActionItem>
      )) : null}
      <FullScreenIcon {...props} />
      {avatarProps ? (
        <UserAvatar {...props} {...avatarProps} />
      ) : null}
    </>);
  } : undefined;

  const contentRender = rightActionsRender || rightContentRender;
  return (
    <div className={styles.headerActions}>
      {contentRender ? (
        <Flex> {contentRender({ ...props })}</Flex>
      ) : null}
    </div>
  );
}

export default ActionsContent;
