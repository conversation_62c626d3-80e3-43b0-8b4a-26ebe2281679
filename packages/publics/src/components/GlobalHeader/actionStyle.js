import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  headerActions: css`
    height: auto;
  `,
  headerActionsAvatar: css`
    display: inline-block;
    align-items: center;
    justify-content: center;
    padding-inline-start: ${token.padding}px;
    padding-inline-end: ${token.padding}px;
    cursor: pointer;
    color: ${token.colorTextSecondary};

    > div {
      height: 44px;
      color: ${token.colorTextSecondary};
      padding-inline: 8;
      padding-block: 8;
      cursor: pointer;
      display: flex;
      align-items: center;
      line-height: 44px;
      border-radius: ${token.borderRadius}px;

      &:hover {
        background-color: ${token.colorBgTextHover};
      }
    }
  `
}));

export default useStyles;
