import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  headerContainer: css`
    position: relative;
    background: transparent;
    margin-block: 0;
    margin-inline: 16px;
    height: ${token.header?.heightLayoutHeader || 56}px;
    box-sizing: border-box;

    & > a {
      height: 100%;
    }
  `,
  collapsedBtn: css`
    min-height: 22px;
    color: ${token.header?.colorHeaderTitle};
    font-size: 18px;
    margin-inline-end: 16px;
  `,
}));

export default useStyles;
