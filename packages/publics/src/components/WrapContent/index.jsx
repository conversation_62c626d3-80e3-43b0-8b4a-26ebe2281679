import { Layout } from 'antd';

import { ErrorBoundary } from '@titd/publics/pages';

import useStyles from './style';

const WrapContent = props => {
  const {
    style,
    children,
    hasPageContainer = 0,
  } = props;

  const { styles, cx } = useStyles();

  const contentClassName = cx(
    styles.content,
    props.hasHeader ? styles.contentHasHeader : '',
    hasPageContainer > 0 ? styles.contentHasPageContianer : '',
  );

  const ErrorComponent = props.ErrorBoundary || ErrorBoundary;
  return props.ErrorBoundary === false ? (
    <Layout.Content className={contentClassName} style={style}>{children}</Layout.Content>
  ) : (
    <ErrorComponent>
      <Layout.Content className={contentClassName} style={style}>{children}</Layout.Content>
    </ErrorComponent>
  );
}

export { WrapContent };
