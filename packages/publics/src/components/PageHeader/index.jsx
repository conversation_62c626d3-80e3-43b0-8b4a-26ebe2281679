import { useState } from 'react';
import { Avatar, Breadcrumb, Space } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';
import ResizeObserver from 'rc-resize-observer';

import { useStyles } from './style';

const renderBack = (styles, backIcon, onBack) => {
  if (!backIcon || !onBack) return null;

  return (
    <div className={styles.back}>
      <div
        role="button"
        onClick={e => onBack?.(e)}
        className={styles.backBtn}
        aria-label="back"
      >{backIcon}</div>
    </div>
  );
}

const renderBreadcrumb = (styles, breadcrumb) => {
  if (!breadcrumb.item?.length) return null;

  return (
    <Breadcrumb
      {...breadcrumb}
      className={styles.breadcrumb}
    />
  );
}

const getBackIcon = props => {
  if (props.backIcon !== undefined) {
    return props.backIcon;
  }

  return <ArrowRightOutlined />;
}

const renderTitle = (styles, props) => {
  const {
    title, avatar, subTitle, tags, extra, onBack,
  } = props;

  const hasHeading = title || subTitle || tags || extra;
  if (!hasHeading) return null;

  const backIcon = getBackIcon(props);
  const backIconDom = renderBack(styles, backIcon, onBack);

  const hasTitle = backIconDom || avatar || hasHeading;

  return (
    <div className={styles.heading}>
      {hasTitle && (
        <div className={styles.headingLeft}>
          {backIconDom}
          {avatar && (
            <Avatar
              className={styles.headingAvatar}
              {...avatar}
            />
          )}
          {title && (
            <span
              className={styles.headingTitle}
              title={typeof title === 'string' ? title : undefined}
            >{title}</span>
          )}
          {subTitle && (
            <span
              className={styles.headingSubTitle}
              title={typeof subTitle === 'string' ? subTitle : undefined}
            >{subTitle}</span>
          )}
          {tags && (
            <span className={styles.headingTags}>{tags}</span>
          )}
        </div>
      )}
      {extra && (
        <span className={styles.headingExtra}>
          <Space>{extra}</Space>
        </span>
      )}
    </div>
  );
}

const renderFooter = (styles, footer) => {
  if (!footer) return null;

  return (
    <div className={styles.footer}>{footer}</div>
  );
}

const renderChildren = (styles, children) => <div className={styles.content}>{children}</div>;

const PageHeader = props => {
  const {
    style,
    footer,
    children,
    breadcrumb,
    breadcrumbRender,
    className: customizeClassName,
    contentWidth,
    layout,
    ghost = true,
  } = props;

  const { styles, cx } = useStyles();

  const [compact, setCompact] = useState(false);

  const onResize = width => setCompact(width < 768);

  const defaultBreadcrumbDom = renderBreadcrumb(styles, breadcrumb);
  const isBreadcrumbComponent = breadcrumb && 'props' in breadcrumb;
  // const breadcrumbDom = isBreadcrumbComponent
  //   ? breadcrumb
  //   : breadcrumbRender(props, defaultBreadcrumbDom) || defaultBreadcrumbDom;
  const breadcrumbDom = isBreadcrumbComponent
    ? breadcrumb
    : defaultBreadcrumbDom;

  const className = cx(
    customizeClassName,
    breadcrumbDom ? styles.hasBreadcrumb : '',
    footer ? styles.hasFooter : '',
    compact ? styles.compact : '',
    contentWidth === 'Fixed' && layout == 'top' ? styles.wide : '',
    ghost ? styles.ghost : '',
  );

  const title = renderTitle(styles, props);
  const childDom = children && renderChildren(children);
  const footerDom = renderFooter(footer);

  // 如果没有任何子元素，则返回一个空div
  if (!breadcrumbDom && !title && !footerDom && !childDom) {
    return <div className={styles.noChildren} />;
  }

  return (
    <ResizeObserver onResize={onResize}>
      <div className={className} style={style}>
        {breadcrumbDom}
        {title}
        {childDom}
        {footerDom}
      </div>
    </ResizeObserver>
  );
}

export { PageHeader};
