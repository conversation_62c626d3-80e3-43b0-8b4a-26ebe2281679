import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token, css, prefixCls, cx }) => {
  const ghost = cx(css``);
  const hasBreadcrumb = cx(css``);
  const hasFooter = cx(css``);

  const heading = cx(css`
    display: flex;
    justify-content: space-between;
  `);

  const textOverflowEllipsis = css`
    text-overflow: ellipsis;
    overflow: hidden;
  `;

  const tiToken = {
    pageHeaderBgGhost: 'transparent',
    pageHeaderPadding: 16,
    pageHeaderPaddingVertical: 4,
    pageHeaderPaddingBreadCrumb: token.paddingSM,
    pageHeaderColorBack: token.colorTextHeading,
    pageHeaderFontSizeHeaderTitle: token.fontSizeHeading4,
    pageHeaderFontSizeHeaderSubTitle: 14,
    pageHeaderPaddingContentPadding: token.paddingSM,
  }

  return {
    pageHeader: css`
      position: relative;
      background-color: ${token.colorWhite};
      padding-block: ${tiToken.pageHeaderPaddingVertical + 2}px;
      padding-inline: ${tiToken.pageHeaderPadding}px;

      &.${ghost} {
        background-color: ${tiToken.pageHeaderBgGhost};
      }
      &.${hasBreadcrumb} {
        padding-block-start: ${tiToken.pageHeaderPaddingBreadCrumb}px;
      }
      &.${hasFooter} {
        padding-block-end: 0;
      }

      .${prefixCls}-divider-vertical {
        height: 14px;
        margin-block: 0;
        margin-inline: ${token.marginSM}px;
        vertical-align: middle;
      }
    `,
    onChildren: css`
      height: ${token.layout?.pageContainer?.paddingBlockPageContainerContent}px;
    `,

    back: css`
      margin-inline-end: ${token.margin}px;
      font-size: 16px;
    `,
    backBtn: css`
      font-size: 16px;
      color: ${tiToken.pageHeaderColorBack};
      cursor: pointer;
    `,

    headingLeft: css`
      display: flex;
      align-items: center;
      margin-block: ${token.marginXS / 2}px;
      margin-inline-end: 0;
      margin-inline-start: 0;
      overflow: hidden;
    `,
    headingAvatar: css`
      margin-inline-end: ${token.marginSM}px;
    `,
    headingTitle: css`
      margin-inline-end: ${token.marginSM}px;
      margin-block-end: 0;
      color: ${token.colorTextHeading};
      font-weight: 600;
      font-size: ${tiToken.pageHeaderFontSizeHeaderTitle}px;
      line-height: ${token.controlHeight}px;
      ${textOverflowEllipsis}
    `,
    headingSubTitle: css`
      margin-inline-end: ${token.marginSM}px;
      color: ${token.colorTextSecondary};
      font-size: ${tiToken.pageHeaderFontSizeHeaderSubTitle}px;
      line-height: ${token.lineHeight}px;
      ${textOverflowEllipsis}
    `,
    headingTags: css``,
    headingExtra: css`
      margin-block: ${token.marginXS / 2}px;
      margin-block-end: 0;
      margin-block-start: 0;
      white-space: nowrap;

      > * {
        white-space: unset;
      }
    `,

    breadcrumb: css`
      & + .${heading} {
        margin-block-start: ${token.marginXS}px;
      }
    `,
    content: css`
      padding-block-start: ${tiToken.pageHeaderPaddingContentPadding}px;
    `,
    footer: css`
      margin-block-start: ${token.margin}px;
    `,

    compact: css`
      & .${heading} {
        flex-wrap: wrap;
      }
    `,
    wide: css`
      max-width: 1152px;
      margin: 0 auto;
    `,
  }
});
