import Link from 'next/link';
import { redirect } from 'next/navigation';

import { useMemo, useRef, useEffect } from 'react';
import { Dropdown, Space, Typography, Tag, App } from 'antd';
import {
  // UserOutlined,
  SettingOutlined,
  SyncOutlined,
  KeyOutlined,
} from '@ant-design/icons';
import { FormattedMessage, useIntl } from 'react-intl';

import { ModalForm } from '../components/form';
import { updatePwdForm } from './data';

import staticRouter from '@publics/static/staticRouter';
import { MESSAGE_TYPE, SITE_URL, CURD } from '../utils/consts';
import { roleDict, tagNormalColors } from '../utils/dicts';
import { DEFAULT_SESSION_PARAMS } from '../utils/session';
import { SESSION, TiLocalStorage, TiSessionStorage } from '../utils/storage';
// import { USER, SERVER, CLIENT, PRODUCT, TiSessionStorage } from '../utils/storage';
import { errorResp } from '../utils/error-response';

const { Text } = Typography;

const menuOthers = [{
  label: <Link href="/settings"><FormattedMessage id={'app.header.user.setting'} /></Link>,
  icon: <SettingOutlined />,
  key: '1',
}, {
  label: <FormattedMessage id={'app.header.user.clearcache'} />,
  icon: <SyncOutlined />,
  key: '2',
}, {
  type: 'divider',
}, {
  label: <FormattedMessage id={'app.auth.updatepwd'} />,
  icon: <KeyOutlined />,
  key: '3'
}];

const User = props => {
  const { isMobile } = props;

  const intervalRef = useRef(null);
  const updateForm = useRef(null);
  const { message } = App.useApp();

  const intl = useIntl();

  const defaultParams = {
    ...DEFAULT_SESSION_PARAMS(),
  };

  const localUser = TiLocalStorage.getJson(SESSION);

  const { post, response } = useFetch(SITE_URL.BASE);
  const [request, resp] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await post(SITE_URL.EXTEND(type, params.svrid), params);

    if (response.ok) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  useEffect(() => {
    if (localUser) {
      const timer = setInterval(() => {
        checkSession();
      }, 5 * 60 * 1000);

      intervalRef.current = timer;
    }

    return () => {
      intervalRef.current && clearInterval(intervalRef.current);
    }
  }, [localUser]); // eslint-disable-line react-hooks/exhaustive-deps

  const createUpdatePwdForm = useMemo(() => updatePwdForm(intl), [intl]);

  const checkSession = async () => {
    await request.post(SITE_URL.EXTEND(MESSAGE_TYPE.CheckSession), defaultParams);

    if (resp.status === 300) {
      gotoLogin();

      setTimeout(() => {
        // 刷新浏览器
        window.location.reload();
      }, 500);
    }
  }

  const clearStorage = () => {
    // TiSessionStorage.remove(USER);
    // TiSessionStorage.remove(SERVER);
    // TiSessionStorage.remove(CLIENT);
    // TiSessionStorage.remove(PRODUCT);
    TiSessionStorage.clear();

    // 删除后刷新浏览器
    window.location.reload();
  }

  const gotoLogin = () => {
    TiLocalStorage.clear();
    TiSessionStorage.clear();
    // navigate('/auth/login', { state: { from: location }}, { replace: true });
    redirect(staticRouter.login);
  };
  const changePwd = () => {
    const record = {
      loginname: localUser.id
    }
    updateForm.current.show(3, record);
  }

  const updatePwdSubmit = (form) => {
    const postData = {
      ...defaultParams,
      userid: form.loginname,
      oldpassword: form.oldpassword,
      newpassword: form.newpassword,
    }

    fetchFunc(MESSAGE_TYPE.UpdatePassword, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);

      gotoLogin();
    });
  }

  const UserRole = () => (localUser.role ? (
    <Tag color={tagNormalColors[localUser.role - 1]}>{roleDict[localUser.role]}</Tag>
  ) : null)

  const menuItems = useMemo(() => {
    if (isMobile) {
      return [{
        label: <UserRole />,
        key: '0',
        disabled: true
      }].concat(menuOthers);
    } else {
      return menuOthers;
    }
  }, [isMobile]);

  const menuClick = ({ key }) => {
    switch(key) {
      case '2':
        clearStorage();
        break;
      case '3':
        changePwd();
        break;
      default:
        break;
    }
  }

  return (<>
    {localUser ? (
      <Dropdown
        menu={{
          items: menuItems,
          onClick: menuClick,
        }}
        placement="bottomRight"
      >
        <Space>
          <Text style={{ marginRight: '5px', whiteSpace: 'nowrap' }}>{localUser.name || localUser.id}</Text>
          {!isMobile && <UserRole />}
        </Space>
      </Dropdown>
    ) : (
      <FormattedMessage id={'app.header.user.unknown'} />
    )}

    {/* 修改密码 */}
    <ModalForm
      ref={updateForm}
      onOk={updatePwdSubmit}
      formData={createUpdatePwdForm}
    />
  </>);
}

export default User;
