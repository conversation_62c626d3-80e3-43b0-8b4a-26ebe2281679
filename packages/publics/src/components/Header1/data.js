import {
  KeyOutlined,
  CopyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';

export const updatePwdForm = intl => [{
  valueType: 'text',
  name: 'loginname',
  label: $t('app.options.sys.loginname')
}, {
  valueType: 'password',
  name: 'oldpassword',
  label: $t('app.auth.oldpassword')
  rules: [{ required: true }],
  fieldProps: {
    prefix: <KeyOutlined className="ti-form-item-icon" />,
    iconRender: visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />),
  }
}, {
  valueType: 'password',
  name: 'newpassword',
  label: $t('app.auth.newpassword')
  rules: [{ required: true }],
  fieldProps: {
    prefix: <KeyOutlined className="ti-form-item-icon" />,
    iconRender: visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />),
  }
}, {
  valueType: 'password',
  name: 'repassword',
  label: $t('app.auth.newpassword.again')
  rules: [
    { required: true },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newpassword') === value) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error(
            $t('app.options.repassword.error')
          ));
        }
      }
    }),
  ],
  fieldProps: {
    prefix: <CopyOutlined className="ti-form-item-icon" />,
    iconRender: visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />),
  }
}];
