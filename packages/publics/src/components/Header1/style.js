import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  header: css`
    padding: 0;
    height: auto;
    width: 100%;
    border-block-end: 1px solid rgba(5, 5, 5, 0.06);
    background-color: rgba(255, 255, 255, 0.6);
    z-index: 19;
  `,
  headerFixed: css`
    position: sticky;
    top: 0;
  `,
  container: css`
    position: relative;
    padding: 0 16px;
  `,
  logo: css`
    display: inline-block;
    margin: 16px 8px 16px 0;
  `,
}));

export default useStyles;
