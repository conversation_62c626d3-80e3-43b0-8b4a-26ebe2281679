import { redirect } from 'next/navigation';

import { Toolt<PERSON>, But<PERSON> } from 'antd';
import { LoginOutlined } from '@ant-design/icons';
import { FormattedMessage } from 'react-intl';

// import staticRouter from '@publics/static/staticRouter';

const LogoutIcon = () => {
  // const localUser = TiLocalStorage.getJson(SESSION);

  // const gotoLogin = () => {
  //   TiLocalStorage.clear();
  //   TiSessionStorage.clear();

  //   redirect(staticRouter.login);
  // };
  const logout = async () => {
    // const logoutParams = {
    //   webuserid: localUser.id,
    //   sessionid: localUser.sessionParams,
    // }

    // const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.UserLogout), logoutParams);
    // if (response.ok) {
    //   gotoLogin();
    // } else {
    //   message.error(resp.errmessage || CURD.ServerError);

    //   // 不管成功与否都去登录
    //   setTimeout(() => {
    //     gotoLogin();
    //   }, 300);
    // }
    console.log('logout')
  }

  // return (
  //   <span className="ti-menu-item" onClick={logout}>
  //     <Tooltip title={<FormattedMessage id={'app.header.user.logout'} />}>
  //       <LoginOutlined />
  //     </Tooltip>
  //   </span>
  // );
  return (
    <Tooltip title={<FormattedMessage id={'app.header.user.logout'} />}>
      <Button type="text" icon={<LoginOutlined />} onClick={logout} />
    </Tooltip>
  );
}

export default LogoutIcon;
