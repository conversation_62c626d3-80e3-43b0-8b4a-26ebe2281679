import { ConfigProvider, Layout, Flex, Space } from 'antd';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
} from '@ant-design/icons';

// import { isNeedOpenHash } from '@publics/utils/tools';

import Branding from '@publics/components/Branding';
import LogoutIcon from './Logout';

import useStyles from './style';

const { Header } = Layout;

const DefaultHeader = props => {
  const {
    isMobile,
    pathSnippets,
    theme,
    collapsed,
    collapsedToggle,
  } = props;

  const { styles, cx } = useStyles();

  return (
    <ConfigProvider
      theme={{
        // hashed: isNeedOpenHash(),
        components: {
          Layout: {
            headerBg: 'transparent',
            bodyBg: 'transparent',
          },
        },
      }}
    >
      <Header className={cx(styles.header, styles.headerFixed)}>
        <Flex className={styles.container} justify="flex-start" align="center" gap="12px">
          {isMobile && (
            <span className="ti-menu-item ti-header-trigger" onClick={collapsedToggle}>
              {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            </span>
          )}
          <Branding
            style={styles.logo}
            collapsed={isMobile}
            theme={theme}
          />
          <Flex flex="1">
            {/*{!isMobile && (
              <Topmenu theme="dark" pathSnippets={pathSnippets} />
            )}*/}
            123
          </Flex>
          {/*<User isMobile={isMobile} />*/}
          <Space align="center" size={0} className="ti-header-active">
            {/* unlock */}
            {/*<UnlockIcon />*/}
            {/* logout */}
            <LogoutIcon />
            {/* fullscreen */}
            {/*<FullscreenIcon />*/}
          </Space>
        </Flex>
      </Header>
    </ConfigProvider>
  );
}

export default DefaultHeader;
