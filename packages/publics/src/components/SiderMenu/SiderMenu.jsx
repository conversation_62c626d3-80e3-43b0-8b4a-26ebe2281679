import { useMemo } from 'react';
import { Layout, Menu, Space, Avatar } from 'antd';
import { MacScrollbar } from 'mac-scrollbar';

import { Branding } from '@titd/publics/components';
// import Apps from '@publics/components/Apps';
import CollapsedIcon from './CollapsedIcon';
import { BaseMenu } from './BaseMenu';

import { useStyles } from './style';

const { Sider } = Layout;

const renderLogo = (props, renderKey = 'menuHeaderRender') => {
  if (props.isMobile) {
    return null;
  }

  if (props.layout === 'mix' && renderKey === 'menuHeaderRender') return false;

  return Branding(props);
}

const SiderMenu = props => {
  const {
    collapsed,
    originCollapsed,
    fixSiderbar,
    onCollapsed,
    theme,
    siderWidth,
    isMobile,
    onMenuHeaderClick,
    breakpoint = 'lg',
    style,
    layout,
    menuFooterRender,
    menuExtraRender = false,
    links,
    menuContentRender,
    collapsedButtonRender,
    avatar<PERSON><PERSON>,
    rightContentRender,
    actionsRender,
    onOpenChange,
    logoStyle,
  } = props;

  const { styles, cx } = useStyles();

  const showSiderExtraDom = useMemo(() => {
    if (isMobile) return false;
    if (layout === 'mix') return false;
    return true;
  }, [isMobile, layout]);

  // 收起的宽度
  const collapsedWidth = 64;

  const siderClassName = cx(
    styles.sider,
    fixSiderbar ? styles.siderFixed : '',
    (layout === 'mix' && !isMobile && fixSiderbar) ? styles.siderFixedMix : '',
    props.collapsed ? styles.siderCollapsed : '',
    theme !== 'dark' ? styles.siderLight : '',
    (layout === 'mix' && !isMobile) ? styles.siderMix : '',
  );

  const headerDom = renderLogo(props);
  const extraDom = menuExtraRender && menuExtraRender(props);

  // 主题菜单
  const menuDom = useMemo(() => menuContentRender !== false && (
    <BaseMenu
      className={styles.menu}
      {...props}
      key="base-menu"
      mode={collapsed && !isMobile ? 'vertical' : 'inline'}
      hangleOpenChange={onOpenChange}
      style={{
        width: '100%',
      }}
    />
    // eslint-disable-next-line react-hooks/exhaustive-deps
  ), [menuContentRender, onOpenChange, props]);

  const linksMenuItems = (links || []).map((node, index) => ({
    className: styles.link,
    label: node,
    key: index,
  }));

  const menuRenderDom = useMemo(() => menuContentRender ? menuContentRender(props, menuDom) : menuDom, [menuContentRender, menuDom, props]);

  const avatarDom = useMemo(() => {
    if (!avatarProps) return null;

    const { title, render, ...rest } = avatarProps;
    const dom = (
      <div className={styles.actionsAvatar}>
        {rest?.src || rest.icon || rest.children ? (
          <Avatar size={28} {...rest} />
        ) : null}
        {title && !collapsed && <span>{title}</span>}
      </div>
    );
    if (render) {
      return render(avatarProps, dom, props);
    }

    return dom;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [avatarProps, collapsed]);

  const actionsDom = useMemo(() => {
    if (!actionsRender) return null;

    return (
      <Space
        className={cx(
          [styles.actionsList],
          { [styles.actionsListCollapsed]: collapsed },
        )}
        align="center"
        size={4}
        direction={collapsed ? 'vertical' : 'horizontal'}
      >
        {[actionsRender?.(props)].map((item, index) => (
          <div
            key={index}
            className={styles.actionsListItem}
          >{item}</div>
        ))}
      </Space>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [actionsRender, collapsed]);

  // const appsDom = useMemo(() => (
  //   <Apps
  //     onItemClick={props.itemClick}
  //     appList={props.appList}
  //   />
  // ), [props.appList]);

  // 收缩按钮
  const collapsedDom = useMemo(() => {
    if (collapsedButtonRender === false) return null;

    const dom = (
      <CollapsedIcon
        className={styles.collapsedButton}
        isMobile={isMobile}
        collapsed={originCollapsed}
        onClick={() => onCollapsed?.(!originCollapsed)}
      />
    );
    if (collapsedButtonRender) {
      return collapsedButtonRender(collapsed, dom);
    }

    return dom;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    collapsedButtonRender,
    isMobile,
    originCollapsed,
    collapsed,
    onCollapsed,
  ]);

  // 操作区域的dom
  const actionAreaDom = useMemo(() => {
    if (!avatarDom && !actionsDom) return null;

    return (
      <div className={cx(
        [styles.actions],
        { [styles.actionsCollapsed]: collapsed },
      )}>
        {avatarDom}
        {actionsDom}
      </div>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    actionsDom,
    avatarDom,
    collapsed,
  ]);

  const hideMenuWhenCollapsed = useMemo(() => {
    // 收起时完全隐藏菜单
    if (props?.menu?.hideMenuWhenCollapsed && collapsed) {
      return true;
    }
    return false;
  }, [collapsed, props?.menu?.hideMenuWhenCollapsed]);

  const menuFooterDom = menuFooterRender && menuFooterRender?.(props);

  const menuDomItems = (<>
    {headerDom && (
      <div
        className={cx(
          [styles.logo],
          { [styles.logoCollapsed]: collapsed },
        )}
        onClick={showSiderExtraDom ? onMenuHeaderClick : undefined}
        key="logo"
        style={logoStyle}
      >
        {headerDom}
        {/*{appsDom}*/}
      </div>
    )}
    {extraDom && (
      <div className={cx(
        [styles.extra],
        { [styles.extraNoLogo]: !headerDom },
      )}>{extraDom}</div>
    )}
    <MacScrollbar style={{
      flex: 1,
      overflowX: 'hidden',
    }}>{menuRenderDom}</MacScrollbar>

    {/* 扩展 */}
    <div>
      {links ? (
        <div className={styles.links}>
          <Menu
            className={styles.linkMenu}
            inlineIndent={16}
            selectedKeys={[]}
            openKeys={[]}
            theme={theme}
            mode="inline"
            items={linksMenuItems}
          />
        </div>
      ) : null}
      {showSiderExtraDom && (<>
        {actionAreaDom}
        {!actionsDom && rightContentRender ? (
          <div className={cx(
            [styles.actions],
            { [styles.actionsCollapsed]: collapsed },
          )}>{rightContentRender?.(props)}</div>
        ) : null}
      </>)}
      {menuFooterDom && (
        <div className={cx(
          [styles.footer],
          { [styles.footerCollapsed]: collapsed }
        )}>{menuFooterDom}</div>
      )}
    </div>
  </>);

  const fixMenuWidth = useMemo(() => collapsed ? collapsedWidth : siderWidth, [collapsed, siderWidth]);

  return (<>
    {fixSiderbar && !isMobile && !hideMenuWhenCollapsed && (
      <div style={{
        width: fixMenuWidth,
        overflow: 'hidden',
        flex: `0 0 ${fixMenuWidth}`,
        maxWidth: fixMenuWidth,
        minWidth: fixMenuWidth,
        transiton: 'all 0.2s ease 0s',
        ...style,
      }} />
    )}

    <Sider
      collapsible
      trigger={null}
      collapsed={collapsed}
      breakpoint={breakpoint === false ? undefined : breakpoint}
      onCollapse={collapse => {
        if (isMobile) return;
        onCollapsed?.(collapse);
      }}
      collapsedWidth={collapsedWidth}
      style={style}
      theme={theme}
      width={siderWidth}
      className={cx(
        siderClassName,
        hideMenuWhenCollapsed ? styles.hideMenuWhenCollapsed : '',
      )}
    >
      {hideMenuWhenCollapsed ? (
        <div
          className={styles.hideWhenCollapsed}
          style={{
            height: '100%',
            width: '100%',
            opacity: hideMenuWhenCollapsed ? 0 : 1,
          }}
        >{menuDomItems}</div>
      ) : (
        menuDomItems
      )}

      {collapsedDom}
    </Sider>
  </>)
}

export { SiderMenu };
