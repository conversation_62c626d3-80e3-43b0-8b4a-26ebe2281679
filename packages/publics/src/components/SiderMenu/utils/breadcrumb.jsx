import { match } from 'path-to-regexp';

// /userInfo/2144/id => ['/userInfo','/userInfo/2144,'/userInfo/2144/id']
const urlToList = url => {
  if (!url || url === '/') {
    return ['/'];
  }

  const urlList = url.split('/').filter(i => i);
  return urlList.map((urlItem, index) => `/${urlList.slice(0, index + 1).join('/')}`,
  );
}

const defaultItemRender = (route, _, routes) => {
  const { breadcrumbName, title, path } = route;

  const last = routes.findIndex(i => i.linkPath === route.path) === routes.length - 1;
  return last ? (
    <span>{title || breadcrumbName}</span>
  ) : (
    <span onClick={path ? () => (location.href = path) : undefined}>{title || breadcrumbName}</span>
  );
}

const renderItemLocal = (item, props) => {
  const { formatMessage, menu } = props;
  if(item.locale && formatMessage && menu?.locale !== false) {
    return formatMessage({ id: item.locale, defaultMessage: item.name });
  }
  return item.name;
}

export const getBreadcrumb = (breadcrumbMap, url) => {
  let breadcrumbItem = breadcrumbMap.get(url);
  if (!breadcrumbItem) {
    const keys = Array.from(breadcrumbMap.keys()) || [];
    const targetPath = keys.find(path => {
      try {
        if (path?.startsWith('http')) return false;
        return match(path.replace('?', ''))(url);
      } catch (error) {
        console.log('path', path, error);
        return false;
      }
    });
    if (targetPath) {
      breadcrumbItem = breadcrumbMap.get(targetPath);
    }
  }

  return breadcrumbItem || { path: '' };
}

export const getBreadcrumbFromProps = props => {
  const { location, breadcrumbMap } = props;
  return {
    location,
    breadcrumbMap,
  };
}

const conversionFromLocation = (routerLocation, breadcrumbMap, props) => {
  const pathSnippets = urlToList(routerLocation?.pathname);
  const extraBreadcrumbItems = pathSnippets.map(url => {
    const currentBreadcrumb = getBreadcrumb(breadcrumbMap, url);
    const name = renderItemLocal(currentBreadcrumb, props);
    const { hideInBreadcrumb } = currentBreadcrumb;
    return name && !hideInBreadcrumb ? {
      linkPath: url,
      breadcrumbName: name,
      title: name,
      component: currentBreadcrumb.component,
    } : {
      linkPath: '',
      breadcrumbName: '',
      title: '',
    }
  }).filter(i => i && i.linkPath);

  return extraBreadcrumbItems;
}

export const genBreadcrumbProps = props => {
  const { location, breadcrumbMap } = getBreadcrumbFromProps(props);

  if (location && location.pathname && breadcrumbMap) {
    return conversionFromLocation(location, breadcrumbMap, props);
  }
  return [];
}

export const getBreadcrumbProps = (props, layoutProps) => {
  const { breadcrumbRender, itemRender: propsItemRender } = props;
  const { minLength = 2 } = layoutProps.breadcrumbProps || {};
  const routesArray = genBreadcrumbProps(props);
  const itemRender = (item, ...rest) => {
    const renderFunction = propsItemRender || defaultItemRender;
    return renderFunction?.(
      {
        ...item,
        path: item.linkPath || item.path,
      },
      ...rest,
    );
  };

  let items = routesArray;
  // 如果breadcrumbRender存在，则使用其渲染数组items
  if (breadcrumbRender) {
    items = breadcrumbRender(items || []) || undefined;
  }
  // 如果items（渲染后的数组）的长度小于minLength或者breadcrumbRender为false，则items为undefined
  if ((items && items.length < minLength) || breadcrumbRender === false) {
    items = undefined;
  }
  // ant design 版本大于等于5.3.0，则返回一个对象{items,itemRender}
  return {
    items,
    itemRender,
  };
}
