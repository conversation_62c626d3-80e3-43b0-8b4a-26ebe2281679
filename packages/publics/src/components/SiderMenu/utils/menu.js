import { transformRoute } from '@umijs/route-utils';

export const getOpenKeysFromMenuData = menuData => (menuData || []).reduce((pre, item) => {
  if (item.key) {
    pre.push(item.key);
  }
  if (item.children) {
    const newArray = pre.concat(getOpenKeysFromMenuData(item.children) || []);
    return newArray;
  }
  return pre;
}, []);

export const clearMenuItem = menusData => menusData.map(item => {
  const children = item.children || [];
  const finalItem = { ...item };

  if (!finalItem.name || finalItem.hideInMenu) {
    return null;
  }
  if (finalItem && finalItem?.children) {
    if (
      !finalItem.hideChildrenInMenu &&
      children.some(child => child && child.name && !child.hideInMenu)
    ) {
      return {
        ...item,
        children: clearMenuItem(children),
      }
    }
    // children为空删除
    delete finalItem.children;
  }
  return finalItem;
}).filter(i => i);

function fromEntries(iterable) {
  return [...iterable].reduce((obj, [key, val]) => {
    obj[key] = val;
    return obj;
  });
}

export const getMenuData = (
  routes,
  menu,
  formatMessage,
  menuDataRender
) => {
  if (!routes || routes.length === 0) {
    return {};
  }
  const { menuData, breadcrumb } = transformRoute(
    routes,
    menu?.locale,
    formatMessage,
    true,
  );

  if (!menuDataRender) {
    return {
      breadcrumb: fromEntries(breadcrumb),
      breadcrumbMap: breadcrumb,
      menuData,
    };
  }
  return getMenuData(menuDataRender(menuData), menu, formatMessage, undefined);
}
