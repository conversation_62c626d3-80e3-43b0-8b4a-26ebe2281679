import { RightOutlined } from '@ant-design/icons';

import { useBtnStyles } from './style';

const CollapsedIcon = props => {
  const { isMobile, collapsed, ...rest } = props;

  const { styles, cx } = useBtnStyles();

  if (isMobile && collapsed) return null;

  return (
    <div
      {...rest}
      className={cx(
        styles.btnIcon,
        collapsed ? styles.btnIconCollapsed : '',
        isMobile ? styles.btnIconIsMobile : '',
      )}
    >
      <RightOutlined />
    </div>
  );
}

export default CollapsedIcon;
