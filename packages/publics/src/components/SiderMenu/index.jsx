import { useEffect } from 'react';
import { Drawer } from 'antd';
import Omit from 'omit.js';

import { SiderMenu } from './SiderMenu';

const SiderMenuWrapper = props => {
  const {
    isMobile,
    siderWidth,
    collapsed,
    onCollapse,
    style,
    hide,
    getContainer,
  } = props;

  useEffect(() => {
    if (isMobile === true) {
      onCollapse?.(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMobile]);

  const omitProps = Omit(props, ['style']);

  if (hide) {
    return null;
  }

  return isMobile ? (
    <Drawer
      style={{
        padding: 0,
        height: '100vh',
        ...style,
      }}
      onClose={() => onCollapse?.(true)}
      maskClosable
      closable={false}
      getContainer={getContainer || false}
      width={siderWidth}
      styles={{
        body: {
          height: '100vh',
          padding: 0,
          display: 'flex',
          flexDirection: 'row',
          // backgroundColor: token.layout?.sider?.colorMenuBackground,
        },
      }}
    >
      <SiderMenu
        {...omitProps}
        isMobile={true}
        collapsed={isMobile ? false : collapsed}
        splitMenus={false}
        originCollapsed={collapsed}
      />
    </Drawer>
  ) : (
    <SiderMenu
      originCollapsed={collapsed}
      {...omitProps}
      style={style}
    />
  );
}

export default SiderMenuWrapper;
