import { useEffect, useState } from 'react';
import { Tooltip } from 'antd';

const MenuItemTooltip = props => {
  const [collapsed, setCollapsed] = useState(props.collapsed);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    setOpen(false);
    setTimeout(() => setCollapsed(props.collapsed), 400);
  }, [props.collapsed]);

  if (props.disable) {
    return props.children;
  }

  return (
    <Tooltip
      title={props.title}
      open={collapsed && props.collapsed ? open : false}
      placement="right"
      onOpenChange={setOpen}
    >{props.children}</Tooltip>
  );
}

export default MenuItemTooltip;
