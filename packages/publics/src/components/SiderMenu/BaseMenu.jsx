import { useRef, useState, useEffect, useMemo } from 'react';
import { Menu, Tooltip } from 'antd';
import useMergedState from 'rc-util/lib/hooks/useMergedState';

import { getOpenKeysFromMenuData } from './utils';

import MenuUtil from './menu-util';

import { useMenuStyles } from './style';

const getOpenKeysProps = (
  openKeys,
  { layout, collapsed }
) => {
  let openKeysProps = {};

  if (openKeys && !collapsed && ['side', 'mix'].includes(layout || 'mix')) {
    openKeysProps = {
      openKeys,
    };
  }
  return openKeysProps;
}

const BaseMenu = props => {
  const {
    mode,
    theme,
    className,
    handleOpenChange,
    style,
    menuData,
    menu,
    matchMenuKeys,
    selectedKeys: propsSelectedKeys,
    onSelect,
    menuRenderType, // 'header' | 'side'
    openKeys: propsOpenKeys,
  } = props;

  const { styles, cx } = useMenuStyles({ mode });

  const defaultOpenKeysRef = useRef([]);

  const [defaultOpenAll, setDefaultOpenAll] = useMergedState(menu?.defaultOpenAll);
  const [openKeys, setOpenKeys] = useMergedState(() => {
    if (menu?.defaultOpenAll) {
      return getOpenKeysFromMenuData(menuData) || [];
    }
    if (propsOpenKeys === false) {
      return false;
    }
    return [];
  }, {
    value: propsOpenKeys === false ? undefined : propsOpenKeys,
    onChange: handleOpenChange,
  });
  const [selectedKeys, setSelectedKeys] = useMergedState([], {
    value: propsSelectedKeys,
    onChange: onSelect ? keys => {
      if (onSelect && keys) {
        onSelect(keys);
      }
    } : undefined,
  });

  useEffect(() => {
    if (menu?.defaultOpenAll || propsOpenKeys === false) return;

    if (matchMenuKeys) {
      setOpenKeys(matchMenuKeys);
      setSelectedKeys(matchMenuKeys);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchMenuKeys.join('-')]);

  useEffect(() => {
    if (matchMenuKeys.join('-') !== (selectedKeys || []).join('-')) {
      setSelectedKeys(matchMenuKeys);
    }
    if (
      !defaultOpenAll &&
      propsOpenKeys !== false &&
      matchMenuKeys.join('-') !== (openKeys || []).join('-')
    ) {
      let newKeys = matchMenuKeys;
      if (menu?.autoClose === false) {
        newKeys = Array.from(
          new Set([...matchMenuKeys, ...(openKeys || [])])
        );
      }
      setOpenKeys(newKeys);
    } else if (menu?.ignoreFlatMenu && defaultOpenAll) {
      setOpenKeys(getOpenKeysFromMenuData(menuData));
    } else {
      setDefaultOpenAll(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matchMenuKeys.join('-')]);

  const openKeysProps = useMemo(() => getOpenKeysProps(openKeys, props), [openKeys && openKeys.join(','), props.layout, props.collapsed]);

  const menuUtils = useMemo(() => {
    return new MenuUtil({
      ...props,
      menuRenderType,
      styles,
      cx,
    });
  }, [props, menuRenderType, styles, cx]);

  if (props.openKeys === false && !props.handleOpenChange) {
    defaultOpenKeysRef.current = matchMenuKeys;
  }

  const finallyData = props.postMenuData ? props.postMenuData(menuData) : menuData;
  if (finallyData && finallyData?.length < 1) {
    return null;
  }

  return (
    <Menu
      {...openKeysProps}
      className={cx(
        className,
        styles.baseMenu,
        mode === 'horizontal' ? styles.baseMenuHorizontal : '',
        props.collapsed ? styles.baseMenuCollapsed : '',
      )}
      key="menu"
      mode={mode}
      inlineIndent={16}
      defaultOpenKeys={defaultOpenKeysRef.current}
      theme={theme}
      selectedKeys={selectedKeys}
      style={{
        backgroundColor: 'transparent',
        border: 'none',
        ...style,
      }}
      items={menuUtils.getNavMenuItems(finallyData, 0, 0)}
      onOpenChange={_openKeys => {
        if (!props.collapsed) {
          setOpenKeys(_openKeys);
        }
      }}
      {...props.menuProps}
    />
  );
};

export { BaseMenu };
