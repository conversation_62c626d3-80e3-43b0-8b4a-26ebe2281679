import { Image } from 'antd';

import MenuItemTooltip from './MenuItemTooltip';

import { tools } from '@titd/publics/utils';

const { isUrl, isImg } = tools;

const getIcon = (icon, className) => {
  if (typeof icon === 'string' && icon !== '') {
    if (isUrl(icon) || isImg(icon)) {
      return (
        <Image
          width={16}
          key={icon}
          src={icon}
          alt="icon"
          className={className}
        />
      )
    };
  }
  return icon;
}

const getMenuTitleSymbol = title => {
  if (title && typeof title === 'string') {
    return title.substring(0, 1).toUpperCase();
  }
  return null;
};

class MenuUtil {
  constructor(props) {
    this.props = props;
  }

  getNavMenuItems = (menusData, level, noGroupLevel) => menusData
    .map(item => this.getSubMenuOrItem(item, level, noGroupLevel))
    .filter(i => i)
    .flat(1);

  getSubMenuOrItem = (item, level, noGroupLevel) => {
    const {
      subMenuItemRender,
      collapsed,
      menu,
      layout,
      styles,
      cx,
    } = this.props;

    const isGroup = menu?.type === 'group' && layout !== 'top';

    const name = this.getIntlName(item);
    const children = item?.children;

    const menuType = isGroup && level === 0 ? 'group' : undefined;
    const itemKey = item.key || item.path;

    if (Array.isArray(children) && children.length > 0) {
      // 第一级有icon，isGroup 时第二级也有
      const shouldHasIcon = level === 0 || (isGroup && level === 1);

      const iconDom = getIcon(item.icon, styles.itemIcon);
      // 无icon用首字代替
      const defaultIcon = collapsed && shouldHasIcon ? getMenuTitleSymbol(name) : null;

      const defaultTitle = (
        <div className={cx(
          styles.itemTitle,
          collapsed ? styles.itemTitleCollapsed : '',
          collapsed && !noGroupLevel ? styles.itemTitleCollapsedLevel0 : '',
          menuType === 'group' ? styles.groupItemTitle : '',
          menu?.collapsedShowTitle && collapsed ? styles.itemCollapsedShowTitle : '',
        )}>
          {menuType === 'group' && collapsed ? null :
            shouldHasIcon && iconDom ? (
              <span className={styles.itemIcon}>{iconDom}</span>
            ) : (
              defaultIcon
            )}
          <span className={cx(
            styles.itemText,
            menuType !== 'group' && shouldHasIcon && (iconDom || defaultIcon) ? styles.itemTextHasIcon : '',
          )}>{name}</span>
        </div>
      );

      const title = subMenuItemRender ? subMenuItemRender({ ...item, isUrl: false }, defaultTitle, this.props) : defaultTitle;

      if (
        isGroup &&
        level === 0 &&
        !menu.collapsedShowGroupTitle
      ) {
        return this.getNavMenuItems(children, level + 1, level);
      }

      const childrenList = this.getNavMenuItems(
        children,
        level + 1,
        isGroup && level === 0 && this.props.collapsed ? level : level + 1,
      );

      return [
        {
          type: menuType,
          key: itemKey,
          label: title,
          onClick: isGroup ? undefined : item.onTitleClick,
          children: childrenList,
          className: cx(
            menuType === 'group' ? styles.group : '',
            menuType !== 'group' ? styles.submenu : '',
            menuType !== 'group' && shouldHasIcon && iconDom ? styles.submenuHasIcon : '',
          )
        },
        isGroup && level === 0
          ? ({
            type: 'divider',
            className: styles.groupDivider,
            key: itemKey + '-group-divider',

          }) : undefined
      ].filter(i => i);
    }

    return {
      className: styles.menuItem,
      disabled: item.disabled,
      key: itemKey,
      onClick: item.onTitleClick,
      label: this.getMenuItemPath(item, level, noGroupLevel),
    }
  };

  // 国际化
  getIntlName = item => {
    const { name, locale } = item;
    const { menu, formatMessage } = this.props;
    if (locale && menu?.locale !== false) {
      return formatMessage?.(locale) || name;
    }
    return name;
  }

  getMenuItemPath = (item, level, noGroupLevel) => {
    const itemPath = this.conversionPath(item.path || '/');
    const {
      location = { pathname: '/' },
      isMobile,
      onCollapse,
      menuItemRender,
      menu,
      collapsed,
      styles,
      cx,
    } = this.props;

    const menuItemTitle = this.getIntlName(item);
    const isGroup = menu?.type === 'group';
    // 第一级有icon，isGroup 时第二级也有
    const hasIcon = level === 0 || (isGroup && level === 1);
    const icon = !hasIcon
      ? null
      : getIcon(item.icon, styles.icon);

    // 无icon用首字代替
    const defaultIcon = collapsed && hasIcon ? getMenuTitleSymbol(menuItemTitle) : null;

    const isHttpUrl = isUrl(itemPath);
    const defaultItem = (
      <div
        key={itemPath}
        onClick={isHttpUrl ? () => window?.open?.(itemPath, '_blank') : undefined}
        className={cx(
          styles.itemTitle,
          collapsed ? styles.itemTitleCollapsed : '',
          menu?.collapsedShowTitle && collapsed ? styles.itemCollapsedShowTitle : '',
          isHttpUrl ? styles.itemLink : '',
        )}
      >
        <span
          className={styles.itemIcon}
          style={{
            display: defaultIcon === null && !icon ? 'none' : ''
          }}
        >
          {icon || <span className="anticon">{defaultIcon}</span>}
        </span>
        <span
          className={cx(
            styles.itemText,
            hasIcon && (icon || defaultIcon) ? styles.itemTextHasIcon : '',
          )}
        >{menuItemTitle}</span>
      </div>
    );
    if (menuItemRender) {
      const renderItemProps = {
        ...item,
        isUrl: isHttpUrl,
        itemPath,
        isMobile,
        replace: itemPath === location.pathname,
        onClick: () => onCollapse && onCollapse(true),
        children: undefined,
      };
      return level === 0 ? (
        <MenuItemTooltip
          collapsed={collapsed}
          title={menuItemTitle}
          disable={item.disabledTooltip}
        >
          {menuItemRender(renderItemProps, defaultItem, this.props)}
        </MenuItemTooltip>
      ) : (
        menuItemRender(renderItemProps, defaultItem, this.props)
      );
    }

    return level === 0 ? (
      <MenuItemTooltip
        collapsed={collapsed}
        title={menuItemTitle}
        disable={item.disabledTooltip}
      >
        {defaultItem}
      </MenuItemTooltip>
    ) : (
      defaultItem
    );
  };

  conversionPath = path => {
    if (path && path.indexOf('http') === 0) {
      return path;
    }
    return `/${path || ''}`.replace(/\/+/g, '/');
  };
}

export default MenuUtil;
