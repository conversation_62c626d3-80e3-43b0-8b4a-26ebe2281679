import { createStyles, keyframes } from 'antd-style';

const layoutTitleHide = keyframes`
  0% {
    display: none;
    opacity: 0;
    overflow: hidden;
  }
  80% {
    overflow: hidden;
  }
  100% {
    display: unset;
    opacity: 1;
  }
`;

// 侧边栏
const useStyles = createStyles(({ token, css, prefixCls }) => ({
  sider: css`
    position: relative;
    box-sizing: border-box;
    &.${prefixCls}-layout-sider {
      background: ${token.sider?.colorMenuBackground || 'transparent'};
    }

    & .${prefixCls}-layout-sider-children {
      position: relative;
      display: flex;
      flex-direction: column;
      height: 100%;
      padding-inline: ${token.sider?.paddingInlineLayoutMenu}px;
      padding-block: ${token.sider?.paddingBlockLayoutMenu}px;
      border-inline-end: 1px solid ${token.colorSplit};
      margin-inline-end: -1px;
    }

    .${prefixCls}-menu {
      .${prefixCls}-menu-item-group-title {
        font-size: ${token.fontSizeSM}px;
        padding-bottom: 4px;
      }
      .${prefixCls}-menu-item:hover {
        color: ${token.sider?.colorTextMenuItemHover};
      }
    }
  `,
  siderFixed: css`
    position: fixed !important;
    inset-block-start: 0;
    inset-inline-start: 0;
    z-index: 100;
    height: 100%;
  `,
  siderFixedMix: css`
    height: calc(100% - ${token.header?.heightLayoutHeader || 56}px);
    inset-block-start: ${token.header?.heightLayoutHeader || 56}px;
  `,
  siderCollapsed: css``,
  siderLight: css``,
  siderMix: css``,
  logo: css`
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-inline: 12px;
    padding-block: 16px;
    color: ${token.sider.colorTextMenu};
    cursor: pointer;
    border-block-end: 1px solid ${token.sider.colorMenuItemDivider};
  `,
  logoCollapsed: css`
    flex-direction: column-reverse;
    margin: 0;
    padding: 12px;
  `,
  actions: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-block: 4px;
    margin-inline: 0;
    color: ${token.sider?.colorTextMenu};
  `,
  actionsCollapsed: css`
    flex-direction: column-reverse;
    padding-block: 0;
    padding-inline: 8px;
    font-size: 16px;
    transition: font-size 0.3s ease-in-out;
  `,
  actionsList: css`
    color: ${token.sider?.colorTextMenuSecondary};
  `,
  actionsListCollapsed: css`
    margin-block-end: 8px;
    animation-name: none;
  `,
  actionsListItem: css`
    padding-inline: 6px;
    padding-block: 6px;
    line-height: 16px;
    font-size: 16px;
    cursor: pointer;
    border-radius: ${token.borderRadius}px;
    &:hover {
      background: ${token.colorBgTextHover};
    }
  `,
  actionsAvatar: css`
    font-size: 14px;
    padding-inline: 8px;
    padding-block: 8px;
    display: flex;
    align-items: center;
    gap: ${token.marginXS}px;
    border-radius: ${token.borderRadius}px;
    & * {
      cursor: pointer;
    }
    &:hover {
      background: ${token.colorBgTextHover};
    }
  `,
  hideMenuWhenCollapsed: css`
    inset-inline-start: -${token.proLayoutCollapsedWidth - 12}px;
    position: absolute;
  `,
  hideWhenCollapsed: css``,
  extra: css`
    margin-block: 0px;
    margin-block-end: 16px;
    margin-inline: 16px;
  `,
  extraNoLogo: css`
    margin-block-start: 16px;
  `,
  links: css`
    width: 100%;

    ul {
      height: auto;
    }
  `,
  linkMenu: css`
    border: none;
    box-shadow: none;
    background: transparent;
  `,
  link: css``,
  footer: css`
    color: ${token.sider?.colorTextMenuSecondary};
    padding-block-end: 16px;
    font-size: ${token.fontSize}px;
    animation: ${layoutTitleHide} 0.4s ease;
  `,
  footerCollapsed: css``,
  collapsedButton: css``,
}));

// 菜单栏
const useMenuStyles = createStyles(({ token, css, cx, prefixCls }, { mode }) => {
  const menuToken = mode?.includes('horizontal')
    ? token.header
    : token.sider;

  const itemText = cx(css`
    text-overflow: ellipsis;
    overflow: hidden;
  `);
  const itemIcon = cx(css`
    display: flex;
    align-items: center;
  `);
  const itemTitleCollapsed = cx(css`
    min-width: 40px;
    height: 40px;
  `);
  const itemTextHasIcon = cx(css``);
  const group = cx(css``);

  return {
    baseMenu: css`
      background: transparent;
      color: ${menuToken?.colorTextMenu};
      border: none;

      .${prefixCls}-menu-title-content {
        width: 100%;
        height: 100%;
        display: inline-block;

        &:first-child {
          width: 100%;
        }
      }

      .${prefixCls}-menu-sub {
        color: ${token.sider?.colorTextMenu};
      }
      .${prefixCls}-layout {
        background-color: transparent;
        width: 100%;
      }
      .${prefixCls}-menu-submenu-expand-icon,
      .${prefixCls}-menu-submenu-arrow {
        color: inherit;
      }
      &.${prefixCls}-menu {
        color: ${token.sider?.colorTextMenu};

        .${prefixCls}-menu-item {
          * {
            transition: none !important;
          }
          a {
            color: inherit;
          }
        }
      }
      &.${prefixCls}-menu-inline {
        .${prefixCls}-menu-selected::after,
        .${prefixCls}-menu-item-selected::after {
          display: none;
        }
      }
      .${prefixCls}-menu-sub,
      .${prefixCls}-menu-inline {
        background-color: transparent !important;
      }
      .${prefixCls}-menu-item:active,
      .${prefixCls}-menu-submenu-title:active {
        background-color: transparent !important;
      }
      &.${prefixCls}-menu-light {
        .${prefixCls}-menu-item:hover,
        .${prefixCls}-menu-item-active,
        .${prefixCls}-menu-submenu-active,
        .${prefixCls}-menu-submenu-title:hover {
          color: ${token.sider?.colorTextMenuActive};
          border-radius: ${token.borderRadius}px;
        }
        .${prefixCls}-menu-submenu-arrow {
          color: ${token.sider?.colorTextMenuActive};
        }
      }
      &.${prefixCls}-menu:not(.${prefixCls}-menu-horizontal) {
        .${prefixCls}-menu-item-selected {
          background-color: ${token.sider?.colorBgMenuItemSelected};
          border-radius: ${token.borderRadius}px;
        }
        .${prefixCls}-menu-item:hover,
        .${prefixCls}-menu-item-active,
        .${prefixCls}-menu-submenu-title:hover {
          color: ${token.sider?.colorTextMenuActive};
          border-radius: ${token.borderRadius}px;
          background-color: ${token.header?.colorBgMenuItemHover} !important;
          .${prefixCls}-menu-submenu-arrow {
            color: ${token.sider?.colorTextMenuActive};
          }
        }
      }
      .${prefixCls}-menu-item-selected,
      .${prefixCls}-menu-submenu-selected {
        color: ${token.sider?.colorTextMenuSelected};
      }
      &.${prefixCls}-menu:not(.${prefixCls}-menu-inline) .${prefixCls}-menu-submenu-open {
        color: ${token.sider?.colorTextMenuSelected};
      }

      &.${prefixCls}-menu-vertical {
        .${prefixCls}-menu-submenu-selected {
          border-radius: ${token.borderRadius}px;
          color: ${token.sider?.colorTextMenuSelected};
        }
      }

      .${prefixCls}-menu-submenu:hover > .${prefixCls}-menu-submenu-title > .${prefixCls}-menu-submenu-arrow {
        color: ${token.sider?.colorTextMenuActive};
      }

      &.${prefixCls}-menu-horizontal {
        .${prefixCls}-menu-item:hover,
        .${prefixCls}-menu-submenu:hover,
        .${prefixCls}-menu-item-active,
        .${prefixCls}-menu-submenu-active {
          border-radius: 4px;
          transition: none;
          color: ${token.header?.colorTextMenuActive};
          background-color: ${token.header?.colorBgMenuItemHover} !important;
        }

        .${prefixCls}-menu-item-open,
        .${prefixCls}-menu-submenu-open,
        .${prefixCls}-menu-item-selected,
        .${prefixCls}-menu-submenu-selected {
          background-color: ${token.header?.colorBgMenuItemSelected};
          border-radius: ${token.borderRadius}px;
          transition: none;
          color: ${token.header?.colorTextMenuSelected} !important;
          .${prefixCls}-menu-submenu-arrow {
            color: ${token.header?.colorTextMenuSelected} !important;
          }
        }
        > .${prefixCls}-menu-item,
        > .${prefixCls}-menu-submenu {
          padding-inline: 16px;
          margin-inline: 4px;
        }
        > .${prefixCls}-menu-item::after,
        > .${prefixCls}-menu-submenu::after {
          display: none;
        }
      }
    `,
    baseMenuHorizontal: css``,
    baseMenuCollapsed: css`
      .${prefixCls}-menu-item,
      .${prefixCls}-menu-item-group > .${prefixCls}-menu-item-group-list > .${prefixCls}-menu-item,
      .${prefixCls}-menu-item-group > .${prefixCls}-menu-item-group-list > .${prefixCls}-menu-submenu > .${prefixCls}-menu-submenu-title
      .${prefixCls}-menu-submenu > .${prefixCls}-submenu-title {
        padding-inline: 0px !important;
        margin-block: 4px !important;
      }
      .${prefixCls}-menu-item-group > .${prefixCls}-menu-item-group-list > .${prefixCls}-menu-submenu-selected > .${prefixCls}-menu-submenu-title,
      .${prefixCls}-menu-submenu-selected > .${prefixCls}-menu-submenu-title {
        background-color: ${menuToken?.colorBgMenuItemSelected};
        border-radius: ${token.borderRadiusLG}px;
      }

      .${group} {
        .${prefixCls}-menu-item-group-title {
          padding-inline: 0px;
        }
      }
    `,
    menuItem: css`
      transition: none !important;
    `,
    submenuHasIcon: css`
      > .${prefixCls}-menu-sub {
        padding-inline-start: 10px;
      }
    `,
    submenu: css``,
    itemTitle: css`
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: ${token.marginXS}px;

      .${itemText} {
        max-width: 100%;
        word-break: break-all;
        white-space: nowrap;
      }

      &.${itemTitleCollapsed} {
        .${itemIcon} {
          height: 16px;
          width: 16px;
          line-height: 16px !important;

          .anticon {
            line-height: 16px !important;
            height: 16px;
          }
        }
        .${itemTextHasIcon} {
          display: none !important;
        }
      }
    `,
    itemTitleCollapsed,
    itemCollapsedShowTitle: css`
      line-height: 16px;
      gap: 0;

      &.${itemTitleCollapsed} {
        display: flex;
      }

      .${itemText} {
        opacity: 1 !important;
        display: inline !important;
        text-align: center;
        font-size: 12px;
        height: 12px;
        line-height: 12px;
        white-space: nowrap;
        width: 100%;
        margin: 0;
        padding: 0;
        margin-block-start: 4px;
      }
    `,
    itemTitleCollapsedLevel0: css`
      flex-direction: column;
      justify-content: center;
    `,
    itemText,
    itemIcon,
    itemTextHasIcon,
    itemLink: css``,
    icon: css``,
    group,
    groupItemTitle: css`
      height: 18px;
      gap: ${token.marginXS}px;
      overflow: hidden;
    `,
    groupDivider: css`
      color: ${token.colorTextSecondary};
      font-size: 12px;
      line-height: 20px;
    `,
  };
});


// 按钮
const useBtnStyles = createStyles(({ token, css }) => ({
  btnIcon: css`
    position: absolute;
    width: 24px;
    height: 24px;
    font-size: 14px;
    text-align: center;
    border-radius: 40px;
    z-index: 101;
    inset-block-start: 18px;
    inset-inline-end: -13px;
    transition: transform 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: ${token.sider?.colorTextCollapsedButton};
    background-color: ${token.sider?.colorBgCollapsedButton};
    box-shadow: 0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08);

    &:hover {
      color: ${token.sider?.colorTextCollapsedButtonHover};
      box-shadow: 0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08);
    }

    .anticon {
      font-size: 14px;
    }
    svg {
      transition: transform  0.3s;
      // transform: rotate(90deg);
    }
  `,
  btnIconCollapsed: css`
    svg {
      transform: rotate(-180deg);
    }
  `,
  btnIconIsMobile: css``,
}));

export {
  useStyles,
  useMenuStyles,
  useBtnStyles,
};
