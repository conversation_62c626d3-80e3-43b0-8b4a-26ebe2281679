import { useState, useEffect } from 'react';
import { Space, Popover, Button } from 'antd';
import { DoubleRightOutlined } from '@ant-design/icons';

import { useFormattedMessage } from '@titd/publics/utils';

const LinkItem = (link, index, FormattedMsg) => link ? (
  <Button
    key={index}
    icon={link.icon ? link.icon : null}
    onClick={link.onClick}
    size="small"
    color="default"
    variant="link"
    {...link.style}
  >{FormattedMsg(link.id, link.name)}</Button>
) : '';

const ActionLinks = props => {
  const { links } = props;
  const [dom, setDom] = useState(null);
  const { FormattedMsg } = useFormattedMessage();

  useEffect(() => {
    const linkLen = links?.length || 0;

    if (linkLen > 3) {
      const newLinks = links.splice(2);
      // console.log(newLinks, links);

      const content = (
        <nav style={{display: 'flex', flexDirection: 'column'}}>
          {newLinks.map((link, index) => LinkItem(link, index, FormattedMsg))}
        </nav>
      );

      setDom(<>
        {links.map((link, index) => LinkItem(link, index, FormattedMsg))}
        <Popover content={content}>
          <Button
            icon={<DoubleRightOutlined />}
            onClick={e => e.stopPropagation()}
            size="small"
            color="default"
            variant="link"
          >{FormattedMsg('actions.more')}</Button>
        </Popover>
      </>);
    } else {
      setDom(links.map((link, index) => LinkItem(link, index, FormattedMsg)));
    }
  }, [links])

  return (
    <Space size="0" wrap>
      {dom}
    </Space>
  );
}

export { ActionLinks };
