import { Popover, Typography, Tag } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

import { dicts } from '@titd/publics/utils';

// import TitdErrorCode from '../../../assets/titderrorcode.json';

const { Text } = Typography;
const { exchangeCodeDict, exchangeC2NDict } = dicts;

const ErrorMsg = ({ titdErrorCode, errcode, exchangeid }) => {
  if (errcode === 0) {
    return (<Tag color="green">成功</Tag>);
  }

  const errMsgArr = titdErrorCode.filter(item => item.id === Number(errcode));
  // console.log(errMsgArr, exchangeid);

  let errMsg;
  if (errMsgArr.length === 1 && exchangeCodeDict.indexOf(errMsgArr[0]?.exchange) === -1) {
    errMsg = errMsgArr[0];
  } else {
    const exchangeCode = exchangeCodeDict[exchangeid];
    errMsg = errMsgArr.find(i => i.exchange === exchangeCode);
  }

  return errMsg ? (<>
    <Tag color="red">{errcode}</Tag>
    <Popover
      title={<>
        <Text strong>{errMsg.platform}</Text>
        <Text type="secondary" style={{ marginLeft: '12px' }}>{exchangeC2NDict[errMsg.exchange] || errMsg.exchange}</Text>
      </>}
      content={errMsg.message}
    >
      <QuestionCircleOutlined />
    </Popover>
  </>) : (
    <Tag color="red">{errcode}</Tag>
  );
}

export default ErrorMsg;
