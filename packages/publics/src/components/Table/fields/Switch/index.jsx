import { Switch } from 'antd';

const switchConfirm = ({ modal, name, onOk }) => modal.confirm({
  title: `确定更改“ ${name} ”状态吗？`,
  okText: '确定',
  cancelText: '取消',
  onOk: onOk,
});

const TableSwitch = (modal, text, record, name, onChange, dict, checked, otherProps) => (
  <Switch
    { ...otherProps }
    checked={text === checked}
    checkedChildren={dict[checked]}
    unCheckedChildren={dict[checked ? 0 : 1]}
    onClick={checked => switchConfirm({
      modal,
      name: record[name],
      onOk: () => onChange(record, checked)
    })}
  />
)

export default TableSwitch;
