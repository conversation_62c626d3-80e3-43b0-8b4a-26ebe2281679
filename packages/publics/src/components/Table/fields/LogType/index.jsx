import { Tag } from 'antd';

function getColor(text) {
  // 根据匹配的内容返回不同的颜色
  if ((/新增/).test(text)) {
    return 'green';  // 新增 -> 绿色
  } else if ((/修改/).test(text)) {
    return 'blue';   // 修改 -> 蓝色
  } else if ((/删除/).test(text)) {
    return 'red';    // 删除 -> 红色
  } else {
    return 'purple';   // 默认没有匹配 -> 紫色
  }
}

const TableLogType = (text, menuMap) => {
  if (menuMap) {
    const logType = menuMap[text] || text;
    const color = getColor(logType);

    return (<Tag color={color}>{logType}</Tag>);
  } else {
    return text;
  }
}

export default TableLogType;
