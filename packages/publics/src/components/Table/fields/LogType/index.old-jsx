import { Tag } from 'antd';

import { logTypeColor, logGroupDict, logTypeDict } from '~/utils/dicts';

const TableLogType = text => {
  const logType = logTypeDict[text] || text;

  if (Array.isArray(logType)) {
    const logColor = logTypeColor[logType[2]];
    const logText = logGroupDict[logType[0]] ? `${logGroupDict[logType[0]]} - ${logType[1]}` : logType[1];

    if (!logColor) {
      return (<Tag color="orange">{logText}</Tag>);
    }

    return (
      <Tag color={logColor[1]}>{`${logText} (${logColor[0]})`}</Tag>
    );
  }

  return (<Tag color="purple">{logType}</Tag>);
}

export default TableLogType;
