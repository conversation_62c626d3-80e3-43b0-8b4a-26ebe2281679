const ColumnTitle = (columns, addName) => columns.map(item => {
  let title = item.title;

  // 子列表
  if (item.children) {
    return ColumnTitle(item.children, item.short);
  }

  // 去除序号列、操作列
  if (!title || item.dataIndex === 'actions') {
    return null;
  }

  // React元素的提取文字
  if (typeof title === 'object') {
    title = title.props.short || title.props.title;
  }

  return addName ? addName + title : title;
}).filter(i => i);

export default ColumnTitle;
