import { Space, Button } from 'antd';
import { FormattedMessage } from 'react-intl';

const BtnItem = (btn, index) => (
  <Button
    key={index}
    icon={btn.icon ? btn.icon : null}
    onClick={btn.onClick}
    disabled={btn.disabled}
    {...btn.style}
  >
    <span><FormattedMessage id={btn.id} defaultMessage={btn.name} /></span>
  </Button>
);

const ActionBtns = ({ btns }) => (
  <Space wrap>
    {btns.map((btn, index) => BtnItem(btn, index))}
  </Space>
);

export { ActionBtns };
