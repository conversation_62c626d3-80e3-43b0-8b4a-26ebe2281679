import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ token, css, prefixCls }) => ({
  table: css`
    .${prefixCls}-table-content {
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        &:hover {
          background-color: rgba(0, 0, 0, 0.4);
        }
      }
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }

    th, td {
      // 边框颜色
      &.ti-group-border {
        border-right-width: 3px !important;
        border-right-style: double !important;
      }
    }
  `,
  tableToolbar: css`
    margin-bottom: 16px;
  `,
}));
