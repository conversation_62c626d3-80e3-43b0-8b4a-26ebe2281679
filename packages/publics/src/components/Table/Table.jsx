import { useState } from 'react';
import { Table, Space, Row, Col, Tooltip } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

import { ActionBtns } from './ActionBtns';
import { DensityButton } from './Density';
// import ColumnSettingButton from './ColumnSetting';

import { consts } from '@titd/publics/utils';

import { useStyles } from './style';

const { DEFAULT_CONFIGS } = consts;

const DEFAULT_TABLE = {
  size: 'small',
}

const TiTable = props => {
  const {
    columns,
    dataSource,
    tableTitle,
    actionBtns = [],
    isLoading,
    expandable,                 // 可展开
    checkable = true,           // 可选择
    rowClickable = false,       // 行可以点击选择
    selectionType = 'checkbox', // 选择类型:多选-checkbox,单选-radio
    selectKeys,                 // 选择行
    setSelectKeys,              // 设定选择行主键（父组件）
    onRefresh,                  // 表格刷新（父组件）
    showPagination = true,      // 分页
    page,
    total,                      // 总条数，分页用
    pageChange,                 // 页数、条数更改（父组件）
    hasToolBar = true,
    className,
    customKey = 'id',           // 自定义主键
    bordered = true,            // 表格边框
    handleChange,               // 筛选、排序、分页
    pageSize = DEFAULT_CONFIGS.LIMIT,
    scroll = { x: 'max-content' },
    ...otherProps
  } = props;

  const { styles, cx } = useStyles();

  const [localPage, setLocalPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [size, setSize] = useState(pageSize);

  const [tableSize, setTableSize] = useState(DEFAULT_TABLE.size);

  // 行可选
  const rowSelection = checkable ? {
    // columnWidth: '36px',
    type: selectionType,
    getCheckboxProps: record => ({
      disabled: record.isDisabled
    }),
    selectedRowKeys: selectKeys,
    // onChange: (selectedRowKeys, selectedRows) => {
    onChange: selectedRowKeys => {
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      setSelectKeys && setSelectKeys(selectedRowKeys);
    },
    // onSelect: (record, selected, selectedRows) => {
    //   console.log(record, selected, selectedRows);
    // },
    // onSelectAll: (selected, selectedRows, changeRows) => {
    //   console.log(selected, selectedRows, changeRows);
    // },
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ]
  } : undefined;

  // 分页
  const pagination = showPagination ? {
    hideOnSinglePage: true,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
    current: page || localPage,
    pageSize: size,
    total: total,
    pageSizeOptions: [15, 30, 60, 100],
    onChange: (newPage, pageSize) => {
      // console.log(page, pageSize);
      // setPage(page);
      if (pageChange) {
        pageChange(newPage, pageSize);
      } else {
        setLocalPage(page);
      }
    },
    onShowSizeChange: (current, size) => {
      // console.log(current, size);
      setSize(size);
      pageChange && pageChange(current, size);
    }
  } : false;

  // 表格行点击
  const rowActions = record => rowClickable ? ({
    onClick: () => {
      // console.log(customKey, record, selectKeys);
      if (record.isDisabled) {
        return;
      }

      const key = record[customKey];
      const keyIdx = selectKeys.indexOf(key);
      if (selectionType === 'checkbox') {
        if (keyIdx !== -1) {
          selectKeys.splice(keyIdx, 1);
        } else {
          selectKeys.push(key);
        }

        setSelectKeys([...selectKeys]);
      } else if (selectionType === 'radio') {
        if (keyIdx === -1) {
          setSelectKeys([key]);
        }
      }
    },
    // onDoubleClick: event => {
    //   console.log(event);
    // },
    // onContextMenu: event => {},
    // onMouseEnter: event => {}, // 鼠标移入行
    // onMouseLeave: event => {},
  }) : {};

  // 行样式
  // const getRowClassName = (record, index) => {
  //   return index % 2 === 0 ? 'oddRow' : 'evenRow';
  // }

  return (<>
    {/* 操作区 */}
    {hasToolBar && (
      <div className={styles.tableToolbar}>
        <Row
          gutter={[8, 12]}
          justify="space-between"
          align="bottom"
        >
          <Col>
            {/* Title */}
            {tableTitle}
            {/* Action Buttons */}
            {actionBtns && <ActionBtns btns={actionBtns} />}
          </Col>

          <Col flex="1" style={{ textAlign: 'right' }}>
            <Space className="ti-tool-button" size="middle" align="center">
              <Tooltip title="刷新" onClick={() => onRefresh()}>
                <ReloadOutlined />
              </Tooltip>
              <DensityButton defaultSize={DEFAULT_TABLE.size} setSize={setTableSize} />
              {/* <ColumnSettingButton columns={columns} /> */}
            </Space>
          </Col>
        </Row>
      </div>
    )}

    {/* 选择提示 */}
    {/*{(checkable && selectionType === 'checkbox' && selectKeys?.length > 0) && (
      <div className="ti-table-toolbar">
        <Alert
          showIcon
          type="info"
          message={<>
            {'已选择 '}
            <Link strong>{selectKeys.length}</Link>
            {' 项'}
            <Link style={{ marginLeft: '24px' }} onClick={() => setSelectKeys([])}>清空</Link>
          </>}
        />
      </div>
    )}*/}

    {/* 表格主体 */}
    <Table
      { ...otherProps }
      className={cx(styles.table, className)}
      columns={columns}
      dataSource={dataSource}
      rowKey={record => record[customKey]}
      rowSelection={rowSelection}
      rowClassName={record => record.rowClassName || ''}
      scroll={scroll}
      loading={isLoading}
      pagination={pagination}
      size={tableSize}
      expandable={expandable}
      bordered={bordered}
      onRow={rowActions}
      onChange={handleChange}
    />
  </>);
}

export { TiTable };
