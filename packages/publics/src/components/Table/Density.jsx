import { Dropdown, Tooltip } from 'antd';
import { ColumnHeightOutlined } from '@ant-design/icons';

const densityItems = [{
  label: '默认',
  key: 'default'
}, {
  label: '中等',
  key: 'middle'
}, {
  label: '紧凑',
  key: 'small'
}];

const DensityButton = props => {
  const { defaultSize, setSize } = props;

  return (
    <Dropdown
      trigger={['click']}
      menu={{
        selectable: true,
        items: densityItems,
        defaultSelectedKeys: [defaultSize],
        onClick: ({ key }) => setSize(key),
      }}
    >
      <Tooltip title="密度">
        <ColumnHeightOutlined />
      </Tooltip>
    </Dropdown>
  );
}

export { DensityButton };
