import { useRef, useState, useEffect, useMemo } from 'react';
import { Popover, Tooltip, Checkbox, Tree, Typography } from 'antd';
import {
  MenuOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignBottomOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';

import { genColumnKey } from './utils';

import './index.less';

const { Link } = Typography;

const ToolTipIcon = props => {
  const {
    title,
    show,
    children,
    columnKey,
    fixed,
  } = props;

  if (!show) {
    return null;
  }

  return (
    <Tooltip title={title}>
      <span onClick={e => {
        e.stopPropagation();
        e.preventDefault();
        console.log(columnKey);
      }}>
        {children}
      </span>
    </Tooltip>
  );
}

const CheckboxListItem = props => {
  const {
    columnKey,
    isLeaf,
    title,
    columnClassName,
    fixed
  } = props;

  const dom = (
    <span className={`${columnClassName}-list-item-option`}>
      <ToolTipIcon
        columnKey={columnKey}
        fixed="left"
        title="固定在列首"
        show={fixed !== 'left'}
      >
        <VerticalAlignTopOutlined />
      </ToolTipIcon>
      <ToolTipIcon
        columnKey={columnKey}
        fixed={undefined}
        title="不固定"
        show={!!fixed}
      >
        <VerticalAlignMiddleOutlined />
      </ToolTipIcon>
      <ToolTipIcon
        columnKey={columnKey}
        fixed="right"
        title="固定在列尾"
        show={fixed !== 'right'}
      >
        <VerticalAlignBottomOutlined />
      </ToolTipIcon>
    </span>
  );

  return (
    <span className={`${columnClassName}-list-item`} key={columnKey}>
      <div className={`${columnClassName}-list-item-title`}>{title}</div>
      {!isLeaf ? dom : null}
    </span>
  );
}

const CheckboxList = props => {
  const {
    list,
    draggable,
    checkable,
    columnClassName,
    showTitle = true,
    title: listTitle
  } = props;

  const show = list && list.length > 0;

  if (!show) {
    return null;
  }

  const listDom = (
    <Tree
      itemHeight={24}
      height={280}
      blockNode
      showLine={false}
      draggable={draggable}
      onDrop={(info) => {
        console.log(info);
      }}
      checkable={checkable}
      onCheck={(_, e) => {
        console.log(e);
      }}
      treeData={list}
      titleRender={(node) => (
        <CheckboxListItem
          columnClassName={columnClassName}
          {...node}
          children={undefined}
          columnKey={node.key}
        />
      )}
    />
  );

  return (
    <>
      {showTitle && <span className={`${columnClassName}-list-title`}>{listTitle}</span>}
      {listDom}
    </>
  )
}

const GroupCheckboxList = ({ localColumns, columnClassName, draggable, checkable }) => {
  const rightList = [];
  const leftList = [];
  const list = [];

  localColumns.forEach(item => {
    if (item.hideInSetting) {
      return;
    }

    const { fixed } = item;
    if (fixed === 'left') {
      leftList.push(item);
      return;
    }
    if (fixed === 'right') {
      rightList.push(item);
      return;
    }
    list.push(item);
  });

  console.log(rightList, leftList, list);
  const showRight = rightList && rightList.length > 0;
  const showLeft = leftList && leftList.length > 0;
  return (
    <div className={classNames(
      `${columnClassName}-list`,
      {[`${columnClassName}-list-group`]: showRight || showLeft},
    )}>
      <CheckboxList
        title="固定在左侧"
        list={leftList}
        draggable={draggable}
        checkable={checkable}
        columnClassName={columnClassName}
      />
      <CheckboxList
        title="不固定"
        showTitle={showLeft || showRight}
        list={list}
        draggable={draggable}
        checkable={checkable}
        columnClassName={columnClassName}
      />
      <CheckboxList
        title="固定在右侧"
        list={rightList}
        draggable={draggable}
        checkable={checkable}
        columnClassName={columnClassName}
      />
    </div>
  );
}

const ColumnSettingButton = props => {
  const columnRef = useRef({});
  const localColumns = props.columns;
  const { checkedReset = true } = props;
  const columnClassName = 'ti-column-setting';

  const defaultColumnKeyMap = useMemo(() => {
    const columnKeyMap = {};
    props.columns?.forEach(({ key, fixed }, index) => {
      const columnKey = genColumnKey(key, index);
      if (columnKey) {
        columnKeyMap[columnKey] = {
          show: true,
          fixed,
        };
      }
    });
    return columnKeyMap;
  }, [props.columns]);

  const [columnsMap, setColumnsMap] = useState(defaultColumnKeyMap);

  useEffect(() => {
    if (columnsMap) {
      columnRef.current = JSON.parse(JSON.stringify(columnsMap));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 设置全部选中，或全部未选中
  const setAllSelectAction = (show = true) => {
    const columnKeyMap = {};
    const loopColumns = columns => {
      columns.forEach(({ key, fixed, index, children }) => {
        const columnKey = genColumnKey(key, index);
        if (columnKey) {
          columnKeyMap[columnKey] = {
            show,
            fixed,
          };
        }

        if (children) {
          loopColumns(children);
        }
      });
    };
    loopColumns(localColumns);
    setColumnsMap(columnKeyMap);
  }

  // 未选中的 key 列表
  const unCheckedKeys = Object.values(columnsMap).filter(value => !value || value.show === false);

  // 是否已经选中
  const indeterminate = unCheckedKeys.length > 0 && unCheckedKeys.length !== localColumns.length;

  return (
    <Popover
      arrowPointAtCenter
      title={
        <div className={`${columnClassName}-title`}>
          <Checkbox
            indeterminate={indeterminate}
            checked={unCheckedKeys.length === 0 && unCheckedKeys.length !== localColumns.length}
            onChange={e => {
              if (e.target.checked) {
                setAllSelectAction();
              } else {
                setAllSelectAction(false);
              }
            }}
          >列展示</Checkbox>
          {checkedReset ? (
            <Link
              className={`${columnClassName}-ation-rest-button`}
              onClick={() => {
                setColumnsMap(columnRef.current);
              }}
            >重置</Link>
          ) : null}
        </div>
      }
      overlayClassName={`${columnClassName}-overlay`}
      trigger="click"
      placement="bottomRight"
      content={
        <GroupCheckboxList
          checkable={props.checkable ?? true}
          draggable={props.draggable ?? true}
          columnClassName={columnClassName}
          localColumns={localColumns}
        />
      }
    >
      <Tooltip title="列设置">
        <MenuOutlined />
      </Tooltip>
    </Popover>
  );
}

export default ColumnSettingButton;
