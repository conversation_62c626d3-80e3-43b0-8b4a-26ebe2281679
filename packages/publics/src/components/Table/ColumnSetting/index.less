@import "~antd/lib/style/themes/default.less";

@ti-column-setting: ~'ti-column-setting';

.@{ti-column-setting} {
  width: auto;

  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
  }

  &-overlay {
    .@{ant-prefix}-popover-inner-content {
      width: 210px;
      padding: 0 0 8px;
    }

    .@{ant-prefix}-tree-node-content-wrapper:hover {
      background-color: transparent;
    }

    .@{ant-prefix}-tree-treenode {
      align-items: center;

      &:hover {
        background-color: @item-active-bg;
        .@{ti-column-setting}-list-item-option {
          display: block;
        }
      }

      // .@{ant-prefix}-tree-switcher {
      //   width: 12px;
      // }

      .@{ant-prefix}-tree-checkbox {
        top: 0;
        margin: 0;
        margin-right: 4px;
      }
    }
  }
}

.@{ti-column-setting}-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-top: 8px;

  &.@{ti-column-setting}-list-group {
    padding-top: 0;
  }

  &-title {
    margin-top: 6px;
    margin-bottom: 6px;
    padding-left: 24px;
    color: @text-color-secondary;
    font-size: 12px;
  }

  &-item {
    display: flex;
    align-items: center;

    &-title {
      flex: 1;
    }

    &-option {
      display: none;
      float: right;
      cursor: pointer;
      > span {
        > span.anticon {
          color: @primary-color;
        }
      }
      > span + span {
        margin-left: 8px;
      }
    }
  }
}
