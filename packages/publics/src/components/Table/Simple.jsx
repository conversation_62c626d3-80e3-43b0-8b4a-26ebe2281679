import { Table } from 'antd';

export const TableSimple = props => {
  const {
    columns,
    dataSource,
    customKey,
    bordered = true,
    title,
    footer,
    isLoading,
    scroll = { x: 'max-content' },
    className = 'ti-simple-table',
    tableSize = 'small',
    rowClassName,
    expandable,
    pagination = false,
  } = props;

  return (
    <Table
      className={className}
      columns={columns}
      dataSource={dataSource}
      rowKey={record => record[customKey] || record.id}
      loading={isLoading}
      pagination={pagination}
      scroll={scroll}
      bordered={bordered}
      title={title}
      footer={footer}
      size={tableSize}
      rowClassName={rowClassName}
      expandable={expandable}
    />
  );
}
