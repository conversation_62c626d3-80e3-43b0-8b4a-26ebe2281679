import { useState, useEffect } from 'react';
import { Tree, Spin } from 'antd';

import TiFieldCheckbox from '../Checkbox';

import useStyles from './style';

const TiFieldTree = props => {
  const {
    value,
    onChange,
    treeData,
    controlDefault,
    fieldNames,
    defaultChecked,
    defaultDisabled,
    isTree = true,
  } = props;

  const { styles } = useStyles();

  const { key, children } = fieldNames;

  const [allExpandKeys, setAllExpangKeys] = useState([]);
  const [allKeys, setAllKeys] = useState([]);

  const [expandKeys, setExpandKeys] = useState([]);
  // const [checkStrictly, setCheckStrictly] = useState(false);
  const [checkKeys, setCheckKeys] = useState([]);

  const optionGroup = [isTree ? {
    label: '展开/折叠',
    value: 1,
    onChange: e => setExpandKeys(e.target.checked ? allExpandKeys : []),
  } : null, {
    label: '全选/全不选',
    value: 2,
    onChange: e => onCheck({
      checked: e.target.checked ? allKeys : defaultChecked,
    }),
  // }, {
  //   label: '父子联动',
  //   value: 3,
  //   onChange: e => {
  //     setCheckStrictly(!e.target.checked);
  //     setCheckKeys([]);
  //   },
  }].filter(i => i);

  useEffect(() => {
    const exKeys = [];
    const cxKeys = [];
    const loop = data => {
      data.map(item => {
        cxKeys.push(item[key]);
        if (item[children]?.length > 0) {
          exKeys.push(item[key]);
          loop(item[children]);
        }

        if (defaultDisabled && defaultDisabled.length > 0) {
          if (defaultDisabled.includes(item[key])) {
            item.disableCheckbox = true;
          }
        }
        return item;
      });
    }

    loop(treeData);

    // console.log(exKeys, cxKeys);

    if (exKeys.length > 0) {
      setAllExpangKeys(exKeys);
      setExpandKeys(exKeys);
    }
    setAllKeys(cxKeys);
  }, [treeData]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (value && value.length > 0) {
      setCheckKeys(value);
    }
  }, [value]);

  function onExpand(expandedKeys, { expanded, node }) {
    // console.log('expand:',expandedKeys, expanded, node);
    if (expanded) {
      expandedKeys.push(node[key]);
    } else {
      const idx = expandedKeys.indexOf(node[key]);
      if (idx > -1) {
        expandedKeys.splice(idx, 1);
      }
    }
    setExpandKeys([...expandedKeys]);
  }
  // function onCheck(checkedKeys, { checked, node }) {
  function onCheck(checkedKeys) {
    // console.log('check:', checkedKeys, checked, node);
    setCheckKeys(checkedKeys);

    // 父子不联动需要添加标识位
    onChange(checkedKeys);
  }

  return (<>
    <div className={styles.treeControl}>
      <TiFieldCheckbox
        options={optionGroup}
        defaultValue={controlDefault || [1]}
      />
    </div>
    <div className={styles.treeContent}>
      {props.treeData?.length > 0 ? (
        <Tree
          checkable
          height={233}
          showLine={{ showLeafIcon: false }}
          // checkStrictly={checkStrictly}
          checkStrictly={true}
          expandedKeys={expandKeys}
          onExpand={onExpand}
          checkedKeys={checkKeys}
          onCheck={onCheck}
          { ...props }
        />
      ) : <Spin />}
    </div>
  </>);
}

export default TiFieldTree;
