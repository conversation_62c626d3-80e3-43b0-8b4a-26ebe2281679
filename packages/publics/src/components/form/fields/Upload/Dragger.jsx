import { Upload } from 'antd';
import { InboxOutlined } from '@ant-design/icons';

const { Dragger } = Upload;

const TiFieldUploadDragger = props => (
  <Dragger {...props}>
    <p className="ant-upload-drag-icon">
      <InboxOutlined />
    </p>
    {/*<p className="ant-upload-text">单击或拖动文件到此区域进行上传</p>*/}
    <p className="ant-upload-hint">单击或拖动文件到此区域进行上传</p>
    {/*<p className="ant-upload-hint">严禁上传公司数据或其他机密文件</p>*/}
  </Dragger>
);

export default TiFieldUploadDragger;
