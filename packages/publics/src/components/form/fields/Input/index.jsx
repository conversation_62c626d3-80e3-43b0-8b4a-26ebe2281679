/**
 * 文本组件
 * @type: text, search, url, tel, email, date, month, week, time, datetime-local, number, range, color
 */
import { useMemo } from 'react';
import { Input } from 'antd';

import { useStyles } from './style';

const TiFieldInput = props => {
  const { styles } = useStyles();

  const className = useMemo(() => {
    return props.readOnly ? styles.readonlyInput : '';
  }, [props?.readOnly, styles]);

  return (
    <Input
      allowClear
      className={className}
      placeholder={props?.disabled ? '' : '请输入'}
      {...props}
    />
  );
}

export default TiFieldInput;
