import { useState, useEffect } from 'react';
import { Row, Col, Form, Slider, InputNumber } from 'antd';

const TiFieldSlider = ({ props, otherProps }) => {
  const form = Form.useFormInstance();
  const formValue = Form.useWatch(otherProps.name, form);
  const [inputValue, setInputValue] = useState(formValue);

  useEffect(() => {
    setInputValue(formValue);
  }, [formValue]);

  const onChange = value => {
    const numericValue = Number(value);
    if (isNaN(numericValue)) {
      return;
    }

    setInputValue(numericValue);
    form.setFieldsValue({
      [otherProps.name]: numericValue,
    });
  };

  return (
    <Row gutter={[16, 16]}>
      <Col span={18}>
        <Form.Item
          noStyle
          { ...otherProps }
        >
          <Slider
            value={typeof inputValue === 'number' ? inputValue : 0}
            onChange={onChange}
            { ...props }
          />
        </Form.Item>
      </Col>
      <Col span={6}>
        <InputNumber
          min={props.min}
          max={props.max}
          step={props.step}
          disabled={props.disabled}
          style={{ width: '100%' }}
          value={inputValue}
          onChange={onChange}
        />
      </Col>
    </Row>
  );
}

export default TiFieldSlider;
