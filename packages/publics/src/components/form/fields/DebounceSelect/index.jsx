import { useState } from 'react';
import { Select, Spin } from 'antd';
import { useDebounceFn } from 'ahooks';

const TiFieldDebounceSelect = ({
  fetchOptions,
  fetching,
  debounceTimeout = 800,
  ...props
}) => {
  const [options, setOptions] = useState([]);
  const { run } = useDebounceFn((value) => {
    const resp = fetchOptions(value);

    setOptions(resp);
  }, { wait: debounceTimeout });

  return (
    <Select
      onFocus={run}
      options={options}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      {...props}
    />
  );
}

export default TiFieldDebounceSelect;
