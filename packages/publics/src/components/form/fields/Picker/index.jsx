import { DatePicker, TimePicker } from 'antd';

function PickerWithType({ type = 'date', ...otherProps }) {
  if (type === 'time') return <TimePicker onChange={otherProps.onChange} { ...otherProps } />;
  if (type === 'date') return <DatePicker { ...otherProps } />;
  return <DatePicker picker={type} { ...otherProps } />;
}

const TiFieldPicker = props => (
  <PickerWithType
    style={{ width: '100%' }}
    { ...props }
  />
);

export default TiFieldPicker;
