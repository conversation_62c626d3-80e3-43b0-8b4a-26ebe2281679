import { Select, Row, Col, Typography } from 'antd';

const { Option } = Select;
const { Text } = Typography;

const TiFieldOptionOne = props => {
  const {
    label,
    value,
    name,
  } = props;

  return (
    <Option label={label} value={value}>
      <Row justify="space-between" align="center">
        <Col><Text>{label}</Text></Col>
        {name && <Col><Text type="secondary">{name}</Text></Col>}
      </Row>
    </Option>
  );
}

export default TiFieldOptionOne;
