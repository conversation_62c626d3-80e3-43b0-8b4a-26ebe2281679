export const checkOptions = (dict) => {
  return dict.map((item, idx) => {
    if (item === '' || item === '-') {
      return null;
    }

    return {
      text: item,
      value: idx,
    };
  }).filter(i => i);
}

export const checkOptionsMap = (dict, {isCode, isNum} = {}) => {
  return Object.keys(dict).map(key => {
    if (key === '0') {
      return null;
    }

    let value = key;
    if (isCode) { value = key.charCodeAt() }
    if (isNum) { value = Number(key) }

    return {
      text: dict[key],
      value: value,
    };
  }).filter(i => i);
}
