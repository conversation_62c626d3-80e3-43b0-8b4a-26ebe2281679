export const formItemLayout = labelWidth => ({
  labelCol: {
    flex: `0 0 ${labelWidth}px`,
  },
  wrapperCol: {
    style: {
      maxWidth: `calc(100% - ${labelWidth}px)`,
    },
  },
  style: {
    flexWrap: 'nowrap',
  },
});

export const tailFormItemLayout = labelWidth => ({
  wrapperCol: {
    style: {
      marginLeft: `${labelWidth}px`,
      maxWidth: `calc(100% - ${labelWidth}px)`,
    },
  },
  style: {
    flexWrap: 'nowrap',
  },
});
