export const selectOptions = dict => {
  return dict.map((item, idx) => {
    if (item === '' || item === '-') {
      return null;
    }

    return {
      label: item,
      value: idx,
    };
  }).filter(i => i);
}

export const selectObjOptions = (dict, {isCode, isNum} = {}) => {
  return Object.keys(dict).map(key => {
    if (key === '0') {
      return null;
    }

    let value = key;
    if (isCode) { value = key.charCodeAt() }
    if (isNum) { value = Number(key) }

    return {
      label: dict[key],
      value: value,
    };
  }).filter(i => i);
}

export const selectExchange = (dict, ids) => {
  return dict.map((item, idx) => {
    if (item === '' || item === '-') {
      return null;
    }

    if (ids && ids.indexOf(String(idx)) === -1) {
      return null;
    }

    return {
      label: item,
      value: idx,
    };
  }).filter(i => i);
}

export const selectOptionsStr = dict => {
  return dict.map(item => {
    if (item === '' || item === '-') {
      return null;
    }

    return {
      label: item,
      value: item,
    };
  }).filter(i => i);
}
