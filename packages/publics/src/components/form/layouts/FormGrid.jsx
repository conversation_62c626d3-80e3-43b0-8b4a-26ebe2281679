import { Row, Col } from 'antd';

import TiFormItem from './FormItem';

const layoutSpans = [
  undefined,
  { span: 24 },
  { xs: 24, md: 12 },
  { xs: 24, md: 12, lg: 8 }
];

const FormGrid = ({ formData, spanDefault = 2 }) => (
  <Row gutter={16}>
    {formData && formData.map((item, idx) => {
      if (item.noStyle) {
        return <TiFormItem key={idx} { ...item } />
      }

      let layoutSpan = layoutSpans[item.span || spanDefault];

      return (
        <Col key={idx} {...layoutSpan}>
          <TiFormItem { ...item } />
        </Col>
      );
    })}
  </Row>
)

export default FormGrid;
