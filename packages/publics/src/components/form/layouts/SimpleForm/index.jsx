import { useMemo, useImperativeHandle, forwardRef } from 'react';
import { Form, Grid } from 'antd';
import { useThrottleFn } from 'ahooks';

import FormGrid from '../FormGrid';
import TiFormItem from '../FormItem';
import { formItemLayout, tailFormItemLayout } from '../../utils/form-layout';

const { useBreakpoint } = Grid;

// const itemLayout = {
//   labelCol: { span: 6 },
//   wrapperCol: { span: 16 },
// };
// const tailLayout = {
//   wrapperCol: { offset: 6, span: 16 },
// };

const SimpleForm = (props, ref) => {
  const {
    formData,
    initValues,
    customBtn,
    formStyle,
    formGroup = false,
    spanDefault = 2,
    labelWidth = formGroup ? 135 : 145,
    onValuesChange,
    onFinish,
    children,
  } = props;

  const screens = useBreakpoint();
  const isMobile = (screens.xs === true);

  const [form] = Form.useForm();

  const layout = useMemo(() => isMobile ? 'vertical' : 'horizontal', [isMobile]);

  const formItemLayouts = useMemo(() => {
    if (layout === 'horizontal') {
      return formItemLayout(labelWidth);
    }
    return undefined;
  }, [labelWidth, layout]);
  const formItemTailLayouts = useMemo(() => {
    if (layout === 'horizontal') {
      return tailFormItemLayout(labelWidth);
    }
    return undefined;
  }, [labelWidth, layout]);

  useImperativeHandle(ref, () => ({
    set: (value) => {
      form.setFieldsValue(value);
    },
    get: (name) => {
      return form.getFieldValue(name);
    },
    reset: () => form.resetFields(),
    submit: (func) => run(func),
    validate: form.validateFields,
  }));

  const { run } = useThrottleFn((func) => {
    form.validateFields()
      .then(values => {
        func && func(values);
      }).catch(info => {
        console.log('Validate Failed: ' + info);
      });
  }, { wait: 1000 });

  const createForm = () => {
    if (formGroup) {
      return (
        <FormGrid formData={formData} spanDefault={spanDefault} />
      );
    }

    return formData && formData.map((item, idx) => (
      <TiFormItem key={idx} { ...item } />
    ));
  }

  return (
    <Form
      { ...formItemLayouts }
      form={form}
      name="simple_form"
      autoComplete="off"
      style={formStyle}
      layout={layout}
      initialValues={initValues}
      onValuesChange={onValuesChange}
      onFinish={() => run(onFinish)}
    >
      {createForm()}
      {children}
      {customBtn && (
        <Form.Item {...formItemTailLayouts}>{customBtn}</Form.Item>
      )}
    </Form>
  );
}

export default forwardRef(SimpleForm);
