import { useState, useMemo, useImperativeHandle, forwardRef } from 'react';
import { Modal, Form, Grid } from 'antd';
import { useThrottleFn } from 'ahooks';

import FormGrid from '../FormGrid';
import TiFormItem from '../FormItem';
import { formItemLayout, tailFormItemLayout } from '../../utils/form-layout';

import { useStyles } from './style';

const { useBreakpoint } = Grid;

const formTitle = ['新增', '修改', '重置密码', '修改密码'];

const ModalForm = (props, ref) => {
  const {
    onOk,
    onCancel,
    formData,
    initValues,
    okText,
    formGroup = false,
    spanDefault = 2,
    width = formGroup ? 850 : 680,
    labelWidth = formGroup ? 135 : 145,
    onValuesChange,
    loading = false,
    lateClose = false,
    children,
    ...otherProps
  } = props;

  const { styles } = useStyles();

  const screens = useBreakpoint();
  const isMobile = (screens.xs === true);

  const [form] = Form.useForm();
  const [type, setType] = useState(0);
  const [isOpen, setIsOpen] = useState(false);

  const layout = useMemo(() => isMobile ? 'vertical' : 'horizontal', [isMobile]);

  const formItemLayouts = useMemo(() => {
    if (layout === 'horizontal') {
      return formItemLayout(labelWidth);
    }
    return undefined;
  }, [labelWidth, layout]);

  useImperativeHandle(ref, () => ({
    show: (id, record) => {
      setType(id || 0);
      setIsOpen(true);
      setTimeout(() => {
        form.setFieldsValue(record);
      }, 50);
    },
    set: value => setTimeout(() => { form.setFieldsValue(value) }, 50),
    get: name => setTimeout(() => { form.getFieldValue(name) }, 50),
    validate: form.validateFields,
    close: () => setIsOpen(false),
  }));

  const wrapWithClose = method => {
    // form.resetFields();
    setIsOpen(false);
    method && method();
  }

  const createForm = () => {
    // if (formGroup) {
    //   return formData && formData.map((group, index) => {
    //     return (<div key={index}>
    //       {group.title ? (<Divider
    //         className="no-margin-top"
    //         orientation="left"
    //         plain
    //       >{group.title}</Divider>) : null}
    //       <Row>
    //         {group.content.map((item, idx) => (
    //           <Col key={idx} xs={24} md={12} lg={8}>
    //             <TiFormItem { ...item } />
    //           </Col>
    //         ))}
    //       </Row>
    //     </div>);
    //   });
    // }

    if (formGroup) {
      return (
        <FormGrid formData={formData} spanDefault={spanDefault} />
      );
    }

    return formData && formData.map((item, idx) => {
      if (item.noLabel) {
        item.itemLayout = tailFormItemLayout(labelWidth);
      }

      return (
        <TiFormItem key={idx} { ...item } />
      );
    });
  }

  const { run } = useThrottleFn(() => {
    form.validateFields()
      .then(values => {
        if (lateClose) {
          onOk(values, type);
        } else {
          wrapWithClose(() => onOk(values, type));
        }
      }).catch(info => {
        console.log('Validate Failed: ' + JSON.stringify(info));
      });
  }, { wait: 1000 });

  return (
    <Modal
      destroyOnHidden
      getContainer={false}
      title={formTitle[type] || type}
      open={isOpen}
      confirmLoading={loading}
      okText={okText}
      onOk={run}
      onCancel={() => wrapWithClose(onCancel)}
      width={width}
      { ...otherProps }
    >
      <Form
        { ...formItemLayouts }
        form={form}
        name="modal_form"
        autoComplete="off"
        className={styles.modalForm}
        labelWrap
        layout={layout}
        preserve={false}   // Modal关闭时销毁表单字段数据
        initialValues={initValues}
        onValuesChange={onValuesChange}
      >{createForm()}</Form>
      {children}
    </Modal>
  );
}

export default forwardRef(ModalForm);
