import { useState, useImperativeHandle, forwardRef } from 'react';
import { Drawer, Form, Divider } from 'antd';
import { useThrottleFn } from 'ahooks';

import FormGroup from '../FormGroup';

// import { useStyles } from './style';

const wideLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 14 },
};

const DrawerForm = (props, ref) => {
  const {
    formData,
    initValues,
    formLayout = 'horizontal',
    size = 'large',
    isGroup = false,
    width,
    extra,
    onClose,
    children,
  } = props;

  // const { styles } = useStyles();

  const formItemLayout = formLayout === 'horizontal' ? wideLayout : null;

  const [form] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState('');

  useImperativeHandle(ref, () => ({
    show: (title, record) => {
      title && setTitle(title);
      setIsOpen(true);

      setTimeout(() => {
        record && form.setFieldsValue(record);
      }, 50);
    },
    set: value => form.setFieldsValue(value),
    get: name => form.getFieldValue(name),
    close: func => wrapWithClose(func),
    submit: func => run(func),
    validate: form.validateFields,
  }));

  const wrapWithClose = method => {
    form.resetFields();
    setIsOpen(false);
    method && method();
  }

  const createFormList = (formList, idx) => {
    return (<div key={idx}>
      {formList.label ? <Divider orientation="right">{formList.label}</Divider> : null}
      {formList.children && FormGroup({group: formList.children})}
    </div>)
  }

  const { run } = useThrottleFn(func => {
    form.validateFields()
      .then(values => {
        wrapWithClose(() => func && func(values));
      }).catch(info => {
        console.log('Validate Failed: ' + info);
      });
  }, { wait: 1000 });

  return (
    <Drawer
      title={title}
      // className={styles.drawerForm}
      open={isOpen}
      onClose={() => wrapWithClose(onClose)}
      width={width}
      size={size}
      placement="right"
      extra={extra}
      destroyOnHidden
    >
      <Form
        { ...formItemLayout }
        labelWrap
        form={form}
        name="drawer_form"
        autoComplete="off"
        initialValues={initValues}
      >
        {isGroup ? formData.map((item, idx) => createFormList(item, idx)) : FormGroup({group: formData})}
      </Form>
      {children}
    </Drawer>
  );
}

export default forwardRef(DrawerForm);
