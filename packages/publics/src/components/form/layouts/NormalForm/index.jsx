import { useImperativeHandle, forwardRef } from 'react';
import { Form, Divider } from 'antd';
import { useThrottleFn } from 'ahooks';

import FormGroup from '../FormGroup';

const wideLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 14 },
};

const NormalForm = (props, ref) => {
  const {
    formData,
    initValues,
    formLayout = 'horizontal',
    isGroup = false,
    onFinish,
    children
  } = props;

  const formItemLayout = formLayout === 'horizontal' ? wideLayout : null;

  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    set: value => form.setFieldsValue(value),
    get: name => form.getFieldValue(name),
    submit: func => run(func),
    validate: form.validateFields,
  }));

  const createFormList = (formList, idx) => {
    return (<div key={idx}>
      {formList.label ? <Divider orientation="right">{formList.label}</Divider> : null}
      {formList.children && FormGroup({group: formList.children})}
    </div>)
  }

  const { run } = useThrottleFn((func) => {
    form.validateFields()
      .then(values => {
        func && func(values);
      }).catch(info => {
        console.log('Validate Failed: ' + info);
      });
  }, { wait: 1000 });

  return (
    <Form
      { ...formItemLayout }
      form={form}
      name="normal_form"
      autoComplete="off"
      layout={formLayout}
      initialValues={initValues}
      onFinish={() => run(onFinish)}
    >
      {isGroup ? formData.map((item, idx) => createFormList(item, idx)) : FormGroup({group: formData})}
      {children}
    </Form>
  );
}

export default forwardRef(NormalForm);
