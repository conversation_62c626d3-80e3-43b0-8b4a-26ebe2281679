/**
 * 表单组件包裹
 * @valueType text, textarea, password, select, checkbox, radio, cascader, switch, slider
*/

import { Form } from 'antd';

import TiFieldInput from '../fields/Input';
import TiFieldTextArea from '../fields/Textarea';
import TiFieldPassword from '../fields/Password';
import TiFieldNumber from '../fields/Number';
import TiFieldSelect from '../fields/Select';
import TiFieldCheckbox from '../fields/Checkbox';
import TiFieldRadio from '../fields/Radio';
import TiFieldSwitch from '../fields/Switch';
import TiFieldAutoComplete from '../fields/AutoComplete';
import TiFieldPicker from '../fields/Picker';
import TiFieldRange from '../fields/Picker/Range';
import TiFieldTreeSelect from '../fields/TreeSelect';
import TiFieldTree from '../fields/Tree';
import TiFieldUpload from '../fields/Upload';
import TiFieldText from '../fields/Text';
import TiFieldInputGroup from '../fields/InputGroup';
import TiFieldDivider from '../fields/Divider';
import TiFieldHidden from '../fields/Hidden';
import TiFieldPlain from '../fields/Plain';
import TiFieldSelectJsx from '../fields/SelectJsx';
import TiFieldSlider from '../fields/Slider';
import TiFieldDebounceSelect from '../fields/DebounceSelect';
import TiFieldUploadDragger from '../fields/Upload/Dragger';

const TiFormItem = props => {
  const {
    valueType = 'input',
    name,
    label,
    tooltip,
    rules,
    itemLayout,
    otherProps,
    fieldProps,
    noStyle = false,
    extra,
  } = props;

  const valueEvent = () => {
    switch(valueType) {
      case 'input':
        // 去除头尾空格
        return e => e.target.value.trim();
      case 'inputNoTrim':
        // 去除头Tab键空格
        return e => e.target.value.replace(/\t/g, '');
      default:
        return undefined;
    }
  }

  const setField = () => {
    switch(valueType) {
      case 'input':
      case 'inputNoTrim':
        return <TiFieldInput {...fieldProps} />;
      case 'textarea':
        return <TiFieldTextArea {...fieldProps} />;
      case 'password':
        return <TiFieldPassword {...fieldProps} />;
      case 'number':
        return <TiFieldNumber {...fieldProps} />;
      case 'select':
        return <TiFieldSelect {...fieldProps} />;
      case 'checkbox':
        return <TiFieldCheckbox {...fieldProps} />;
      case 'radio':
        return <TiFieldRadio {...fieldProps} />;
      case 'switch':
        return <TiFieldSwitch {...fieldProps} />;
      case 'autocomplete':
        return <TiFieldAutoComplete {...fieldProps} />;
      case 'picker':
        return <TiFieldPicker {...fieldProps} />;
      case 'range':
        return <TiFieldRange {...fieldProps} />;
      case 'treeSelect':
        return <TiFieldTreeSelect {...fieldProps} />;
      case 'tree':
        return <TiFieldTree {...fieldProps} />;
      case 'upload':
        return <TiFieldUpload {...fieldProps} />;
      case 'text':
        return <TiFieldText {...fieldProps} />;
      case 'group':
        return <TiFieldInputGroup {...fieldProps} />;
      case 'divider':
        return <TiFieldDivider title={otherProps?.title} {...fieldProps} />;
      case 'hidden':
        return <TiFieldHidden {...fieldProps} />;
      case 'plain':
        return <TiFieldPlain {...fieldProps} />;
      case 'selectJsx':
        return <TiFieldSelectJsx {...fieldProps} />;
      case 'slider':
        return <TiFieldSlider props={fieldProps} otherProps={otherProps} />;
      case 'debounceSelect':
        return <TiFieldDebounceSelect {...fieldProps} />;
      case 'uploadDragger':
        return <TiFieldUploadDragger {...fieldProps} />;
      default:
        return null;
    }
  }

  if (otherProps?.nowrap) {
    return setField();
  }

  return (
    <Form.Item
      name={name}
      label={label}
      tooltip={tooltip}
      rules={rules}
      hasFeedback={!!rules}
      noStyle={noStyle}
      extra={extra}
      getValueFromEvent={valueEvent()}
      { ...itemLayout }
      { ...otherProps }
    >
      {setField()}
    </Form.Item>
  );
}

export default TiFormItem;
