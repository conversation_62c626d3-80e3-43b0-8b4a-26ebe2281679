import { useState, useMemo, useImperative<PERSON>andle, forwardRef } from 'react';
import { Form, Row, Col, Button, Space, Typography } from 'antd';
import { RedoOutlined, SearchOutlined, DownOutlined } from '@ant-design/icons';
import useResizeObserver from 'use-resize-observer';

import TiFormItem from '../FormItem';
import { formItemLayout } from '../../utils/form-layout';

const { Link } = Typography;

// const DEFAULT_BREAKPOINTS = {
//   xs: 480,
//   sm: 576,
//   md: 768,
//   lg: 992,
//   xl: 1200,
//   xxl: 1600,
// };

const CONFIG_SPAN_BREAKPOINTS = {
  xs: 513,
  sm: 513,
  md: 785,
  lg: 992,
  xl: 1057,
  xxl: Infinity,
};
// 配置表单列变化的容器宽度断点
const BREAKPOINTS = {
  vertical: [
    // [breakpoint, cols, layout]
    [513, 1, 'vertical'],
    [785, 2, 'vertical'],
    [1057, 3, 'vertical'],
    [Infinity, 4, 'vertical'],
  ],
  default: [
    [513, 1, 'vertical'],
    [701, 2, 'vertical'],
    [1062, 2, 'horizontal'],
    [1352, 3, 'horizontal'],
    [1928, 4, 'horizontal'],
    [Infinity, 6, 'horizontal'],
  ],
  // default: [
  //   [513, 1, 'vertical'],
  //   [701, 2, 'vertical'],
  //   [1062, 3, 'horizontal'],
  //   [1352, 3, 'horizontal'],
  //   [Infinity, 4, 'horizontal'],
  // ],
};

const getSpanConfig = (layout, width, span) => {
  if (span && typeof span === 'number') {
    return {
      span,
      layout,
    };
  }

  const spanConfig = span ? Object.keys(span).map((key) => [CONFIG_SPAN_BREAKPOINTS[key], 24 / span[key], 'horizontal']) : BREAKPOINTS[layout || 'default'];
  const breakPoint = (spanConfig || BREAKPOINTS.default).find(item => width < item[0] + 16); // 16 = 2 * (ant-row -8px margin)
  return {
    span: 24 / breakPoint[1],
    layout: breakPoint[2],
  };
}

// const defaultWidth = isBrowser() ? document.body.clientWidth : 1024;

const QueryFilter = (props, ref) => {
  const {
    layout,
    defaultCollapsed = false,
    defaultColsNumber,
    span,
    labelWidth = '120',
    formData,
    onSearch,
    onReset,
    isLoading,
    initValues,
    onValuesChange,
    searchText = '查询',
    otherBtns,
  } = props;

  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const [form] = Form.useForm();
  const resize = useResizeObserver({
    onResize: ({ width }) => {
      // console.log(width, 'x', height);
      // console.log(getSpanConfig('', width));
      if (formWidth !== width && width > 17) {
        setFormWidth(width);
      }
    },
  });

  useImperativeHandle(ref, () => ({
    set: value => form.setFieldsValue(value),
    get: name => form.getFieldValue(name),
    submit: func => {
      form.validateFields()
        .then(values => {
          func && func(values);
        })
        .catch(info => {
          console.log('Validate Failed: ' + JSON.stringify(info));
        });
    }
  }));

  // 表单宽度
  const [formWidth, setFormWidth] = useState(1250);
  // 每列占比，label的位置
  const spanSize = useMemo(() => getSpanConfig(layout, formWidth + 16, span), [layout, formWidth, span]);
  // 每行几列
  const showLength = useMemo(() => {
    if (defaultColsNumber !== undefined) {
      return defaultColsNumber;
    }
    return Math.max(1, 24 / spanSize.span);
  }, [defaultColsNumber, spanSize.span]);

  const formItemLayouts = useMemo(() => {
    if (labelWidth && labelWidth !== 'auto' && spanSize.layout !== 'vertical') {
      return formItemLayout(labelWidth);
    }
    return undefined;
  }, [spanSize.layout, labelWidth]);

  // totalSpan 统计控件占的位置，计算 offset 保证查询按钮在最后一列
  let totalSpan = 0;
  let itemLength = 0;

  // for split compute
  let currentSpan = 0;

  const doms = formData.map((formItem, index) => {
    // 如果 formItem 自己配置了 hidden，默认使用它自己的
    const colSize = formItem?.fieldProps?.colSize || 1;
    const colSpan = Math.min(spanSize.span * (colSize || 1), 24);

    // 计算总的 totalSpan 长度
    totalSpan += colSpan;
    const hidden = formItem?.hidden ||
      // 如果收起了
      (collapsed &&
        // 如果 超过显示长度 且 总长度超过了 24
        index >= showLength - 1 &&
        !!index &&
        totalSpan >= 24);

    itemLength += 1;

    if (hidden) {
      return null;
    }

    if (24 - (currentSpan % 24) < colSpan) {
      // 如果当前行空余位置放不下，那么折行
      totalSpan += 24 - (currentSpan % 24);
      currentSpan += 24 - (currentSpan % 24);
    }

    currentSpan += colSpan;

    return (
      <Col key={index} span={colSpan}>
        <TiFormItem { ...formItem } />
      </Col>
    );
  });

  // 是否需要展示 collapseRender
  const needCollapseRender = useMemo(() => {
    if (totalSpan < 24 || itemLength < showLength) {
      return false;
    }
    return true;
  }, [itemLength, showLength, totalSpan]);

  const offset = useMemo(() => {
    const offsetSpan = (currentSpan % 24) + spanSize.span;
    return 24 - offsetSpan;
  }, [currentSpan, spanSize.span]);

  // const items = React.Children.toArray(children).map((item, index) => {
  //   return item;
  // });

  return (
    <div ref={resize.ref}>
      <Form
        form={form}
        name="form_search"
        className="ti-search-form"
        layout={spanSize.layout}
        { ...formItemLayouts }
        labelWrap
        onFinish={(values) => onSearch(values)}
        autoComplete="off"
        initialValues={initValues}
        onValuesChange={onValuesChange}
      >
        <Row gutter={24} justify="start" key="resize-observer-row">
          {doms}
          <Col
            key="submitter"
            span={spanSize.span}
            offset={offset}
            style={{ textAlign: 'right' }}
          >
            <Form.Item
              label={spanSize.span === 24 ? '' : <span>&nbsp;</span>}
              colon={false}
              labelCol={{}}
              wrapperCol={{}}
            >
              <Space>
                <Button
                  icon={<RedoOutlined />}
                  color="default"
                  variant="outlined"
                  onClick={() => {
                    form.resetFields();
                    onReset && onReset();
                  }}
                >重置</Button>
                <Button
                  icon={<SearchOutlined />}
                  color="primary"
                  variant="solid"
                  htmlType="submit"
                  loading={isLoading}
                >{searchText}</Button>
                {otherBtns}
                {needCollapseRender ? (
                  <Button
                    size="small"
                    color="primary"
                    variant="link"
                    icon={<DownOutlined
                      style={{
                        transition: '0.3s all',
                        transform: `rotate(${collapsed ? 0 : 0.5}turn)`,
                      }}
                    />}
                    iconPosition='end'
                    onClick={() => setCollapsed(!collapsed)}
                  >
                    {collapsed ? '展开' : '收起'}
                  </Button>
                ) : null}
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
}

export default forwardRef(QueryFilter);
