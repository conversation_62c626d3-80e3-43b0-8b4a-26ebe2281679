import { useMemo } from 'react';
import { Column } from '@ant-design/charts';

// import { dicts } from '@titd/publics/utils';
// const { exchangeAllDict } = dicts;

const ChartColumn = ({ chartData }) => {
  const config = useMemo(() => ({
    title: {
      visible: true,
      text: '成交汇总',
    },
    data: chartData,
    forceFit: true,
    padding: 'auto',
    xField: 'date',
    yField: 'value',
    colorField: 'type',
    seriesField: 'type',
    group: true,
    groupField: 'type',
    // stack: true,
    // stackField: 'exchange',
    // interaction: {
    //   tooltip: {
    //     render: (e, { title, items }) => (
    //       <div key={title}>
    //         <h4>{title}</h4>
    //         {items.map((item, idx) => {
    //           const { name, value, color } = item;
    //           return (
    //             <div key={idx}>
    //               <div style={{ margin: 0, display: 'flex', justifyContent: 'space-between' }}>
    //                 <div>
    //                   <span
    //                     style={{
    //                       display: 'inline-block',
    //                       width: 6,
    //                       height: 6,
    //                       borderRadius: '50%',
    //                       backgroundColor: color,
    //                       marginRight: 6,
    //                     }}
    //                   ></span>
    //                   <span>{`${name} - ${exchangeAllDict[item.exchange] || item.exchange}`}</span>
    //                 </div>
    //                 <b>{value}</b>
    //               </div>
    //             </div>
    //           );
    //         })}
    //       </div>
    //     )
    //   }
    // },
    label: {
      text: d => d.value,
      textBaseline: 'bottom',
    },
    // label: {
    //   // 可手动配置 label 数据标签位置 'top', 'left', 'right', 'bottom', 'top-left', 'top-right', 'bottom-left', 'bottom-right', 'inside'
    //   position: 'top',
    //   // 可配置附加的布局方法
    //   layout: [{ // 柱形图数据标签位置自动调整
    //     type: 'interval-adjust-position',
    //   }, { // 数据标签防遮挡
    //     type: 'interval-hide-overlap',
    //   }, { // 数据标签文颜色自动调整
    //     type: 'adjust-color',
    //   }],
    // },
  }), [chartData]);

  return (
    <Column {...config} />
  );
}

export { ChartColumn };
