import { useRef, useMemo, useEffect, Suspense } from 'react';
import { useLocation } from 'react-router';
import { useKeepAliveRef } from 'keepalive-for-react';
import KeepAliveRouteOutlet from 'keepalive-for-react-router';

import { PageLoading } from '../PageLoading';

function MemoScrollTopWrapper({ children }) {
  const domRef = useRef(null);
  const location = useLocation();
  const scrollHistoryMap = useRef(new Map());

  const activeKey = useMemo(() => {
    return location.pathname + location.search;
  }, [location.pathname, location.search]);

  useEffect(() => {
    const divDom = domRef.current;
    if (!divDom) return;

    setTimeout(() => {
      divDom.scrollTo(0, scrollHistoryMap.current.get(activeKey) || 0);
    }, 300); // 300 milliseconds to wait for the animation transition ending

    const onScroll = e => {
      const target = e.target;
      if (!target) return;
      scrollHistoryMap.current.set(activeKey, target?.scrollTop || 0);
    };

    divDom?.addEventListener('scroll', onScroll, {
      passive: true,
    });

    return () => {
      divDom?.removeEventListener('scroll', onScroll);
    };
  }, [activeKey]);

  return (
    <div
      style={{
        height: '100%',
      }}
      ref={domRef}
    >{children}</div>
  );
}

function CustomSuspense({ children }) {
  return (
    <Suspense fallback={<PageLoading />}>{children}</Suspense>
  );
}

const KeepAliveDom = () => {
  const aliveRef = useKeepAliveRef();

  return (
    <CustomSuspense>
      <KeepAliveRouteOutlet
        wrapperComponent={MemoScrollTopWrapper}
        duration={50}
        transition={true}
        exclude={[]}
        aliveRef={aliveRef}
      />
    </CustomSuspense>
  );
}

export { KeepAliveDom };
