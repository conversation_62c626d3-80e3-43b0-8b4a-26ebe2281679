import { useNavigate } from 'react-router';
import { Tabs, Dropdown, Space } from 'antd';
import { SettingOutlined, SyncOutlined } from '@ant-design/icons';
import { useKeepAliveContext } from 'keepalive-for-react';

import { useRecoilState } from 'recoil';
import { tabMenuState } from '@titd/publics/states';

import { ActionItem } from '@titd/publics/components';
import { consts } from '@titd/publics/utils';

import { useStyles } from './style';

const { HOME_MENU } = consts;

const menuItems = $t => [{
  label: $t('app.general.tabs.closecurrent'),
  key: '1',
}, {
  label: $t('app.general.tabs.closeothers'),
  key: '2',
}, {
  label: $t('app.general.tabs.closeall'),
  key: '3',
}];

const TabMenu = ({ $t }) => {
  const navigate = useNavigate();

  const { styles } = useStyles();

  const [tabMenu, setTabMenu] = useRecoilState(tabMenuState);
  const { refresh, destroyAll, destroyOther } = useKeepAliveContext();

  const onChange = activeKey => {
    const filterMenu = tabMenu.menuList.find(item => item.key === activeKey);

    if (filterMenu) {
      setTabMenu({
        ...tabMenu,
        activeKey: activeKey,
      });

      navigate(filterMenu.path);
    }
  }

  const onEdit = (targetKey, action) => {
    if (action === 'remove') {
      tabRemove(targetKey);
    }
  }

  const tabRemove = targetKey => {
    const menuList = [...tabMenu.menuList];
    const menuIndex = menuList.findIndex(item => item.key === targetKey);

    if (menuIndex !== -1) {
      if (targetKey === tabMenu.activeKey) {
        const activeMenu = menuIndex === menuList.length - 1 ? menuList[menuIndex - 1] : menuList[menuIndex + 1];

        // 删除此项
        menuList.splice(menuIndex, 1);

        setTabMenu({
          menuList: menuList,
          activeKey: activeMenu.key,
        });

        navigate(activeMenu.path);
      } else {
        // 删除此项
        menuList.splice(menuIndex, 1);

        setTabMenu({
          ...tabMenu,
          menuList: menuList,
        });
      }

      destroyOther && destroyOther(targetKey);
    }
  }

  const tabRemoveOthers = () => {
    // console.log('Remove Others');
    const activeMenu = tabMenu.menuList.find(item => item.key === tabMenu.activeKey);

    setTabMenu({
      ...tabMenu,
      menuList: [
        HOME_MENU,
        activeMenu,
      ],
    })
  }

  const tabRemoveAll = () => {
    // console.log('Remove All');
    setTabMenu({
      menuList: [HOME_MENU],
      activeKey: HOME_MENU.key,
    });

    destroyAll();

    navigate(HOME_MENU.path);
  }

  const onClickHover = ({ key }) => {
    switch (key) {
      case '1':
        if (tabMenu.activeKey !== 'dashboard') {
          tabRemove(tabMenu.activeKey);
        }
        break;
      case '2':
        tabRemoveOthers();
        break;
      case '3':
        tabRemoveAll();
        break;
      default:
        break;
    }
  };

  const actionArray = [{
    title: $t('app.general.tabs.reload'),
    onClick: () => refresh(tabMenu.activeKey),
    children: <SyncOutlined />
  }, {
    children: (
      <Dropdown menu={{
        items: menuItems($t),
        onClick: onClickHover
      }}><SettingOutlined /></Dropdown>
    )
  }];

  const operations = (
    <Space size={0}>
      {actionArray.map((item, index) => (
        <ActionItem key={index} {...item} />
      ))}
    </Space>
  );

  return (
    <Tabs
      hideAdd
      type="editable-card"
      className={styles.tabmenu}
      // tabBarStyle={{ backgroundColor: '#fff' }}
      tabPosition="top"
      tabBarGutter={-1}
      tabBarExtraContent={operations}
      activeKey={tabMenu.activeKey}
      onChange={onChange}
      onEdit={onEdit}
      items={tabMenu.menuList?.map(menu => ({
        // label: $t(menu.nameid, {
        //   defaultMessage: menu.name,
        // }),
        label: menu.name,
        key: menu.key,
        closable: menu.closable
      }))}
    />
  );
}

export { TabMenu };
