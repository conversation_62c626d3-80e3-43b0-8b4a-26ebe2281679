import { Card, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

import useStyles from './style';

const StatisticCard = props => {
  const {
    title,
    desc,
    total = 0,
    content,
    footer,
  } = props;

  const { styles } = useStyles();

  return (
    <Card styles={{
      body: { padding: '20px 24px 8px' }
    }}>
      <div className={styles.summaryTop}>
        <div className={styles.meta}>
          <span>{title}</span>
          {desc && (
            <span className={styles.actions}>
              <Tooltip title={desc}>
                <InfoCircleOutlined />
              </Tooltip>
            </span>
          )}
        </div>
        <div className={styles.total}>{total}</div>
      </div>
      {content && <div className={styles.summaryContent}>{content}</div>}
      {footer && <div className={styles.summaryFooter}>{footer}</div>}
    </Card>
  );
}

export { StatisticCard };
