import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  summaryTop: css`
    position: relative;
    width: 100%;
    overflow: hidden;
  `,
  meta: css`
    margin-bottom: ${token.marginXXS}px;
    color: ${token.colorTextSecondary};
    font-size: ${token.titleFontSize}px;
  `,
  actions: css`
    float: right;
  `,
  total: css`
    color: ${token.colorText};
    font-size: ${token.fontSizeHeading3}px;
    font-family: ${token.fontFamily};
  `,
  summaryContent: css`
    position: relative;
    width: 100%;
    height: 46px;
    margin-bottom: ${token.marginSM}px;
  `,
  summaryFooter: css`
    margin-top: ${token.marginXS}px;
    padding-top: ${token.paddingXS}px;
    border-top: 1px solid ${token.colorBorderSecondary};
  `,
}));

export default useStyles;
