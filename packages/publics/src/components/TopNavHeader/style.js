import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  topNavHeader: css`
    position: relative;
    width: 100%;
    height: 100%;
    background: transparent;
    & .anticon {
      color: inherit;
    }
  `,
  headerWide: css`
    max-width: 1152px;
    margin: 0 auto;
  `,
  topNavHeaderMain: css`
    height: 100%;
    padding-inline: 16px;
  `,
  topNavMenu: css`
    min-width: 0;
    display: flex;
    align-items: center;
    padding-inline: 6px;
    padding-block: 6px;
    line-height: ${Math.max(
      (token.header?.heightLayoutHeader || 56) - 12,
      40,
    )}px;
  `,
}));

export default useStyles;
