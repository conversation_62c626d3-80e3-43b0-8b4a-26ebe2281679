import { useMemo } from 'react';
import { Flex } from 'antd';

import { Branding } from '@titd/publics/components';
import { defaultSettings } from '@titd/publics/utils';

import ActionsContent from '../GlobalHeader/ActionsContent';
import { BaseMenu } from '../SiderMenu/BaseMenu';

import useStyles from './style';

const TopNavHeader = props => {
  const {
    // onMenuHeaderClick,
    contentWidth,
    rightContentRender,
    className: propsClassName,
    style,
    headerContentRender,
    layout,
    actionRender,
  } = props;

  const { styles, cx } = useStyles();

  const contentDom = useMemo(() => {
    const defaultDom = (
      <BaseMenu
        theme={props.theme}
        {...props}
        className={`${defaultSettings.prefixCls}-topnav-base-menu`}
        {...props.menuProps}
        style={{
          width: '100%',
          ...props.menuProps?.style,
        }}
        collapsed={false}
        menuRenderType="header"
        mode="horizontal"
      />
    );

    if (headerContentRender) {
      return headerContentRender(props, defaultDom);
    }
    return defaultDom;
  }, [props, headerContentRender]);

  return (
    <div
      className={cx(
        propsClassName,
        styles.topNavHeader,
        contentWidth === 'Fixed' && layout === 'top' ? styles.headerWide : '',
      )}
      style={style}
    >
      <Flex
        className={styles.topNavHeaderMain}
        gap="small"
        justify="start"
        align="center"
      >
        <Branding {...props} />
        <div
          style={{ flex: 1 }}
          className={styles.topNavMenu}
        >{contentDom}</div>
        {(rightContentRender || actionRender || props.avatarProps) && (
          <ActionsContent rightContentRender={rightContentRender} {...props} />
        )}
      </Flex>
    </div>
  );
}

export default TopNavHeader;
