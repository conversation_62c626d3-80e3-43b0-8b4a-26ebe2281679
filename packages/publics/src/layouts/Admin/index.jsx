import { useRef, useMemo } from 'react';
import { Outlet, useLocation, useNavigate, Link } from 'react-router';
import { App } from 'antd';
// import { useRequest } from 'ahooks';

// import { PageContainer } from '../../components/PageContainer';
// import { KeepAliveDom } from '../../components/KeepAlive';
import { ModalForm } from '../../components/form';
import { updatePwdForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  storage,
} from '@titd/publics/utils';

import { MainLayout } from '../Main';
import { userDropMenu, actionsGroup, bgImgList } from './data-dom';

const { SITE_URL, MESSAGE_TYPE, CURD } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { SESSION, TiLocalStorage, TiSessionStorage } = storage;

const AdminLayout = () => {
  const updateForm = useRef();
  const location = useLocation();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const localUser = TiLocalStorage.getJson(SESSION) || TiSessionStorage.getJson(SESSION);

  const createUpdatePwdForm = useMemo(() => updatePwdForm($t), [$t]);

  const gotoLogin = () => {
    TiLocalStorage.clear();
    TiSessionStorage.clear();
    // navigate('/auth/login', { state: { from: location }}, { replace: true });
    navigate('/auth/login', { replace: true });
  };

  const updatePwdSubmit = async form => {
    const postData = {
      ...defaultParams,
      userid: form.loginname,
      oldpassword: form.oldpassword,
      newpassword: form.newpassword,
    }

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.UpdatePassword), postData);

    if (resp.status === 200) {
      message.success(CURD.Update + CURD.StatusOkMsg);
      gotoLogin();
    }
  }

  const logout = async () => {
    const logoutParams = {
      webuserid: localUser.id,
      sessionid: localUser.session,
    }

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.UserLogout), logoutParams);

    if (resp.status === 200) {
      gotoLogin();
    } else {
      message.error(resp.data.errmessage || CURD.ServerError);

      // 不管成功与否都去登录
      setTimeout(() => {
        gotoLogin();
      }, 300);
    }
  }

  return (<>
    <MainLayout
      location={location}
      formatMessage={$t}
      actionsArray={actionsGroup}
      avatarProps={{
        userInfo: localUser,
        dropMenu: userDropMenu,
        menuClick: item => [
          () => console.log('1: ', item),
          // 清除缓存
          () => {
            TiSessionStorage.clear();
            // 删除后刷新浏览器
            window.location.reload();
          },
          // 修改密码
          () => {
            const record = {
              loginname: localUser.id
            }
            updateForm.current.show(3, record);
          },
          // 退出登录
          () => logout(),
        ][item.key](),
      }}
      menuItemRender={(item, dom) => (<Link to={item.path}>{dom}</Link>)}
      breadcrumbRender={routes => [{
        path: '/',
        title: '主页',
      }, ...(routes || [])]}
      bgLayoutImgList={bgImgList}
    >
      {/* <PageContainer> */}
        {/* <KeepAliveDom /> */}
      <Outlet />
      {/* </PageContainer> */}
    </MainLayout>

    {/* 修改密码 */}
    <ModalForm
      ref={updateForm}
      onOk={updatePwdSubmit}
      formData={createUpdatePwdForm}
    />
  </>);
}

export { AdminLayout };
