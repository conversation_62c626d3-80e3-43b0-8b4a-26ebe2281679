import { iconDom } from '@titd/publics/utils';
const { keyIcon, copyIcon, hideIcon, showIcon } = iconDom;

export const updatePwdForm = $t => [{
  valueType: 'text',
  name: 'loginname',
  label: $t('app.options.sys.loginname'),
}, {
  valueType: 'password',
  name: 'oldpassword',
  label: $t('app.auth.oldpassword'),
  rules: [{ required: true }],
  fieldProps: {
    prefix: keyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}, {
  valueType: 'password',
  name: 'newpassword',
  label: $t('app.auth.newpassword'),
  rules: [{ required: true }],
  fieldProps: {
    prefix: keyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}, {
  valueType: 'password',
  name: 'repassword',
  label: $t('app.auth.newpassword.again'),
  rules: [
    { required: true },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newpassword') === value) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error(
            $t('app.options.repassword.error')
          ));
        }
      }
    }),
  ],
  fieldProps: {
    prefix: copyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}];
