@import '~antd/es/style/themes/default.less';

.ti-layout {
  &.full-container {
    min-height: 100vh;
  }
}

.ti-layout-footer {
  padding: 12px 25px;
  text-align: center;
}

.ti-auth-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ti-auth-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 0;

  .ti-auth-title {
    text-align: center;

    .ti-auth-title-logo {
      height: 42px;
    }
    .ti-auth-title-desc {
      margin-top: 8px;
      margin-bottom: 20px;
    }
  }

  .ti-auth-main {
    width: 380px;
  }
}

@media (min-width: @screen-md-min) {
  .ti-auth-bg {
    &.@{ant-prefix}-layout {
      background-image: url('/assets/images/bg.svg');
      background-repeat: no-repeat;
      background-position: center 110px;
      background-size: 100%;
    }
  }

  .ti-auth-box {
    padding: 32px 0 24px;
  }
}

.ti-auth-main {
  .ti-form-btn {
    width: 100%;
  }
}

.ti-form-item-icon {
  color: rgba(0,0,0, 0.25);
}

.ti-login-form {
  .ti-form-forgot {
    float: right;
  }

  .ti-form-register {
    margin-top: 8px;
    text-align: center;
  }

  .ti-login-title {
    margin-bottom: 24px;
  }
}
