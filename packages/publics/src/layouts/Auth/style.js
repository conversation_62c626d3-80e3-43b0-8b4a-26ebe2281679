import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css, responsive }) => ({
  layout: css`
    display: flex;
    background-color: transparent;
    width: 100%;
    min-height: 100vh;
  `,
  authBg: responsive({
    md: css`
      background-image: url('/assets/images/bg.svg');
      background-repeat: no-repeat;
      background-position: center 110px;
      background-size: 100%;
    `,
  }),
  layoutFooter: css`
    padding: 12px 25px;
    text-align: center;
  `,
  authContent: css`
    display: flex;
    justify-content: center;
    align-items: center;
  `,
  authBox: css`
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px 0;

    ${responsive({
      md: css`
        padding: 32px 0 24px;
      `,
    })}
  `,
  authTitle: css`
    text-align: center;
  `,
  authTitleLogo: css`
    height: 42px;
  `,
  authTitleDesc: css`
    margin-top: 8px;
    margin-bottom: 20px;
  `,
  authMain: css`
    width: 380px;
  `,
  formBtn: css`
    width: 100%;
  `,
  formItemIcon: css`
    color: rgba(0,0,0, 0.25);
  `,
  formForgot: css`
    float: right;
  `,
  formRegister: css`
    margin-top: 8px;
    text-align: center;
  `,
  loginTitle: css`
    margin-bottom: 24px;
  `,
}));
