import { Layout, Card, Typography } from 'antd';
import { Outlet } from 'react-router';
import { HelmetProvider } from 'react-helmet-async';

import { AntdStyleRegistry } from '@titd/publics/libs';
import { Footer } from '@titd/publics/components';
import { LogoCNF } from '@titd/publics/assets';
import { defaultSettings, useFormattedMessage } from '@titd/publics/utils';

import { useStyles } from './style';

const { Content } = Layout;
const { Title, Link } = Typography;

const AuthLayout = () => {
  const { $t } = useFormattedMessage();

  const { styles, cx } = useStyles();

  return (
    <AntdStyleRegistry {...defaultSettings}>
      <HelmetProvider>
        <Layout className={cx(styles.layout, styles.authBg)}>
          <Content className={styles.authContent}>
            <section className={styles.authBox}>
              <div className={styles.authTitle}>
                <Link><img className={styles.authTitleLogo} src={LogoCNF} alt="Logo" /></Link>
                <Title level={3} type="secondary" className={styles.authTitleDesc}>Faster is Better</Title>
              </div>
              <Card className={styles.authMain}>
                <Outlet />
              </Card>
            </section>
          </Content>
          <Footer propsFormatMessage={$t} />
        </Layout>
      </HelmetProvider>
    </AntdStyleRegistry>
  );
}

export { AuthLayout };
