import {
  DatabaseOutlined,
  UserOutlined,
  UserSwitchOutlined,
  UsergroupAddOutlined,
  AppstoreOutlined,
  CopyOutlined,
  TrademarkOutlined,
  PayCircleOutlined,
  PieChartOutlined,
  ClockCircleOutlined,
  DiffOutlined,
  GroupOutlined,
  MessageOutlined,
  CloudServerOutlined,
  LinkOutlined,
  FlagOutlined,
  StockOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  BugOutlined,
  ExportOutlined,
  UserAddOutlined,
  TransactionOutlined,
  SwapOutlined,
  FileDoneOutlined,
  LoginOutlined,
  HistoryOutlined,
  RiseOutlined,
} from '@ant-design/icons';

import { consts } from '@titd/publics/utils';

const { DEFAULT_CONFIGS } = consts;

// prettier-ignore
const menus = servers => [{
  id: 1,
  key: 'db',
  name: '场下管理',
  locale: 'db',
  icon: <DatabaseOutlined />,
  hidepath: '/db',
  children: [{
    id: 10,
    key: 'userinfo',
    name: '用户信息',
    locale: 'db.userinfo',
    icon: <UserOutlined />,
    path: '/db/userinfo',
    desc: '普通用户',
    descid: 'db.userinfo.desc'
  }, {
    id: 11,
    key: 'userclient',
    name: '用户交易编码',
    locale: 'db.userclient',
    icon: <UsergroupAddOutlined />,
    path: '/db/userclient',
    desc: '用户与交易编码对应关系信息',
    descid: 'db.userclient.desc'
  }, {
    id: 12,
    key: 'appid',
    name: 'APPID',
    locale: 'db.appid',
    icon: <AppstoreOutlined />,
    path: '/db/appid',
  }, {
    id: 13,
    key: 'tradetpl',
    name: '权限模版',
    locale: 'db.tradetpl',
    icon: <CopyOutlined />,
    path: '/db/tradetpl'
  }, {
    id: 14,
    key: 'traderight',
    name: '交易权限',
    locale: 'db.traderight',
    icon: <TrademarkOutlined />,
    path: '/db/traderight'
  }, {
    id: 15,
    key: 'assignfunds',
    name: '资金分配',
    locale: 'db.assignfunds',
    icon: <PieChartOutlined />,
    path: '/db/assignfunds'
  }, {
    id: 16,
    key: 'frozenfunds',
    name: '资金信息',
    locale: 'db.frozenfunds',
    icon: <PayCircleOutlined />,
    path: '/db/frozenfunds'
  }, {
    id: 17,
    key: 'importlog',
    name: '上场确认',
    locale: 'db.importlog',
    icon: <ClockCircleOutlined />,
    path: '/db/importlog',
    desc: '上场需先进行交易日的确认',
    descid: 'db.importlog.desc'
  }, {
    id: 18,
    key: 'datacompare',
    name: '数据校验',
    locale: 'db.datacompare',
    icon: <DiffOutlined />,
    path: '/db/datacompare',
    desc: '校验 TITD 与 CTP 的资金、持仓数据',
    descid: 'db.datacompare.desc'
  }, {
    id: 19,
    key: 'userrisk',
    name: '风控用户',
    locale: 'db.userrisk',
    icon: <UserSwitchOutlined />,
    path: '/db/userrisk'
  }, {
    id: 110,
    key: 'exsubmit',
    name: '看穿式监管',
    locale: 'db.exsubmit',
    icon: <ExportOutlined />,
    path: '/db/exsubmit',
    desc: '看穿式监管报送文件下载',
    descid: 'db.exsubmit.desc'
  }, DEFAULT_CONFIGS.SHOW_CHILD ? {
    id: 111,
    key: 'sub',
    name: '账号管理',
    locale: 'db.sub',
    icon: <UserAddOutlined />,
    hidepath: '/sub',
    children: [{
      id: 1111,
      key: 'saccount',
      name: '系统资金账户',
      locale: 'db.sub.account',
      path: '/db/sub/saccount',
    }, {
      id: 1112,
      key: 'sclient',
      name: '系统交易编码',
      locale: 'db.sub.client',
      path: '/db/sub/sclient',
    }, {
      id: 1113,
      key: 'sassignfunds',
      name: '系统资金分配',
      locale: 'db.sub.assignfunds',
      path: '/db/sub/sassignfunds'
    }]
  } : null, DEFAULT_CONFIGS.SHOW_VIRTUAL ? {
    id: 112,
    key: 'limit',
    name: '异常交易设置',
    locale: 'app.meun.db.limit',
    icon: <RiseOutlined />,
    hidepath: '/limit',
    children: [{
      id: 1124,
      key: 'openclosetrans',
      name: '开平转换',
      locale: 'app.meun.db.limit.openclosetrans',
      path: '/db/limit/openclosetrans'
    }, {
      id: 1125,
      key: 'selftradetrans',
      name: '自成交转换',
      locale: 'app.meun.db.limit.selftradetrans',
      path: '/db/limit/selftradetrans'
    }]
  } : null]
}, {
  id: 4,
  key: 'fund',
  name: '资金管理',
  locale: 'fund',
  icon: <TransactionOutlined />,
  hidepath: '/fund',
  children: [{
    id: 41,
    key: 'amount',
    name: '资金划转',
    locale: 'fund.amount',
    icon: <SwapOutlined />,
    path: '/fund/amount'
  }, {
    id: 42,
    key: 'translog',
    name: '划转记录',
    locale: 'fund.translog',
    icon: <FileDoneOutlined />,
    path: '/fund/translog',
  }]
}, {
  id: 2,
  key: 'mem',
  name: '场上管理',
  locale: 'mem',
  icon: <GroupOutlined />,
  hidepath: '/mem',
  children: servers?.length > 0 ? servers.map((item, idx) => ({
    id: 21 + idx,
    key: item.value,
    name: item.label,
    icon: <CloudServerOutlined />,
    hidepath: `/mem/${item.value}`,
    children: [{
      id: 201,
      key: `user${item.value}`,
      name: '交易用户',
      locale: 'mem.user',
      icon: <UserOutlined />,
      hidepath: `/mem/${item.value}/user`,
      children: [{
        id: 2011,
        group: item.value,
        key: `muserinfo${item.value}`,
        name: '登录账户',
        locale: 'mem.user.muserinfo',
        path: `/mem/${item.value}/user/muserinfo`,
        desc: '场上用户信息',
        descid: 'mem.user.muserinfo.desc'
      }, {
        id: 2012,
        group: item.value,
        key: `muserclient${item.value}`,
        name: '用户交易编码',
        locale: 'mem.user.muserclient',
        path: `/mem/${item.value}/user/muserclient`
      }, {
        id: 2013,
        group: item.value,
        key: `session${item.value}`,
        name: 'SESSION信息',
        locale: 'mem.user.session',
        path: `/mem/${item.value}/user/session`
      }, {
        id: 2014,
        group: item.value,
        key: `account${item.value}`,
        name: '资金信息',
        locale: 'mem.user.account',
        path: `/mem/${item.value}/user/account`,
        desc: '可以修改、出金、入金',
        descid: 'mem.user.account.desc'
      }, {
        id: 2015,
        group: item.value,
        key: `commimargin${item.value}`,
        name: '手续费保证金',
        locale: 'mem.user.commimargin',
        path: `/mem/${item.value}/user/commimargin`,
        desc: '合约手续费保证金信息',
        descid: 'mem.user.commimargin.desc'
      }, {
        id: 2016,
        group: item.value,
        key: `risk${item.value}`,
        name: '风控信息',
        locale: 'mem.user.risk',
        path: `/mem/${item.value}/user/risk`
      }]
    }, {
      id: 202,
      group: item.value,
      key: `instrument${item.value}`,
      name: '合约信息',
      locale: 'mem.instrument',
      icon: <LinkOutlined />,
      path: `/mem/${item.value}/instrument`
    }, {
      id: 203,
      key: `pos${item.value}`,
      name: '持仓信息',
      locale: 'mem.pos',
      icon: <FlagOutlined />,
      hidepath: `/mem/${item.value}/pos`,
      children: [{
        id: 2031,
        group: item.value,
        key: `position${item.value}`,
        name: '持仓汇总',
        locale: 'mem.pos.position',
        path: `/mem/${item.value}/pos/position`,
        desc: '多头、空头、备兑持仓信息',
        descid: 'mem.pos.position.desc'
      }, {
        id: 2032,
        group: item.value,
        key: `positiondtl${item.value}`,
        name: '持仓详情',
        locale: 'mem.pos.positiondtl',
        path: `/mem/${item.value}/pos/positiondtl`
      }, {
        id: 2033,
        group: item.value,
        key: `positioncomb${item.value}`,
        name: '组合持仓',
        locale: 'mem.pos.positioncomb',
        path: `/mem/${item.value}/pos/positioncomb`,
      }]
    }, {
      id: 204,
      key: `ord${item.value}`,
      name: '交易请求',
      locale: 'mem.ord',
      icon: <StockOutlined />,
      hidepath: `/mem/${item.value}/ord`,
      children: [{
        id: 2041,
        group: item.value,
        key: `orderinsert${item.value}`,
        name: '期权报单',
        locale: 'mem.ord.orderinsert',
        path: `/mem/${item.value}/ord/orderinsert`
      }, {
        id: 2042,
        group: item.value,
        key: `orderaction${item.value}`,
        name: '撤单信息',
        locale: 'mem.ord.orderaction',
        path: `/mem/${item.value}/ord/orderaction`
      }, {
        id: 2043,
        group: item.value,
        key: `omlinsert${item.value}`,
        name: '组合报单',
        locale: 'mem.ord.omlinsert',
        path: `/mem/${item.value}/ord/omlinsert`,
      }, {
        id: 2044,
        group: item.value,
        key: `exerc${item.value}`,
        name: '行权报单',
        locale: 'mem.ord.exerc',
        path: `/mem/${item.value}/ord/exerc`,
      }, {
        id: 2045,
        group: item.value,
        key: `combexerc${item.value}`,
        name: '组合行权',
        locale: 'mem.ord.combexerc',
        path: `/mem/${item.value}/ord/combexerc`,
      }, {
        id: 2046,
        group: item.value,
        key: `quoteinsert${item.value}`,
        name: '报价信息',
        locale: 'mem.ord.quoteinsert',
        path: `/mem/${item.value}/ord/quoteinsert`,
      }, {
        id: 2047,
        group: item.value,
        key: `ordertimeout${item.value}`,
        name: '报单超时',
        locale: 'mem.ord.ordertimeout',
        path: `/mem/${item.value}/ord/ordertimeout`
      }]
    }, {
      id: 205,
      group: item.value,
      key: `tradeinfo${item.value}`,
      name: '成交信息',
      locale: 'mem.tradeinfo',
      icon: <CheckCircleOutlined />,
      path: `/mem/${item.value}/tradeinfo`
    }, {
      id: 206,
      key: `rtn${item.value}`,
      name: '回报信息',
      locale: 'mem.rtn',
      icon: <MessageOutlined />,
      hidepath: `/mem/${item.value}/rtn`,
      children: [{
        id: 2061,
        group: item.value,
        key: `rtnorder${item.value}`,
        name: '委托回报',
        locale: 'mem.rtn.rtnorder',
        path: `/mem/${item.value}/rtn/rtnorder`
      }, {
        id: 2062,
        group: item.value,
        key: `rtntrade${item.value}`,
        name: '成交回报',
        locale: 'mem.rtn.rtntrade',
        path: `/mem/${item.value}/rtn/rtntrade`
      }, {
        id: 2063,
        group: item.value,
        key: `rtnexerc${item.value}`,
        name: '行权回报',
        locale: 'mem.rtn.rtnexerc',
        path: `/mem/${item.value}/rtn/rtnexerc`,
      }, {
        id: 2064,
        group: item.value,
        key: `rtnoml${item.value}`,
        name: '组合回报',
        locale: 'mem.rtn.rtnoml',
        path: `/mem/${item.value}/rtn/rtnoml`,
      }, {
        id: 2065,
        group: item.value,
        key: `rtnquote${item.value}`,
        name: '报价回报',
        locale: 'mem.rtn.rtnquote',
        path: `/mem/${item.value}/rtn/rtnquote`,
      }, {
        id: 2066,
        group: item.value,
        key: `rtnwithdraw${item.value}`,
        name: '出入金回报',
        locale: 'mem.rtn.rtnwithdraw',
        path: `/mem/${item.value}/rtn/rtnwithdraw`
      }]
    // }, {
    //   key: `rtnorderstk${item.value}`,
    //   name: '证券委托通知',
    //   icon: <MessageOutlined />,
    //   path: `/mem/${item.value}/rtnorderstk`
    }, {
      id: 207,
      key: `err${item.value}`,
      name: '异常信息',
      locale: 'mem.err',
      icon: <ExclamationCircleOutlined />,
      hidepath: `/mem/${item.value}/err`,
      children: [{
        id: 2071,
        group: item.value,
        key: `selftrade${item.value}`,
        name: '自成交信息',
        locale: 'mem.err.selftrade',
        path: `/mem/${item.value}/err/selftrade`
      }]
    }]
  })) : null,
}, DEFAULT_CONFIGS.SHOW_HISTORY ? {
  id: 5,
  key: 'hist',
  name: '场上历史',
  locale: 'hist',
  icon: <HistoryOutlined />,
  hidepath: '/hist',
  children: [{
    id: 51,
    key: 'user',
    name: '交易用户',
    locale: 'mem.user',
    icon: <UserOutlined />,
    hidepath: '/hist/user',
    children: [{
      id: 511,
      group: '历史',
      key: 'huserinfo',
      name: '登录账户',
      locale: 'mem.user.muserinfo',
      path: '/hist/user/huserinfo'
    }, {
      id: 512,
      group: '历史',
      key: 'huserclient',
      name: '用户交易编码',
      locale: 'mem.user.muserclient',
      path: '/hist/user/huserclient'
    }, {
      id: 513,
      group: '历史',
      key: 'hsession',
      name: 'SESSION信息',
      locale: 'mem.user.session',
      path: '/hist/user/hsession'
    }, {
      id: 514,
      group: '历史',
      key: 'haccount',
      name: '资金信息',
      locale: 'mem.user.account',
      path: '/hist/user/haccount'
    }, {
      id: 515,
      group: '历史',
      key: 'hcommimargin',
      name: '手续费保证金',
      locale: 'mem.user.commimargin',
      path: '/hist/user/hcommimargin'
    }, {
      id: 516,
      group: '历史',
      key: 'hrisk',
      name: '风控信息',
      locale: 'mem.user.risk',
      path: '/hist/user/hrisk'
    }]
  }, {
    id: 52,
    group: '历史',
    key: 'hinstrument',
    name: '合约信息',
    locale: 'mem.instrument',
    icon: <LinkOutlined />,
    path: '/hist/hinstrument'
  }, {
    id: 53,
    key: 'pos',
    name: '持仓信息',
    locale: 'mem.pos',
    icon: <FlagOutlined />,
    hidepath: '/hist/pos',
    children: [{
      id: 531,
      group: '历史',
      key: 'hposition',
      name: '持仓汇总',
      locale: 'mem.pos.position',
      path: '/hist/pos/hposition'
    }, {
      id: 532,
      group: '历史',
      key: 'hpositiondtl',
      name: '持仓详情',
      locale: 'mem.pos.positiondtl',
      path: '/hist/pos/hpositiondtl'
    }, {
      id: 533,
      group: '历史',
      key: 'hpositioncomb',
      name: '组合持仓',
      locale: 'mem.pos.positioncomb',
      path: '/hist/pos/hpositioncomb',
    }]
  }, {
    id: 54,
    key: 'ord',
    name: '交易请求',
    locale: 'mem.ord',
    icon: <StockOutlined />,
    hidepath: '/hist/ord',
    children: [{
      id: 541,
      group: '历史',
      key: 'horderinsert',
      name: '期权报单',
      locale: 'mem.ord.orderinsert',
      path: '/hist/ord/horderinsert'
    }, {
      id: 542,
      group: '历史',
      key: 'horderaction',
      name: '撤单信息',
      locale: 'mem.ord.orderaction',
      path: '/hist/ord/horderaction'
    }, {
      id: 543,
      group: '历史',
      key: 'homlinsert',
      name: '组合报单',
      locale: 'mem.ord.omlinsert',
      path: '/hist/ord/homlinsert',
    }, {
    //   id: 544,
    //   group: '历史',
    //   key: 'hexerc',
    //   name: '行权报单',
    //   locale: 'mem.ord.exerc',
    //   path: '/hist/ord/hexerc',
    // }, {
      id: 545,
      group: '历史',
      key: 'hcombexerc',
      name: '组合行权',
      locale: 'mem.ord.combexerc',
      path: '/hist/ord/hcombexerc',
    }, {
      id: 546,
      group: '历史',
      key: 'hquoteinsert',
      name: '报价信息',
      locale: 'mem.ord.quoteinsert',
      path: '/hist/ord/hquoteinsert',
    }]
  }, {
    id: 55,
    group: '历史',
    key: 'htradeinfo',
    name: '成交信息',
    locale: 'mem.tradeinfo',
    icon: <CheckCircleOutlined />,
    path: '/hist/htradeinfo'
  }, {
    id: 56,
    key: 'rtn',
    name: '回报信息',
    locale: 'mem.rtn',
    icon: <MessageOutlined />,
    hidepath: '/hist/rtn',
    children: [{
      id: 561,
      group: '历史',
      key: 'hrtnorder',
      name: '委托回报',
      locale: 'mem.rtn.rtnorder',
      path: '/hist/rtn/hrtnorder'
    }, {
      id: 562,
      group: '历史',
      key: 'hrtntrade',
      name: '成交回报',
      locale: 'mem.rtn.rtntrade',
      path: '/hist/rtn/hrtntrade'
    }, {
      id: 563,
      group: '历史',
      key: 'hrtnexerc',
      name: '行权回报',
      locale: 'mem.rtn.rtnexerc',
      path: '/hist/rtn/hrtnexerc',
    }, {
      id: 564,
      group: '历史',
      key: 'hrtnoml',
      name: '组合回报',
      locale: 'mem.rtn.rtnoml',
      path: '/hist/rtn/hrtnoml',
    }, {
      id: 565,
      group: '历史',
      key: 'hrtnquote',
      name: '报价回报',
      locale: 'mem.rtn.rtnquote',
      path: '/hist/rtn/hrtnquote',
    // }, {
    //   id: 4066,
    //   group: '历史',
    //   key: 'hrtnwithdraw',
    //   name: '出入金回报',
    //   locale: 'mem.rtn.rtnwithdraw',
    //   path: '/hist/rtn/hrtnwithdraw'
    }]
  }, {
    id: 57,
    group: '历史',
    key: 'hloginlog',
    name: '登录日志',
    locale: 'sys.loginlog',
    icon: <LoginOutlined />,
    path: '/hist/hloginlog'
  }, {
    id: 58,
    group: '历史',
    key: 'hexsubmit',
    name: '看穿式监管',
    locale: 'db.exsubmit',
    icon: <ExportOutlined />,
    path: '/hist/hexsubmit',
  }]
} : null, {
  id: 3,
  key: 'sys',
  name: '系统管理',
  locale: 'sys',
  icon: <SettingOutlined />,
  hidepath: '/sys',
  children: [{
    id: 31,
    key: 'syslog',
    name: '日志管理',
    locale: 'sys.syslog',
    icon: <BugOutlined />,
    path: '/sys/syslog',
    desc: '管理操作日志',
    descid: 'sys.syslog.desc'
  }, {
    id: 32,
    key: 'adminuser',
    name: '用户管理',
    locale: 'sys.adminuser',
    icon: <UserOutlined />,
    path: '/sys/adminuser'
  // }, {
  //   id: 33,
  //   key: 'adminlog',
  //   name: '管理日志',
  //   icon: <BugOutlined />,
  //   path: '/sys/adminlog'
  // }, {
  //   id: 34,
  //   key: 'loginlog',
  //   name: '登录日志',
  //   icon: <LoginOutlined />,
  //   path: '/sys/loginlog'
  }]
}];

export default menus;
