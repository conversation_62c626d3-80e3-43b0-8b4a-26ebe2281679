import { createGlobalStyle, createStyles } from 'antd-style';

import { defaultSettings } from '@titd/publics/utils';

export const GlobalStyle = createGlobalStyle`
  .${defaultSettings.prefixCls}-base-menu {
    color: ${p => p.theme.sider?.colorTextMenu};

    .${defaultSettings.antdCls}-menu-sub {
      background-color: transparent !important;
      color: ${p => p.theme.sider?.colorTextMenu};
    }
    & .${defaultSettings.antdCls}-layout {
      background-color: transparent;
      width: 100%;
    }
    .${defaultSettings.antdCls}-menu-submenu-expand-icon, .${defaultSettings.antdCls}-menu-submenu-arrow {
      color: inherit;
    }
    &.${defaultSettings.antdCls}-menu {
      color: ${p => p.theme.sider?.colorTextMenu};
      .${defaultSettings.antdCls}-menu-item {
        * {
          transition: none !important;
        }
      }
      .${defaultSettings.antdCls}-menu-item a {
        color: inherit;
      }
    }
    &.${defaultSettings.antdCls}-menu-inline {
      .${defaultSettings.antdCls}-menu-selected::after,
      .${defaultSettings.antdCls}-menu-item-selected::after {
        display: none;
        }
    }
    .${defaultSettings.antdCls}-menu-sub.${defaultSettings.antdCls}-menu-inline {
      background-color: transparent !important;
    }
    .${defaultSettings.antdCls}-menu-item:active,
    .${defaultSettings.antdCls}-menu-submenu-title:active {
      background-color: transparent !important;
    }
    &.${defaultSettings.antdCls}-menu-light {
      .${defaultSettings.antdCls}-menu-item:hover,
        .${defaultSettings.antdCls}-menu-item-active,
        .${defaultSettings.antdCls}-menu-submenu-active,
        .${defaultSettings.antdCls}-menu-submenu-title:hover {
        color: ${p => p.theme.sider?.colorTextMenuActive};
        border-radius: ${p => p.theme.borderRadius}px;
        .${defaultSettings.antdCls}-menu-submenu-arrow {
          color: ${p => p.theme.sider?.colorTextMenuActive};
        }
      }
    }
    &.${defaultSettings.antdCls}-menu:not(.${defaultSettings.antdCls}-menu-horizontal) {
      .${defaultSettings.antdCls}-menu-item-selected {
        background-color: ${p => p.theme.sider?.colorBgMenuItemSelected};
        border-radius: ${p => p.theme.borderRadius}px;
      }
      .${defaultSettings.antdCls}-menu-item:hover,
      .${defaultSettings.antdCls}-menu-item-active,
      .${defaultSettings.antdCls}-menu-submenu-title:hover {
        color: ${p => p.theme.sider?.colorTextMenuActive};
        border-radius: ${p => p.theme.borderRadius}px;
        background-color: ${p => p.theme.sider?.colorBgMenuItemHover} !important;
        .${defaultSettings.antdCls}-menu-submenu-arrow {
          color: ${p => p.theme.sider?.colorTextMenuActive};
        }
      }
    }
    .${defaultSettings.antdCls}-menu-item-selected {
      color: ${p => p.theme.sider?.colorTextMenuSelected};
    }
    .${defaultSettings.antdCls}-menu-submenu-selected {
      color: ${p => p.theme.sider?.colorTextMenuSelected};
    }
    &.${defaultSettings.antdCls}-menu:not(.${defaultSettings.antdCls}-menu-inline) .${defaultSettings.antdCls}-menu-submenu-open {
      color: ${p => p.theme.sider?.colorTextMenuSelected};
    }

    &.${defaultSettings.antdCls}-menu-vertical {
      .${defaultSettings.antdCls}-menu-submenu-selected {
        border-radius: ${p => p.theme.borderRadius}px;
        color: ${p => p.theme.sider?.colorTextMenuSelected};
      }
    }

    .${defaultSettings.antdCls}-menu-submenu:hover > .${defaultSettings.antdCls}-menu-submenu-title > .${defaultSettings.antdCls}-menu-submenu-arrow {
      color: ${p => p.theme.sider?.colorTextMenuActive};
    }

    &.${defaultSettings.antdCls}-menu-horizontal {
      .${defaultSettings.antdCls}-menu-item:hover,
      .${defaultSettings.antdCls}-menu-submenu:hover,
      .${defaultSettings.antdCls}-menu-item-active,
      .${defaultSettings.antdCls}-menu-submenu-active {
        border-radius: 4px;
        transition: none;
        color: ${p => p.theme.sider?.colorTextMenuActive};
        background-color: ${p => p.theme.sider?.colorBgMenuItemHover} !important;
      }

      .${defaultSettings.antdCls}-menu-item-open,
      .${defaultSettings.antdCls}-menu-submenu-open,
      .${defaultSettings.antdCls}-menu-item-selected,
      .${defaultSettings.antdCls}-menu-submenu-selected {
        background-color: ${p => p.theme.sider?.colorBgMenuItemSelected};
        border-radius: ${p => p.theme.borderRadius}px;
        transition: none;
        color: ${p => p.theme.sider?.colorTextMenuSelected} !important;
        .${defaultSettings.antdCls}-menu-submenu-arrow {
          color: ${p => p.theme.sider?.colorTextMenuSelected} !important;
        }
      }
      > .${defaultSettings.antdCls}-menu-item, > .${defaultSettings.antdCls}-menu-submenu {
        padding-inline: 16px;
        margin-inline: 4px;
      }
      > .${defaultSettings.antdCls}-menu-item::after, > .${defaultSettings.antdCls}-menu-submenu::after {
        display: none;
      }
    }
  }

  .${defaultSettings.prefixCls}-topnav-base-menu {
    &.${defaultSettings.antdCls}-menu {
      color: ${p => p.theme.layout?.header?.colorTextMenu};
      .${defaultSettings.antdCls}-menu-item a {
        color: inherit;
      }
    }
    &.${defaultSettings.antdCls}-menu-light {
      .${defaultSettings.antdCls}-menu-item:hover,
      .${defaultSettings.antdCls}-menu-item-active,
      .${defaultSettings.antdCls}-menu-submenu-active,
      .${defaultSettings.antdCls}-menu-submenu-title:hover {
        color: ${p => p.theme.layout?.header?.colorTextMenuActive};
        border-radius: ${p => p.theme.borderRadius};
        transition: none;
        background-color: ${p => p.theme.layout?.header?.colorBgMenuItemSelected};
        .${defaultSettings.antdCls}-menu-submenu-arrow {
          color: ${p => p.theme.layout?.header?.colorTextMenuActive};
        }
      }

      .${defaultSettings.antdCls}-menu-item-selected {
        color: ${p => p.theme.layout?.header?.colorTextMenuSelected};
        border-radius: ${p => p.theme.borderRadius};
        background-color: ${p => p.theme.layout?.header?.colorBgMenuItemSelected};
      }
    }
  }

  .${defaultSettings.antdCls}-menu-sub.${defaultSettings.antdCls}-menu-inline {
    background-color: transparent !important;
  }
  .${defaultSettings.antdCls}-menu-submenu-popup {
    background-color: rgba(255, 255, 255, 0.42);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    .${defaultSettings.antdCls}-menu {
      background: transparent !important;
      background-color: transparent !important;
      .${defaultSettings.antdCls}-menu-item:active,
      .${defaultSettings.antdCls}-menu-submenu-title:active {
        background-color: transparent !important;
      }
    }
    .${defaultSettings.antdCls}-menu-item-selected {
      color: ${p => p.theme.layout?.sider?.colorTextMenuSelected};
    }
    .${defaultSettings.antdCls}-menu-submenu-selected {
      color: ${p => p.theme.layout?.sider?.colorTextMenuSelected};
    }
    .${defaultSettings.antdCls}-menu:not(.${defaultSettings.antdCls}-menu-horizontal) {
      .${defaultSettings.antdCls}-menu-item-selected {
        background-color: rgba(0, 0, 0, 0.04);
        border-radius: ${p => p.theme.borderRadius};
        color: ${p => p.theme.layout?.sider?.colorTextMenuSelected};
      }
      .${defaultSettings.antdCls}-menu-item:hover,
      .${defaultSettings.antdCls}-menu-item-active,
      .${defaultSettings.antdCls}-menu-submenu-title:hover {
        color: ${p => p.theme.layout?.sider?.colorTextMenuActive};
        border-radius: ${p => p.theme.borderRadius};
        .${defaultSettings.antdCls}-menu-submenu-arrow {
          color: ${p => p.theme.layout?.sider?.colorTextMenuActive};
        }
      }
    }
  }
`;

export const useStyles = createStyles(({ token, css, prefixCls }) => ({
  layout: css`
    & .${prefixCls}-layout {
      display: flex;
      background-color: transparent;
      width: 100%;
    }
  `,
  container: css`
    width: 100%;
    display: flex;
    flex: 1;
    flex-direction: column;
    min-width: 0;
    min-height: 0;
    background-color: transparent;
    overflow: hidden;
  `,
  bgList: css`
    pointer-events: none;
    position: fixed;
    overflow: hidden;
    inset-block-start: 0px;
    inset-inline-start: 0px;
    z-index: 0;
    height: 100%;
    width: 100%;
    background: ${token?.bgLayout};
  `,
  hasFooter: css`
    height: 64px;
    margin-block-start: ${token.pageContainer?.paddingBlockPageContainerContent}px;
  `,
  fullContainer: css`
    height: 100vh;
  `,
  layoutTopMenu: css``,
  layoutIsChildren: css``,
  layoutFixSiderbar: css``,
}));
