import { createGlobalStyle, createStyles } from 'antd-style';

import { defaultSettings } from '@titd/publics/utils';

export const GlobalStyle = createGlobalStyle`
  .${defaultSettings.prefixCls}-layout {
    background-color: transparent !important;
  }
  .${defaultSettings.prefixCls}-menu-sub.${defaultSettings.prefixCls}-menu-inline {
    background-color: transparent !important;
  }
  .${defaultSettings.prefixCls}-menu-submenu-popup {
    background-color: rgba(255, 255, 255, 0.42) !important;
    backdrop-filter: blur(8px);

    .${defaultSettings.prefixCls}-menu {
      background: transparent !important;
      background-color: transparent !important;

      .${defaultSettings.prefixCls}-menu-item:active,
      .${defaultSettings.prefixCls}-menu-submenu-title:active {
        background-color: transparent !important;
      }
    }
    .${defaultSettings.prefixCls}-menu-item-selected {
      color: ${p => p.theme.sider?.colorTextMenuSelected};
    }
    .${defaultSettings.prefixCls}-menu-submenu-selected {
      color: ${p => p.theme.sider?.colorTextMenuSelected};
    }
    .${defaultSettings.prefixCls}-menu:not(.${defaultSettings.prefixCls}-menu-horizontal) {
      .${defaultSettings.prefixCls}-menu-item-selected {
        background-color: ${p => p.theme.sider?.colorBgMenuItemSelected};
        border-radius: ${p => p.theme.borderRadius}px;
      }
      .${defaultSettings.prefixCls}-menu-item:hover,
      .${defaultSettings.prefixCls}-menu-item-active,
      .${defaultSettings.prefixCls}-menu-submenu-title:hover {
        color: ${p => p.theme.sider?.colorTextMenuActive};
        border-radius: ${p => p.theme.borderRadius}px;

        .${defaultSettings.prefixCls}-menu-submenu-arrow {
          color: ${p => p.theme.sider?.colorTextMenuActive};
        }
      }
    }
  }
`;

export const useStyles = createStyles(({ token, css, prefixCls }) => ({
  layout: css`
    & .${prefixCls}-layout {
      display: flex;
      background-color: transparent;
      width: 100%;
    }
  `,
  container: css`
    width: 100%;
    display: flex;
    flex-direction: column;
    min-width: 0;
    min-height: 0;
    background-color: transparent;
  `,
  bgList: css`
    pointer-events: none;
    position: fixed;
    overflow: hidden;
    inset-block-start: 0px;
    inset-inline-start: 0px;
    z-index: 0;
    height: 100%;
    width: 100%;
    background: ${token?.bgLayout};
  `,
  hasFooter: css`
    height: 64px;
    margin-block-start: ${token.pageContainer?.paddingBlockPageContainerContent}px;
  `,
  fullContainer: css`
    height: 100vh;
    overflow: auto;
  `,
  layoutTopMenu: css``,
  layoutIsChildren: css``,
  layoutFixSiderbar: css``,
}));
