import { createIcon } from '@titd/publics/components';

const addServerInMenu = (memMenu, server, idPath) => {
  const menuid = idPath + '.';
  return memMenu?.map(item => {
    // 清除可能多加的功能菜单
    if (!item.menucode || item.menucode === '') return null;

    const itemNameId = menuid + item.menucode;
    const menuItem = {
      id: item.menuid,
      group: server.value,
      key: item.menucode + server.value,
      name: item.menuname,
      nameid: itemNameId,
      icon: createIcon(item.icon),
    }

    if (item.path && item.path !== '' && item.path !== '#') {
      menuItem.path = item.path?.replace(/\$server/, server.value);
    } else {
      menuItem.hidepath = `/${item.menucode}`;
    }

    if (item.childmenu) {
      const childMenus = addServerInMenu(item.childmenu, server, itemNameId);
      if (childMenus.length > 0) {
        menuItem.children = childMenus;
      }
    }

    return menuItem;
  }).filter(i => i);
}

export const formateMenu = (menus, servers, idPath) => {
  const menuid = (idPath ? idPath + '.' : 'app.menu.');
  return menus?.map(item => {
    // 清除可能多加的功能菜单
    if (!item.menucode || item.menucode === '') return null;

    const itemNameId = menuid + item.menucode;
    const menuItem = {
      id: item.menuid,
      key: item.menucode,
      name: item.menuname,
      nameid: itemNameId,
      icon: createIcon(item.icon),
    }

    if (item.path && item.path !== '' && item.path !== '#') {
      menuItem.path = item.path;
    } else {
      menuItem.hidepath = `/${item.menucode}`;
    }

    if (item.childmenu) {
      if (item.menuid === 3) {
        menuItem.children = servers.map((server, idx) => ({
          id: 99 + idx,
          key: server.value + '',
          name: server.label,
          icon: createIcon('CloudServerOutlined'),
          children: addServerInMenu(item.childmenu, server, itemNameId),
        }));
      } else {
        const childMenus = formateMenu(item.childmenu, servers, itemNameId);
        if (childMenus.length > 0) {
          menuItem.children = childMenus;
        }
      }
    }

    return menuItem;
  }).filter(i => i);
}
