import { Link } from 'react-router';
import { Result, Button } from 'antd';
import { useFormattedMessage } from '@titd/publics/utils';

const ExceptionPage = ({ code }) => {
  const { $t } = useFormattedMessage();

  return (
    <Result
      status={code}
      title={code}
      subTitle={$t(`app.exception.${code}`)}
      extra={(<Button type="primary">
        <Link to="/">{$t('app.exception.back')}</Link>
      </Button>)}
    />
  )
};

export default ExceptionPage;
