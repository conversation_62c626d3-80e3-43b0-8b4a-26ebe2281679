import { Form, Input, Button, message } from 'antd';
import {
  UserOutlined,
  KeyOutlined,
  CopyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';
import { useNavigate, useLocation } from "react-router-dom";
import useFetch from 'use-http';

import { MESSAGE_TYPE, SITE_URL, CURD } from '@utils/consts';

const Register = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const { post, response, loading } = useFetch(SITE_URL.BASE);

  const from = location.state?.from?.pathname || '/';

  const onFinish = async (values) => {
    console.log(values);
  }

  return (
    <Form
      name="register"
      className="ti-register-form"
      onFinish={onFinish}
    >
      {/* Username */}
      <Form.Item
        name="username"
        hasFeedback
        rules={[
          { required: true, message: '请输入用户名!' }
        ]}
      >
        <Input
          prefix={<UserOutlined className="ti-form-item-icon" />}
          size="large"
          placeholder="用户名"
          autoComplete="false"
        />
      </Form.Item>

      {/* Password */}
      <Form.Item
        name="password"
        hasFeedback
        rules={[
          { required: true, message: '请输入密码!' }
        ]}
      >
        <Input.Password
          prefix={<KeyOutlined className="ti-form-item-icon" />}
          iconRender={visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          size="large"
          placeholder="密码"
        />
      </Form.Item>

      {/* Repeat Password */}
      <Form.Item
        name="repassword"
        hasFeedback
        rules={[
          { required: true, message: '请再次输入密码!' },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('password') === value) {
                return Promise.resolve();
              } else {
                return Promise.reject(new Error('两次密码不一致'));
              }
            }
          })
        ]}
      >
        <Input.Password
          prefix={<CopyOutlined className="ti-form-item-icon" />}
          iconRender={visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          size="large"
          placeholder="确认密码"
        />
      </Form.Item>

      {/* Submit Button */}
      <div>
        <Button
          type="primary"
          size="large"
          htmlType="submit"
          className="ti-form-btn"
          loading={loading}
        >注 册</Button>
      </div>
    </Form>
  );
}

export default Register;
