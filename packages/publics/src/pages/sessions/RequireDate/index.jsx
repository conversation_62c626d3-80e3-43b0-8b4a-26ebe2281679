import { useLocation, Navigate } from 'react-router';
import { HISTORY_DATE, TiLocalStorage } from '@utils/storage';

const RequireDate = ({ children }) => {
  const checkRoute = '/hist/checkdate';
  const location = useLocation();
  const histDate = TiLocalStorage.get(HISTORY_DATE);

  if (!histDate && location.pathname !== checkRoute) {
    return <Navigate to={checkRoute} state={{ from: location }} replace />
  }

  return children;
}

export default RequireDate;
