import { useLocation, Navigate } from 'react-router';

import { sessionParams, storage } from '@titd/publics/utils';

const { DEFAULT_SESSION } = sessionParams;
const { UNLOCK, TiLocalStorage } = storage;

const RequireAuth = ({ children }) => {
  const location = useLocation();
  const localUser = DEFAULT_SESSION();
  const lockStatus = TiLocalStorage.get(UNLOCK);

  if (!localUser) {
    return <Navigate to='/auth/login' state={{ from: location }} replace />
  }

  if (lockStatus) {
    const status = lockStatus.substring(lockStatus.length - 1);
    if (status === '1') {
      return <Navigate to='/auth/unlock' state={{ from: location }} replace />
    }
  }

  return children;
}

export { RequireAuth };
