import { useEffect } from 'react';
import { Form, Input, Button, Typography, Modal } from 'antd';
import {
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { FormattedMessage, useIntl } from 'react-intl';
import { MD5 } from 'crypto-js';
import useFetch from 'use-http';
import { useMount } from 'ahooks';

import { MESSAGE_TYPE, SITE_URL } from '@utils/consts';
import { DEFAULT_SESSION_PARAMS } from '@utils/session';
import { UNLOCK, TiLocalStorage, TiSessionStorage } from '@utils/storage';

const { Link, Title } = Typography;

const Unlock = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const defaultParams = {
    ...DEFAULT_SESSION_PARAMS(),
  };

  const fromPath = location.state?.from || '/';
  const lockStatus = TiLocalStorage.get(UNLOCK);

  const [request, resp] = useFetch(SITE_URL.BASE);

  useEffect(() => {
    checkSession();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useMount(() => {
    if (lockStatus) {
      const status = lockStatus.substring(lockStatus.length - 1);
      status === '0' && TiLocalStorage.set(UNLOCK, lockStatus.substring(0, lockStatus.length - 1) + '1');
    } else {
      navigate('/settings', { replace: true });
    }
  });

  const checkSession = async () => {
    await request.post(SITE_URL.EXTEND(MESSAGE_TYPE.CheckSession), defaultParams);

    if (resp.status === 300) {
      TiLocalStorage.set(UNLOCK, lockStatus.substring(0, lockStatus.length - 1) + '0');

      navigate('/auth/login', { replace: true });

      setTimeout(() => {
        // 刷新浏览器
        window.location.reload();
      }, 500);
    }
  }

  const onFinish = async (values) => {
    // console.log(values, from, MD5(values.password).toString());
    const oldPassword = lockStatus.substring(0, lockStatus.length - 1);
    const newPassword = MD5(values.password).toString();

    if (oldPassword === newPassword) {
      TiLocalStorage.set(UNLOCK, oldPassword + '0');
      navigate(fromPath, { replace: true });
    } else {
      Modal.error({
        title: $t('app.auth.lockpwd') + $t('app.general.error')
      });
    }
  }

  const toForgot = () => {
    TiLocalStorage.clear();
    TiSessionStorage.clear();

    navigate('/auth/login', { replace: true });
  }

  return (
    <Form
      name="login"
      className="ti-login-form"
      onFinish={onFinish}
    >
      <Title className="ti-login-title" level={4}>
        <FormattedMessage id={'app.auth.unlock.title'} />
      </Title>

      {/* Password */}
      <Form.Item
        name="password"
        hasFeedback
        rules={[{ required: true, message: $t('app.general.please') + $t('app.auth.lockpwd') }]}
      >
        <Input.Password
          prefix={<LockOutlined className="ti-form-item-icon" />}
          iconRender={visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          autoComplete="off"
          size="large"
          placeholder={$t('app.auth.lockpwd')}
        />
      </Form.Item>

      {/* Submit Button */}
      <div>
        <Button
          type="primary"
          size="large"
          htmlType="submit"
          className="ti-form-btn"
        >
          <FormattedMessage id="app.auth.unlock" />
        </Button>

        {/*<div className="ti-form-register">
          <Link to="/auth/register">没有账号？</Link>
        </div>*/}
      </div>

      {/* Forgot */}
      <div style={{ marginTop: '8px' }}>
        <Link className="ti-form-forgot" onClick={toForgot}>
          <FormattedMessage id="app.auth.forgot" />
        </Link>
      </div>
    </Form>
  );
}

export default Unlock;
