import { useEffect } from 'react';
import { Form, Input, Button, Modal, Typography, message } from 'antd';
import {
  UserOutlined,
  LockOutlined,
  KeyOutlined,
  CopyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';
import { useNavigate, useLocation, Link } from "react-router-dom";
import useFetch from 'use-http';
import { FormattedMessage, useIntl } from 'react-intl';

import { MESSAGE_TYPE, SITE_URL, CURD } from '@utils/consts';
import { SESSION, TiLocalStorage } from '@utils/storage';

const { Title } = Typography;

const UpdatePwd = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [form] = Form.useForm();

  const { post, response, loading } = useFetch(SITE_URL.BASE);

  useEffect(() => {
    const userName = location.state?.name || '';
    if (userName) {
      form.setFieldsValue({
        username: userName,
      });
    }
  }, [location]); // eslint-disable-line react-hooks/exhaustive-deps

  const onFinish = async (values) => {
    // const localUser = TiLocalStorage.getJson(SESSION);
    // console.log(values, localUser);
    const params = {
      webuserid: values.username,
      oldpassword: values.oldpassword,
      newpassword: values.newpassword,
    }

    const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.UpdPassword), params);
    if (response.ok) {
      message.success($t('app.auth.updatepwd.success'));
      TiLocalStorage.remove(SESSION);
      setTimeout(() => {
        navigate('/auth/login', { replace: true });
      }, 800);
    } else {
      Modal.error({
        title: $t('app.general.error'),
        content: resp.errmessage || CURD.ServerError,
      });
    }
  }

  return (
    <Form
      form={form}
      name="updatepwd"
      className="ti-login-form"
      onFinish={onFinish}
    >
      <Title className="ti-login-title" level={4}>
        <FormattedMessage id={'app.auth.updatepwd'} />
      </Title>

      {/* Username */}
      <Form.Item
        name="username"
        hasFeedback
        rules={[{ required: true, message: $t('app.general.please') + $t('app.auth.username') }]}
      >
        <Input
          prefix={<UserOutlined className="ti-form-item-icon" />}
          size="large"
          placeholder={$t('app.auth.username')}
          autoComplete="false"
        />
      </Form.Item>

      {/* Old Password */}
      <Form.Item
        name="oldpassword"
        hasFeedback
        rules={[
          { required: true, message: $t('app.general.please') + $t('app.auth.oldpassword') }
        ]}
      >
        <Input.Password
          prefix={<LockOutlined className="ti-form-item-icon" />}
          iconRender={visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          autoComplete="off"
          size="large"
          placeholder={$t('app.auth.oldpassword')}
        />
      </Form.Item>

      {/* New Password */}
      <Form.Item
        name="newpassword"
        hasFeedback
        rules={[
          { required: true, message: $t('app.general.please') + $t('app.auth.newpassword') }
        ]}
      >
        <Input.Password
          prefix={<KeyOutlined className="ti-form-item-icon" />}
          iconRender={visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          autoComplete="off"
          size="large"
          placeholder={$t('app.auth.newpassword')}
        />
      </Form.Item>

      {/* Repeat Password */}
      <Form.Item
        name="repassword"
        hasFeedback
        rules={[
          { required: true, message: $t('app.auth.newpassword.again.msg') },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value || getFieldValue('newpassword') === value) {
                return Promise.resolve();
              } else {
                return Promise.reject(new Error($t('app.auth.newpassword.again.error')));
              }
            }
          })
        ]}
      >
        <Input.Password
          prefix={<CopyOutlined className="ti-form-item-icon" />}
          iconRender={visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          autoComplete="off"
          size="large"
          placeholder={$t('app.auth.newpassword.again')}
        />
      </Form.Item>

      {/* Submit Button */}
      <div>
        <Button
          type="primary"
          size="large"
          htmlType="submit"
          className="ti-form-btn"
          loading={loading}
        >
          <FormattedMessage id="app.auth.updatepwd.submit" />
        </Button>
      </div>

      <div className="ti-form-register">
        <Link to="/auth/login">
          <FormattedMessage id="app.auth.login.title" />
        </Link>
      </div>
    </Form>
  );
}

export default UpdatePwd;
