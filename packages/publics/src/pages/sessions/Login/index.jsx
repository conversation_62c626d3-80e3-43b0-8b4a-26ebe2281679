import { Form, Input, Button, Checkbox, Modal, Typography, App } from 'antd';
import {
  UserOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';
import { useNavigate } from 'react-router';
import { useRequest } from 'ahooks';
// import { useAliveController } from 'react-activation';

import { useRecoilState } from 'recoil';
import { tabMenuState } from '@titd/publics/states';

import { HeadTitle } from '@titd/publics/components';

import {
  consts,
  useFormattedMessage,
  Request,
  storage,
} from '@titd/publics/utils';

const { Title } = Typography;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, HOME_MENU } = consts;
const { SESSION, TiLocalStorage, TiSessionStorage } = storage;

const Login = () => {
  const navigate = useNavigate();
  // const location = useLocation();
  const { notification } = App.useApp();
  const { $t } = useFormattedMessage();

  const [tabMenu, setTabMenu] = useRecoilState(tabMenuState);

  // const { post, response, loading } = useFetch(SITE_URL.BASE);
  // const { clear } = useAliveController();

  // const from = location.state?.from?.pathname || '/';

  const onLogin = async values => {
    const loginParams = {
      webuserid: values.username,
      password: values.password,
    }

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.UserLogin), loginParams);
    const { data: respData } = resp;

    const roleResp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryRoles), {
      sessionid: respData.sessionid,
      webuserid: respData.webuserid,
      svrid: 0,
    });
    const { data: roleData } = roleResp;
    const roleName = roleData.find(item => item.roleid === respData.roleid)?.rolename;

    DEFAULT_CONFIGS.USER = {
      name: respData.name,
      id: respData.webuserid,
      session: respData.sessionid,
      role: respData.roleid,
      rolename: roleName,
    };
    if (values.remember) {
      TiLocalStorage.setJson(SESSION, DEFAULT_CONFIGS.USER);
    } else {
      TiSessionStorage.setJson(SESSION, DEFAULT_CONFIGS.USER);
    }

    // 再次登录清除 TopMenu 和 Keep-Alive
    if (tabMenu?.menuList.length > 1) {
      setTabMenu({
        menuList: [HOME_MENU],
        activeKey: HOME_MENU.code,
      });
      // clear();
    }

    navigate('/', { replace: true });

    // 添加登录成功提示
    notification.success({
      message: $t('app.auth.login.success'),
      description: $t('app.auth.login.success.tip', {
        name: <Typography.Link>{respData.name || respData.webuserid}</Typography.Link>
      }),
    });
  }

  const { loading, run } = useRequest(onLogin, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      Modal.error({
        title: $t('app.general.error'),
        content: errResp.data?.errmessage || CURD.ServerError
      });
    }
  });

  return (<>
    <HeadTitle title={$t('app.auth.login.title', {
      defaultMsg: '登录',
    })} />

    <Form
      name="login"
      className="ti-login-form"
      initialValues={{
        remember: true
      }}
      onFinish={run}
    >
      <Title className="ti-login-title" level={4}>{$t('app.auth.login.title')}</Title>

      {/* Username */}
      <Form.Item
        name="username"
        hasFeedback
        rules={[{ required: true, message: $t('app.general.please') + $t('app.auth.username') }]}
      >
        <Input
          prefix={<UserOutlined className="ti-form-item-icon" />}
          size="large"
          placeholder={$t('app.auth.username')}
          autoComplete="false"
        />
      </Form.Item>

      {/* Password */}
      <Form.Item
        name="password"
        hasFeedback
        rules={[{ required: true, message: $t('app.general.please') + $t('app.auth.password') }]}
      >
        <Input.Password
          prefix={<LockOutlined className="ti-form-item-icon" />}
          iconRender={visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          autoComplete="off"
          size="large"
          placeholder={$t('app.auth.password')}
        />
      </Form.Item>

      {/* Remember */}
      <Form.Item
        name="remember"
        valuePropName="checked"
      >
        <Checkbox>{$t('app.auth.rememberme')}</Checkbox>
      </Form.Item>

      {/*<Row
        justify="space-between"
        align="middle"
        style={{ marginBottom: '12px' }}
      >
        <Form.Item
          name="remember"
          valuePropName="checked"
          style={{ marginBottom: 0 }}
        >
          <Checkbox><FormattedMessage id="app.auth.rememberme" /></Checkbox>
        </Form.Item>

        <Link to="/auth/updatepwd">
          <FormattedMessage id="app.auth.updatepwd" />
        </Link>
      </Row>*/}

      {/* Submit Button */}
      <div>
        <Button
          block
          color="primary"
          variant="solid"
          size="large"
          htmlType="submit"
          loading={loading}
        >{$t('app.auth.submit')}</Button>
      </div>
    </Form>
  </>);
}

export { Login };
