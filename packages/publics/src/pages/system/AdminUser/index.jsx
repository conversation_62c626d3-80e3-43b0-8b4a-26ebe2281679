import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount, useDebounceFn } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm, resetPwdForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  updateRowData,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTagMap, TableSwitch } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink, resetPwdLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { adminStatusDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;

const AdminUserBase = () => {
  const modalForm = useRef(null);
  const resetForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.sys.loginname'),
    dataIndex: 'loginname',
    key: 'loginname',
    sorter: sortByName('loginname'),
    sortOrder: sortedInfo.columnKey === 'loginname' ? sortedInfo.order : null,
    render: text => <Text>{text}</Text>,
  }, {
    title: $t('app.options.db.userinfo.name'),
    dataIndex: 'name',
  }, {
    title: $t('app.options.sys.roleid'),
    dataIndex: 'roleid',
    render: text => TableTagMap(text, roleGroup, tagNormalColors),
  }, {
    title: $t('app.options.db.userinfo.status'),
    dataIndex: 'status',
    render: (text, record) => TableSwitch(modal, text, record, 'loginname', toChgStatus, adminStatusDict, 0),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.USER,
    render: (_, record) => !record.isDisabled ? (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
        resetPwdLink(toResetPwd, record),
      ]} />
    ) : null
  }];

  const [adminOptions, setAdminOptions] = useState([]);
  const [roleGroup, setRoleGroup] = useState([]);
  const { run: adminRun } = useDebounceFn((rules, value, callback) => {
    const isExist = adminOptions.find(item => item.value === value);
    if (isExist && !isModify) {
      callback(new Error('管理用户已存在，请重新输入'));
    } else {
      callback();
    }
  }, { wait: 500 });

  const roleFocus = useCallback(async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryRoles), defaultParams);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const roleList = respData.map(item => ({
        label: item.rolename,
        value: item.roleid,
      }));
      setRoleGroup(roleList);
    }
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, adminOptions), [$t, adminOptions]);
  const createAddForm = useMemo(() => addForm($t, isModify, adminRun, roleFocus, roleGroup, false), [$t, isModify, adminRun, roleFocus, roleGroup]);
  const createResetPwdForm = useMemo(() => resetPwdForm($t), [$t]);

  useMount(() => {
    roleFocus()
  });
  useUnmount(() => cancel());

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAdminUser), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        const adminIds = respData.map(item => ({
          label: item.name ? `${item.loginname}(${item.name})` : item.loginname,
          value: item.loginname,
        }));
        setAdminOptions(adminIds);
      } else {
        setAdminOptions([]);
      }
    }

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        isDisabled: item.roleid === 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAdminUser), {
      ...defaultParams,
      loginname: record.loginname,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }

    return null;
  }

  const onRefresh = showAuto => {
    run({
      ...prevParams[0],
      showAuto: showAuto,
    });
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert', record);
    setIsModify(false);
    modalForm.current.show(0);
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      setIsModify(true);
      modalForm.current.show(1, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
        isDisabled: newRecord.roleid === 1,
      });
      mutate(newData);
    } else {
      message.error($t('app.general.deleted'));
      onRefresh();
    }
  }
  const deleteFunc = async (record) => {
    const deleteData = {
      ...defaultParams,
      loginname: record.loginname,
    }

    fetchFunc(MESSAGE_TYPE.DelAdminUser, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }
  const toDeleteMore = (records) => {
    // console.log('Delete More', records);
    const ids = records.map(i => i.loginname);
    Confirm({
      modal,
      content: <>{ids.join(', ')}</>,
      onOk: () => {
        // console.log('ok', records);
        records.forEach(record => deleteFunc(record));
      }
    });
  }
  const toDelete = (record) => {
    // console.log('Delete', record);
    Confirm({
      modal,
      content: <>{record.loginname}</>,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }
  const toResetPwd = (record) => {
    // console.log('ResetPwd', record);
    resetForm.current.show(2, record);
  }
  const toChgStatus = (record, checked) => {
    // console.log(record, checked);
    const chgData = {
      loginname: record.loginname,
      name: record.name,
      roleid: record.roleid,
      status: checked ? 0 : 1,
    }

    addSubmit(chgData, 1);
  }

  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdAdminUser
      : MESSAGE_TYPE.InsAdminUser, postData, () => {
        message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);

        onRefresh(!type);
      });
  }
  const resetPwdSubmit = (form) => {
    const postData = {
      ...defaultParams,
      loginname: form.loginname,
      password: form.newpassword,
    }

    fetchFunc(MESSAGE_TYPE.UpdAdminUser, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.sys.adminuser', {
        defaultMsg: '用户管理',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        customKey: 'rspseqno',
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        handleChange: tableChange,
      }}
    />

    {/* 新增修改 */}
    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />

    {/* 重置密码 */}
    <ModalForm
      ref={resetForm}
      onOk={resetPwdSubmit}
      formData={createResetPwdForm}
    />
  </>);
}

const AdminUser = props => (
  // <KeepAlive name={keepName}>
  <AdminUserBase {...props} />
  // </KeepAlive>
);

export { AdminUser };
