// import { Spin } from 'antd';
import { iconDom } from '@titd/publics/utils';

const { keyIcon, copyIcon, hideIcon, showIcon } = iconDom;

export const searchForm = ($t, adminOptions) => [{
  valueType: 'autocomplete',
  name: 'loginname',
  label: $t('app.options.sys.loginname'),
  fieldProps: {
    options: adminOptions
  }
}];

export const addForm = ($t, isModify, adminChange, roleFocus, roleGroup) => [{
  valueType: 'input',
  name: 'loginname',
  label: $t('app.options.sys.loginname'),
  rules: !isModify ? [
    { required: true },
    { min: 3 },
    { pattern: new RegExp(/^[a-z0-9_]*$/, 'g'), message: $t('app.options.userid.msg') },
    { validator: adminChange }
  ] : null,
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'input',
  name: 'name',
  label: $t('app.options.db.userinfo.name'),
}, {
  valueType: 'password',
  name: 'password',
  label: $t('app.auth.password'),
  rules: [{ required: !isModify }],
  fieldProps: {
    disabled: isModify,
  }
}, {
//   valueType: 'select',
//   name: 'status',
//   label: '用户状态',
//   fieldProps: {
//     options: selectOptions(adminStatusDict),
//   }
// }, {
  valueType: 'select',
  name: 'roleid',
  label: $t('app.options.sys.role'),
  rules: [{ required: true }],
  fieldProps: {
    onFocus: roleFocus,
    options: roleGroup,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}];

export const resetPwdForm = $t => [{
  valueType: 'text',
  name: 'loginname',
  label: $t('app.options.sys.loginname'),
}, {
  valueType: 'password',
  name: 'newpassword',
  label: $t('app.options.newpassword'),
  rules: [{ required: true }],
  fieldProps: {
    prefix: keyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}, {
  valueType: 'password',
  name: 'repassword',
  label: $t('app.options.repassword'),
  rules: [
    { required: true },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newpassword') === value) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error(
            $t('app.options.repassword.error')
          ));
        }
      }
    }),
  ],
  fieldProps: {
    prefix: copyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}];
