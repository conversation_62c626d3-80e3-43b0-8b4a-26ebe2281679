// export const MENUS_DISABLED = [4, 241, 242, 243, 244, 245, 447, 448, 449, 450, 454, 456 ];
export const MENUS_CHECKED = [99, 201, 202, 273, 339, 281];

export const addForm = ($t, isModify, menuTree) =>  [{
  valueType: 'number',
  name: 'roleid',
  label: $t('app.options.sys.roleid'),
  rules: [{ required: true }],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'input',
  name: 'rolename',
  label: $t('app.options.sys.rolename'),
  rules: [{ required: true }],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'tree',
  name: 'menuid',
  label: $t('app.options.sys.rolemenu'),
  span: 1,
  fieldProps: {
    fieldNames: {
      title: 'menuname',
      key: 'menuid',
      children: 'childmenu'
    },
    defaultChecked: MENUS_CHECKED,
    defaultDisabled: MENUS_CHECKED,
    treeData: menuTree,
  }
}];
