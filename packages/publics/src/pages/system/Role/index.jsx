import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Card, Tag, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  TiTable,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
  HeadTitle,
} from '@titd/publics/components';
import { addForm, MENUS_CHECKED } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;

const RolesBase = () => {
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const [menuTree, setMenuTree] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  // const [request] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    title: '角色标识',
    dataIndex: 'roleid',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: '角色名称',
    dataIndex: 'rolename',
    render: (text, record) => <Tag color={tagNormalColors[record.roleid - 1]}>{text}</Tag>,
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    ),
  }];

  const createAddForm = useMemo(() => addForm($t, isModify, menuTree), [$t, isModify, menuTree]);

  useMount(() => getMenuTree());
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryRoles), params);

    const { data: respData } = resp;
    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getMenuTree = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryMenuSimple), {
      ...defaultParams,
      roleid: DEFAULT_CONFIGS.SHOW_CHILD ? 1 : 0,
    });

    const { data: respData } = resp;
    if (respData && respData.menu?.length > 0) {
      // 隐藏98
      const filterMenu = respData.menu.filter(item => item.menuid !== 98);
      setMenuTree(filterMenu);
    }
  }

  const toInsert = () => {
    // console.log('Insert', record);
    setIsModify(false);
    modalForm.current.show(0, {
      menuid: MENUS_CHECKED,
    });
  }
  const toUpdate = record => {
    // console.log('Update', record);
    // const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryRoles), {
    //   ...defaultParams,
    //   roleid: record.roleid,
    // });
    setIsModify(true);
    modalForm.current.show(1, record);
  }
  const deleteFunc = async record => {
    const deleteData = {
      ...defaultParams,
      roleid: record.roleid,
    }

    fetchFunc(MESSAGE_TYPE.DelRoles, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      refresh();
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More', records);
    const ids = records.map(i => i.roleid);
    Confirm({
      modal,
      content: <>{ids.join(', ')}</>,
      onOk: () => {
        // console.log('ok', records);
        records.forEach(record => {
          deleteFunc(record);
        });
      }
    });
  }
  const toDelete = record => {
    // console.log('Delete', record);
    Confirm({
      modal,
      content: <>{record.roleid}</>,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }

  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
      menuid: form.menuid?.checked || form.menuid,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdRoles
      : MESSAGE_TYPE.InsRoles, postData, () => {
        message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
        refresh();
      });
  }

  return (<>
    <HeadTitle title={$t('app.menu.sys.roles', {
      defaultMsg: '角色管理',
    })} />

    <Card>
      <TiTable
        columns={columns}
        dataSource={dataSource}
        isLoading={isLoading}
        actionBtns={[
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ]}
        selectKeys={selectKeys}
        setSelectKeys={setSelectKeys}
        onRefresh={refresh}
      />
    </Card>

    {/* 新增修改 */}
    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />
  </>);
}

const Roles = props => (
  // <KeepAlive name={keepName}>
  <RolesBase {...props} />
  // </KeepAlive>
);

export { Roles };
