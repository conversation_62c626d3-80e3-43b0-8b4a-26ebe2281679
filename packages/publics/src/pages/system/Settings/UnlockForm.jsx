import { useState } from 'react';
import { <PERSON>, Button, Form, Input, Row, Typography } from 'antd';
import { KeyOutlined, CopyOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { FormattedMessage, useIntl } from 'react-intl';
import { MD5 } from 'crypto-js';

import { UNLOCK, TiLocalStorage } from '@utils/storage';

const { Text } = Typography;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const formTailLayout = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 18, offset: 6 },
  },
};

const UnlockForm = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [localPwd, setLocalPwd] = useState(TiLocalStorage.get(UNLOCK));

  const fromPath = location.state?.from;

  const onFinish = (values) => {
    // console.log(values, values.password, fromPath, localPwd);
    let pwd = MD5(values.password).toString();
    pwd += fromPath ? '1' : '0';
    TiLocalStorage.set(UNLOCK, pwd);

    if (fromPath) {
      navigate('/auth/unlock', {
        replace: true,
        state: { from: fromPath }
      });
    } else {
      setLocalPwd(pwd);
    }
  }

  const toDelete = () => {
    TiLocalStorage.remove(UNLOCK);

    setLocalPwd(null);
  }

  return (<Card title={$t('app.unlock.title')} className="ti-unlock-card">
    {localPwd ? (<Row justify="space-between" align="middle">
      <Text type="secondary">
        <FormattedMessage id="app.unlock.added" />
      </Text>
      <Button type="danger" onClick={toDelete}>
        <FormattedMessage id="app.general.delete" />
      </Button>
    </Row>) : (<>
      <Text type="secondary">
        <FormattedMessage id="app.unlock.tip" />
      </Text>
      <Form
        {...formItemLayout}
        name="unlockform"
        onFinish={onFinish}
        className="ti-unlock-form"
      >
        <Form.Item
          name="password"
          label={$t('app.options.newpassword')}
          rules={[{ required: true }]}
        >
          <Input
            prefix={<KeyOutlined className="ti-form-item-icon" />}
            type="password"
          />
        </Form.Item>
        <Form.Item
          name="repassword"
          label={$t('app.options.repassword')}
          rules={[
            { required: true },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                } else {
                  return Promise.reject(new Error(
                    $t('app.options.repassword.error')
                  ));
                }
              }
            }),
          ]}
        >
          <Input
            prefix={<CopyOutlined className="ti-form-item-icon" />}
            type="password"
          />
        </Form.Item>
        <Form.Item {...formTailLayout}>
          <Button type="primary" htmlType="submit">
            <FormattedMessage id="app.general.sure" />
          </Button>
        </Form.Item>
      </Form>
    </>)}
  </Card>);
}

export default UnlockForm;
