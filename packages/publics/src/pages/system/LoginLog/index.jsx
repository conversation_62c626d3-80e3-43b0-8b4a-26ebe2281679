import { useEffect, useState, useMemo } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { Card, message } from 'antd';
import useFetch from 'use-http';

import TiTable from '@components/Table';
import { IndexColumn } from '@components/Table/fields';
import { QueryFilter } from '@components/form';
import { searchForm } from './data';

import { MESSAGE_TYPE, SITE_URL } from '@utils/consts';
import { DEFAULT_SESSION_PARAMS } from '@utils/session';
import { errorResp } from '@utils/error-response';
import { objFilter } from '@utils/tools';

let limit = DEFAULT_CONFIGS.LIMIT;
let filterParams;

const LoginLog = () => {
  const [dataSource, setDataSource] = useState([]);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [total, setTotal] = useState(0);
  const navigate = useNavigate();
  const location = useLocation();
  const param = useParams();

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: param.id,
  }), [param]);

  const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await post(SITE_URL.EXTEND(type, params.svrid), params);

    if (response.ok) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'rspseqno',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: '登录用户',
    dataIndex: 'userid',
  }, {
    title: 'SESSION序号',
    dataIndex: 'sessionid',
  }, {
    title: 'IP 地址',
    dataIndex: 'ipaddr',
  }, {
    title: '物理地址',
    dataIndex: 'macaddr',
  }, {
    title: '',
    dataIndex: 'logevent',
  }, {
    title: '登录时间',
    dataIndex: 'logtime',
  }, {
    title: '登录结果',
    dataIndex: 'result',
  }, {
    title: '',
    dataIndex: 'logsource',
  }, {
    title: '消息',
    dataIndex: 'message',
  }];

  useEffect(() => {
    onRefresh();

    return () => {
      abort();
    }
  }, [location.key]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (params) => {
    fetchFunc(MESSAGE_TYPE.MemQryLoginLog, params, (resp) => {
      if (resp?.length > 0) {
        setDataSource(resp);
        setTotal(resp[0].rsptotnum);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    });
  };
  const onRefresh = () => {
    //   初始化赋值
    if (!filterParams) {
      filterParams = {
        ...defaultParams
      };
    }

    getTableData(filterParams);
  }
  const onSearch = (values) => {
    filterParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    getTableData(filterParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    filterParams = {
      ...filterParams,
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    };

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      limit = size;
    }

    filterParams = pageParams;

    getTableData(filterParams);
  }

  return (
    <div key={location.key}>
      <div className="ti-table-search">
        <QueryFilter
          formData={searchForm}
          onSearch={onSearch}
          isLoading={isLoading}
        />
      </div>
      <Card>
        <TiTable
          customKey={'rspseqno'}
          columns={columns}
          dataSource={dataSource}
          isLoading={isLoading}
          onRefresh={onRefresh}
          checkable={false}
          page={page}
          total={total}
          pageChange={pageChange}
        />
      </Card>
    </div>
  );
}

export default LoginLog;
