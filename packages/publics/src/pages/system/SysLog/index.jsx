import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableLogType } = TableFields;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { formatNoDateString } = dateFormat;

const SysLogBase = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [menuMap, setMenuMap] = useState({});

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
  }), [limit]);

  // const [request] = useFetch(SITE_URL.BASE);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.sys.logid'),
    dataIndex: 'logid',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.sys.loginname'),
    dataIndex: 'loginname',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.sys.logip'),
    dataIndex: 'logip',
  }, {
    title: $t('app.options.sys.logtime'),
    dataIndex: 'logtime',
    render: text => formatNoDateString(text),
  }, {
    title: $t('app.options.sys.logtype'),
    dataIndex: 'logtype',
    render: text => TableLogType(text, menuMap),
  }, {
  //   title: '消息',
  //   dataIndex: 'logmsg',
  // }, {
    title: $t('app.options.sys.logdetail'),
    dataIndex: 'logdetail',
  }];

  const createSearchForm = useMemo(() => searchForm($t, menuMap), [$t, menuMap]);

  useMount(() => getMenuTree());
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QrySysLog), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: params.beginno + item.rspseqno - 1,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getMenuTree = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryMenuSimple), {
      ...defaultParams,
      roleid: DEFAULT_CONFIGS.SHOW_CHILD ? 1 : 0,
    });

    const { data: respData } = resp;
    if (respData.menu?.length > 0) {
      const menuData = {};
      const loop = data => {
        data.forEach(item => {
          // F or f
          if (item.menutype === 70 || item.menutype === 102) {
            menuData[item.menuid] = item.menuname;
          }
          if (item.childmenu) {
            loop(item.childmenu);
          }
        });
      };
      loop(respData.menu);

      setMenuMap(menuData);
    }
  }

  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.sys.syslog', {
        defaultMsg: '日志管理',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        customKey: 'rspseqno',
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: refresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const SysLog = props => (
  // <KeepAlive name={keepName}>
  <SysLogBase {...props} />
  // </KeepAlive>
);

export { SysLog };
