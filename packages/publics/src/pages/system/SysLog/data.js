import { formOptions } from '@titd/publics/utils';

const { selectObjOptions } = formOptions;

export const searchForm = ($t, menuGroup) => [{
  valueType: 'input',
  name: 'adminid',
  label: $t('app.options.sys.adminid'),
}, {
  valueType: 'select',
  name: 'svrid',
  label: $t('app.options.sys.logtype'),
  fieldProps: {
    showSearch: true,
    options: selectObjOptions(menuGroup).sort((a, b) => (a?.label ?? '').toLowerCase().localeCompare((b?.label ?? '').toLowerCase())),
    filterOption: (input, option) => (option?.label ?? '').includes(input),
  }
}];
