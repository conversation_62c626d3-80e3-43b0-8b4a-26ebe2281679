import { TREE_ROOT } from '@utils/consts';
import { menuVisibleDict, menuTypeDict } from '@utils/dicts';
import { selectOptions, selectObjOptions } from '@utils/form-select';

export const searchForm = intl => [{
  valueType: 'input',
  name: 'name',
  label: '菜单名称'
}, {
  valueType: 'select',
  name: 'visible',
  label: '菜单状态',
  fieldProps: {
    options: selectOptions(menuVisibleDict)
  }
}];

export const addForm = (intl, menuTree) => ([{
  valueType: 'hidden',
  name: 'menuId',
  noStyle: true,
}, {
  valueType: 'treeSelect',
  name: 'menuPid',
  label: '上级菜单',
  span: 1,
  fieldProps: {
    treeData: TREE_ROOT(menuTree)
  }
}, {
  valueType: 'input',
  name: 'code',
  label: '菜单编码',
  rules: [{ required: true }],
}, {
  valueType: 'input',
  name: 'name',
  label: '菜单名称',
  rules: [{ required: true }],
}, {
  valueType: 'radio',
  name: 'type',
  label: '菜单类型',
  rules: [{ required: true }],
  fieldProps: {
    options: selectObjOptions(menuTypeDict),
    optionType: 'button',
    buttonStyle: 'solid',
  }
}, {
  valueType: 'radio',
  name: 'visible',
  label: '菜单状态',
  fieldProps: {
    options: selectOptions(menuVisibleDict)
  }
}, {
  valueType: 'number',
  name: 'sort',
  label: '显示排序',
  rules: [{ required: true }],
}, {
  valueType: 'input',
  name: 'icon',
  label: '图标'
}, {
  valueType: 'input',
  name: 'path',
  label: '请求地址',
}]);
