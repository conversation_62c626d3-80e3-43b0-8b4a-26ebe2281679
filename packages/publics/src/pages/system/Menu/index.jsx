import { useRef, useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Space, message } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import PageContent from '@components/PageContent';
import { IndexColumn } from '@components/Table/fields';
import { ModalForm, Confirm } from '@components/form';
import { searchForm, addForm } from './data';
import ActionLinks from '@components/Table/ActionLinks';
import {
  updateLink,
  insertLink,
  deleteLink,
} from '@components/actions/links';
import {
  insertBtn,
  expandBtn,
} from '@components/actions/btns';
import CreateIcon from '@components/CreateIcon';

import { MESSAGE_TYPE, SITE_URL, CURD } from '@utils/consts';
import { DEFAULT_SESSION_PARAMS } from '@utils/session';
import { errorResp } from '@utils/error-response';
import { objFilter } from '@utils/tools';
import expandAllKeys from '@utils/expand-all-keys';
import updateRowData from '@utils/update-row';

const Menu = () => {
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const intl = useIntl();

  const [dataSource, setDataSource] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const defaultParams = DEFAULT_SESSION_PARAMS();
  const [filterParams, setFilterParams] = useState({
    ...defaultParams,
    showAuto: true,
  });

  const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await post(SITE_URL.EXTEND(type, params.svrid), params);

    if (response.ok) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    title: '菜单名称',
    dataIndex: 'name',
    // render: (text, render) => (
    //   <Space size={5}>
    //     {CreateIcon(render.icon)}
    //     {text}
    //   </Space>
    // )
  }, {
    title: '菜单类型',
    dataIndex: 'type',
    // render: text => {
    //   const item = ?.find(i => i.value === text);
    //   return item ? TableTagColor(item.tag, item.label) : item;
    // },
  }, {
    title: '序号',
    dataIndex: 'sort',
    width: 80,
    align: 'center',
    render: (text, record) => <IndexColumn border level={record.level}>{text}</IndexColumn>,
  }, {
    title: '请求地址',
    dataIndex: 'path',
    render: text => text || '#',
  }, {
    title: '菜单状态',
    dataIndex: 'visible',
    // render: (text, record) => text ? TableSwitch(text, record, 'name', toChgVisible, dictMap['sys_menu_visible']) : '-'
  }, {
    title: '操作',
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 180,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        insertLink(toInsert, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const createSearchForm = useMemo(() => searchForm(intl), [intl]);
  const createAddForm = useMemo(() => addForm(intl, isModify), [intl, isModify]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(filterParams);
  }, [filterParams]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async params => {
    fetchFunc(MESSAGE_TYPE.QryMenu, params, (resp) => {
      if (resp?.length > 0) {
        const tableData = resp.map((item, idx) => ({
          ...item,
          id: idx + 1,
        }));
        setDataSource(tableData);

        // 设置默认展开
        const allKeys = expandAllKeys(resp);
        setExpandedKeys(allKeys);
      } else {
        setDataSource([]);
      }
    });
  };
  const getNewestData = async record => {
    const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryMenu), {
      ...defaultParams,
      loginname: record.loginname,
    });

    if (resp && resp.length > 0) {
      return resp[0];
    }

    return null;
  }

  const onRefresh = showAuto => {
    setFilterParams({
      ...filterParams,
      showAuto: showAuto,
    });
  }
  const onSearch = values => {
    setFilterParams({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    setFilterParams(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert', record);
    setIsModify(false);
    modalForm.current.show(0);
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      setIsModify(true);
      modalForm.current.show(1, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
        isDisabled: newRecord.roleid === 1,
      });
      setDataSource(newData);
    } else {
      message.error($t('app.general.deleted'));
      onRefresh();
    }
  }
  const deleteFunc = async (record) => {
    const deleteData = {
      ...defaultParams,
      loginname: record.loginname,
    }

    fetchFunc(MESSAGE_TYPE.DelMenu, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }
  const toDelete = (record) => {
    // console.log('Delete', record);
    Confirm({
      content: <>{record.loginname}</>,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }
  function toExpand() {
    // console.log('Expand');
    const allKeys = expandedKeys.length > 0 ? [] : expandAllKeys(dataSource);
    setExpandedKeys(allKeys);
  }

  function onExpand(expanded, record) {
    // console.log('expand:', expanded, record);
    if (expanded) {
      expandedKeys.push(record.code);
    } else {
      const idx = expandedKeys.indexOf(record.code);
      if (idx > -1) {
        expandedKeys.splice(idx, 1);
      }
    }
    setExpandedKeys([...expandedKeys]);
  }

  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdMenu
      : MESSAGE_TYPE.InsMenu, postData, () => {
        message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);

        onRefresh(!type);
      });
  }

  return (<>
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        checkable: false,
        expandable: {
          expandedRowKeys: expandedKeys,
          onExpand: onExpand
        },
        actionBtns: [
          insertBtn(toInsert),
          expandBtn(toExpand, expandedKeys.length > 0),
        ],
        onRefresh: onRefresh,
      }}
    />

    {/* 新增修改 */}
    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />
  </>);
}

const MenuAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName}>
    <Menu {...props} />
  </KeepAlive>
);

export default MenuAlive;
