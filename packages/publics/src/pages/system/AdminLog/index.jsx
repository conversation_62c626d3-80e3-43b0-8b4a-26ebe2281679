import { useEffect, useState, useMemo } from 'react';
import { useLocation, useParams, useNavigate } from 'react-router';
import { Card, App } from 'antd';
import { useRequest } from 'ahooks';

import {
  TiTable,
  TableFields,
  forms,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  Request,
  tools,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn } = TableFields;
const { QueryFilter } = forms;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;

let limit = DEFAULT_CONFIGS.LIMIT;
let filterParams;

const AdminLog = () => {
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [total, setTotal] = useState(0);
  const location = useLocation();
  const param = useParams();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: param.id,
  }), [param]);

  // const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);

  const columns = [{
    dataIndex: 'rspseqno',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: '日志类型',
    dataIndex: 'madtype',
  }, {
    title: '管理账号',
    dataIndex: 'adminid',
  }, {
    title: '日志时间',
    dataIndex: 'logtime',
  }, {
    title: 'IP 地址',
    dataIndex: 'ipaddr',
  }, {
    title: '物理地址',
    dataIndex: 'macaddr',
  }, {
    title: '消息',
    dataIndex: 'message',
  }];

  useEffect(() => {
    refresh();

    return () => {
      cancel();
    }
  }, [location.key]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async params => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryAdminLog), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      setTotal(respData[0].rsptotnum);
      return respData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onSearch = (values) => {
    filterParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(filterParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      limit = size;
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <div key={location.key}>
      <div className="ti-table-search">
        <QueryFilter
          formData={searchForm}
          onSearch={onSearch}
          isLoading={isLoading}
        />
      </div>
      <Card>
        <TiTable
          customKey={'rspseqno'}
          columns={columns}
          dataSource={dataSource}
          isLoading={isLoading}
          onRefresh={refresh}
          checkable={false}
          page={page}
          total={total}
          pageChange={pageChange}
        />
      </Card>
    </div>
  );
}

export default AdminLog;
