import { RecoilRoot } from 'recoil';
import { IntlProvider } from 'react-intl';
import { App, ConfigProvider } from 'antd';
import dayjs from 'dayjs';
import { HelmetProvider } from 'react-helmet-async';

import StyleRegistry from './style-registry';

import { loadLocale } from '@titd/publics/assets';

import 'antd/dist/reset.css';

const AntdConfigProvider = ({ children, ...props }) => {
  const { locale, message, antLocale, dayjsLocale } = loadLocale('zh-CN');
  // 日期控件中文
  dayjs.locale(dayjsLocale);

  return (
    <RecoilRoot>
      <IntlProvider locale={locale} messages={message}>
        <HelmetProvider>
          <ConfigProvider locale={antLocale}>
            <StyleRegistry {...props}>
              <div style={{ height: '100vh' }}>{children}</div>
            </StyleRegistry>
          </ConfigProvider>
        </HelmetProvider>
      </IntlProvider>
    </RecoilRoot>
  );
}

const AntdStyleRegistry = ({ children, ...props }) => {
  return (
    <AntdConfigProvider {...props}>
      <App style={{ minHeight: '100%' }}>{children}</App>
    </AntdConfigProvider>
  );
}

export { AntdStyleRegistry };
