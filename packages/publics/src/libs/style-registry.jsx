import { theme } from 'antd';
import { Style<PERSON>rovider, ThemeProvider, extractStaticStyle } from 'antd-style';

import { tools } from '@titd/publics/utils';

const { setAlpha } = tools;

const StyleRegistry = ({ children, ...props }) => (
  <StyleProvider cache={extractStaticStyle.cache}>
    <ThemeProvider
      // prefixCls={props.prefixCls}
      defaultThemeMode={props.theme}
      theme={{
        cssVar: true,
        hashed: false,
        token: {
          algorithm: theme.compactAlgorithm,
          colorPrimary: props.colorPrimary,
          colorInfo: props.colorPrimary,
          // colorSuccess: '#6db743',
        },
        components: {
          Table: {
            // 设置偶数行的背景色
            rowEvenBg: '#fafafa',
          },
        }
      }}
      customToken={({ token }) => ({
        bgLayout: `linear-gradient(${token.colorBgContainer}, ${token.colorBgLayout} 28%)`,
        colorTextAppListIcon: token.colorTextSecondary,
        // appListIconHoverBgColor: token?.sider?.colorBgMenuItemSelected,
        appListIconHoverBgColor: setAlpha(token.colorTextBase, 0.04),
        colorBgAppListIconHover: setAlpha(token.colorTextBase, 0.04),
        colorTextAppListIconHover: token.colorTextBase,
        header: {
          colorBgHeader: setAlpha(token.colorBgElevated, 0.6),
          colorBgScrollHeader: setAlpha(token.colorBgElevated, 0.8),
          colorHeaderTitle: token.colorText,
          colorBgMenuItemHover: setAlpha(token.colorTextBase, 0.03),
          colorBgMenuItemSelected: 'transparent',
          colorBgMenuElevated: token.colorBgElevated,
          colorTextMenuSelected: setAlpha(token.colorTextBase, 0.95),
          colorBgRightActionsItemHover: setAlpha(token.colorTextBase, 0.03),
          colorTextRightActionsItem: token.colorTextTertiary,
          heightLayoutHeader: 56,
          colorTextMenu: token.colorTextSecondary,
          colorTextMenuSecondary: token.colorTextTertiary,
          colorTextMenuTitle: token.colorText,
          colorTextMenuActive: token.colorText,
        },
        sider: {
          paddingInlineLayoutMenu: 8,
          paddingBlockLayoutMenu: 0,
          colorBgCollapsedButton: token.colorBgElevated,
          colorTextCollapsedButtonHover: token.colorTextSecondary,
          colorTextCollapsedButton: setAlpha(token.colorTextBase, 0.25),
          colorMenuBackground: 'transparent',
          colorMenuItemDivider: setAlpha(token.colorTextBase, 0.06),
          colorBgMenuItemHover: setAlpha(token.colorTextBase, 0.03),
          colorBgMenuItemSelected: setAlpha(token.colorTextBase, 0.04),
          colorTextMenuItemHover: token.colorText,
          colorTextMenuSelected: setAlpha(token.colorTextBase, 0.95),
          colorTextMenuActive: token.colorText,
          colorTextMenu: token.colorTextSecondary,
          colorTextMenuSecondary: token.colorTextTertiary,
          colorTextMenuTitle: token.colorText,
          colorTextSubMenuSelected: setAlpha(token.colorTextBase, 0.95),
        },
        pageContainer: {
          colorBgPageContainer: 'transparent',
          paddingInlinePageContainerContent: 40,
          paddingBlockPageContainerContent: 32,
          colorBgPageContainerFixed: token.colorBgElevated,
        },
      })}
    >{children}</ThemeProvider>
  </StyleProvider>
);

export default StyleRegistry;
