import { DEFAULT_CONFIGS } from './consts';
import { SESSION, TiLocalStorage, TiSessionStorage } from './storage';

export const DEFAULT_PARAMS = {
  svrid: DEFAULT_CONFIGS.SERVER_ID,
};

export const DEFAULT_PAGE = {
  start: DEFAULT_CONFIGS.PAGE,
  limit: DEFAULT_CONFIGS.LIMIT,
};

export const DEFAULT_SESSION = () => {
  const globalUser = DEFAULT_CONFIGS.USER;
  // console.log('globalUser', globalUser);

  if (globalUser) {
    return globalUser;
  } else {
    const localUser = TiLocalStorage.getJson(SESSION) || TiSessionStorage.getJson(SESSION);

    // console.log('localUser', localUser);

    if (localUser) {
      DEFAULT_CONFIGS.USER = localUser;

      return localUser;
    } else {
      return null;
    }
  }
}

export const DEFAULT_SESSION_PARAMS = () => {
  const localUser = DEFAULT_SESSION();

  // console.log('params', localUser);

  if (localUser) {
    return {
      ...DEFAULT_PARAMS,
      webuserid: localUser.id,
      sessionid: localUser.session,
    };
  } else {
    return DEFAULT_PARAMS;
  }
};
