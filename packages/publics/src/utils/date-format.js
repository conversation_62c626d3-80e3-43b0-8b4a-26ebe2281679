import dayjs from 'dayjs';

const formatText = 'YYYY-MM-DD';
const formatTextFull = 'YYYY-MM-DD HH:mm:ss';

// 日期格式化
export const formatDate = date => date ? dayjs(String(date)).format(formatText) : '-';
export const formatDateNoTime = date => date && dayjs(date).format('YYYYMMDD');

// 日期格式化(带时间)
export const formatDateFull = date => date ? dayjs(String(date)).format(formatTextFull) : '-';

// 毫秒数格式化
export const formatMsToDateFull = ms => ms > 0 ? dayjs.unix(ms).format(formatTextFull) : '-';

// 时间格式化
export const formatTime = time => {
  time = time ? String(time) : '';
  if (time.length === 5) {
    time = '0' + time;
  }

  return time.replace(/\B(?=(\d{2})+(?!\d))/g, ':');
}

// 格式化非日期格式字符串 20220615-13:30:29.0
export const formatNoDateString = str => {
  if (!str) return '-';

  let date = str.substring(0, str.length - 2);
  date = date.replace(/-/, ' ');
  return formatDateFull(date);
}
