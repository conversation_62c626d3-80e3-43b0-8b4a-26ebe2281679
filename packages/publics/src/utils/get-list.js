import { MESSAGE_TYPE, SITE_URL, PUBLIC_PATH } from './consts';
import { ERRCODE, TiSessionStorage } from './storage';

import { optDiff } from './opt-diff';
import { errorResp } from './error-response';

// 数组序号生成
export const rebuildIndex = (arr) => {
  return [...arr].map((item, idx) => ({
    ...item,
    id: idx + 1,
  }));
}

// 获取场下用户列表
export const getUserList = async (post, defaultParams, setUser) => {
  const user = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserInfo), {
      ...defaultParams,
      status: -1,
    });

  const { data: userData } = user;
  if (userData?.length > 0) {
    const userGroup = userData.map(item => ({
      label: item.name ? `${item.userid} (${item.name})` : item.userid,
      value: item.userid,
      status: item.status,
    }));
    setUser(userGroup);
    // TiSessionStorage.setJson(USER, userGroup);
  } else {
    setUser([]);
  }
  // const localUser = TiSessionStorage.getJson(USER);
  // if (localUser) {
  //   setUser(localUser);
  // } else {

  // }
}

// 获取场上风控用户
export const getRiskUserList = async (post, defaultParams, setUser) => {
  const user = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserRisk), {
      ...defaultParams,
      isglobal: -1,
    });

  const { data: userData } = user;
  if (userData?.length > 0) {
    const userGroup = userData.map(item => ({
      label: item.name ? `${item.userid} (${item.name})` : item.userid,
      value: item.userid,
      status: item.status,
    }));
    setUser(userGroup);
  } else {
    setUser([]);
  }
}

// 获取场上用户列表
export const getMemUserList = async (post, defaultParams, setUser) => {
  const memUser = await post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryUserInfo), {
    ...defaultParams,
    endno: 999
  });

  const { data: memUserData } = memUser;
  if (memUserData?.length > 0) {
    const memUserGroup = memUserData.map(item => ({
      label: item.username ? `${item.userid} (${item.username})` : item.userid,
      value: item.userid,
    }));
    setUser(memUserGroup);
    // TiSessionStorage.setJson(userNameSpace, memUserGroup);
  } else {
    setUser([]);
  }
  // const userNameSpace = MEM_USER(paramId);
  // const localUser = TiSessionStorage.getJson(userNameSpace);
  // if (localUser) {
  //   setUser(localUser);
  // } else {

  // }
}

// 获取服务器列表
export const getServerList = async (post, defaultParams, setServer, navigate) => {
  const serverRsp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryServer), defaultParams);

  const { data: serverData } = serverRsp;
  if (serverData?.server?.length > 0) {
    // 服务器列表
    const servers = serverData.server.map(item => ({
      label: `${item.servername}(${item.serverid})`,
      value: item.serverid,
    }));

    setServer(servers);
  } else {
    errorResp(serverRsp, navigate);
  }
};

// 获取服务器和交易所
export const getServerExchange = async (post, defaultParams, setServer, navigate, setExchange) => {
  const serverRsp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryServer), defaultParams);

  const { data: serverData } = serverRsp;
  if (serverData?.server?.length > 0) {
    // 服务器列表, 交易所列表
    const exchanges = {};
    const servers = serverData.server.map(item => {
      let exchangeArr = item.exchangelist?.split(',');
      // 判断是否现货: 8上海现货, 9深圳现货
      // let isSpot = false;
      // exchangeArr = exchangeArr.map(i => {
      //   if (i === '8' || i === '9') {
      //     isSpot = true;
      //     return String(i - 7);
      //   }

      //   return i;
      // });

      // exchanges[item.serverid] = [exchangeArr, isSpot];

      exchanges[item.serverid] = exchangeArr;

      return {
        label: `${item.servername}(${item.serverid})`,
        value: item.serverid,
        // isSpot: isSpot,
      }
    });

    // TiSessionStorage.setJson(SERVER, servers);
    // TiSessionStorage.setJson(EXCHANGE, exchanges);
    setExchange && setExchange(exchanges);
    // servers = serverDict.concat(servers);
    setServer && setServer(servers);
    return servers;
  } else {
    errorResp(serverRsp, navigate);
  }
  // const localServer = TiSessionStorage.getJson(SERVER);
  // if (localServer) {
  //   setServer(localServer);
  // } else {

  // }
};

// 获取集群分区和服务器列表
export const getSegmentSvrList = async (post, defaultParams, setSegment, navigate, setServer) => {
  const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.SegmentSvr), defaultParams);

  const { data: respData } = resp;
  if (respData?.length > 0) {
    const servers = {};
    const segments = respData?.map(item => {
      servers[item.segmentid] = item.serverlist;

      return {
        label: item.segmentid,
        value: item.segmentid,
      }
    });

    setSegment(segments || []);
    setServer && setServer(servers);
  } else {
    errorResp(resp, navigate);
  }
}

// 获取集群列表
export const getClusterList = async (post, defaultParams, setCluster, navigate, setServer) => {
  const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.SegmentSvr), defaultParams);

  const { data: respData } = resp;
  if (respData?.length > 0) {
    const clusters = optDiff(respData, 'clusterid');
    setCluster && setCluster(clusters);

    if (setServer) {
      const svrMap = {};
      respData.forEach(segItem => {
        const serverlist = segItem.serverlist;
        if (serverlist?.length > 0) {
          serverlist.forEach(item => {
            svrMap[item.serverid] = `${segItem.clusterid}:${item.serverid}`;
          });
        }
      });
      setServer(svrMap);
    }
  } else {
    errorResp(resp, navigate);
  }
}

// 获取交易编码/股东代码列表
export const getClientList = async (post, defaultParams, setClient) => {
  const client = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserClient), defaultParams);

  const { data: clientData } = client;
  if (clientData?.length > 0) {
    const clientGroup = clientData.map(item => ({
      label: item.accountid ? `${item.clientid} (${item.accountid})` : item.clientid,
      value: item.clientid,
      rightlen: client.length,
    }));
    setClient(clientGroup);
    // TiSessionStorage.setJson(CLIENT, clientGroup);
  }
  // const localClient = TiSessionStorage.getJson(CLIENT);
  // if (localClient) {
  //   setClient(localClient);
  // } else {

  // }
}

// 获取资金账户
export const getAccountList = async (post, defaultParams, setAccount) => {
  const account =  await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserClient), defaultParams);

  const { data: accountData } = account;
  if (accountData?.length > 0) {
    const accountGroup = optDiff(accountData, 'accountid');
    setAccount(accountGroup);
  }
}

// 获取用户交易编码/股东代码关系
// export const getUserClientList = async (post, defaultParams, setUserClient, userId) => {
//   const userClient =  await post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryUserClient), {
//       ...defaultParams,
//       userid: userId,
//     });

//   if (userClient?.length > 0) {
//     const userClientGroup = optDiff(userClient, 'clientid');
//     setUserClient(userClientGroup);
//     return userClientGroup[0].value;
//   } else {
//     setUserClient([]);
//     return null;
//   }
// }

// 获取用户交易编码/股东代码关系
export const getUserClientList = async (post, defaultParams, setUserClient) => {
  const userClient =  await post(SITE_URL.EXTEND(MESSAGE_TYPE.OwUserClient), {
    ...defaultParams,
    beginno: undefined,
    endno: undefined,
    usertype: 0,
  });

  const { data: userClientData } = userClient;
  if (userClientData[0]?.userclient?.length > 0) {
    const userClientGroup = userClientData[0]?.userclient.map(item => ({
      label: `${item.userid} - ` + (item.username ? `${item.clientid} (${item.username})` : item.clientid),
      value: `${item.clientid}:${item.userid}`,
    }))
    setUserClient(userClientGroup);
  } else {
    setUserClient([]);
  }
}

// 获取用户交易编码关系数组，分散用
export const getUserClientGroup = async (post, defaultParams, setUserClient) => {
  const userClient =  await post(SITE_URL.EXTEND(MESSAGE_TYPE.OwUserClient), {
    ...defaultParams,
    beginno: undefined,
    endno: undefined,
    usertype: 0,
  });

  const { data: userClientData } = userClient;
  if (userClientData[0]?.userclient?.length > 0) {
    const userClientGroup = userClientData[0]?.userclient.map(item => ({
      username: item.username ? `${item.userid} (${item.username})` : item.userid,
      userid: item.userid,
      clientid: item.clientid,
    }))
    setUserClient(userClientGroup);
  } else {
    setUserClient([]);
  }
}

// 根据用户遍历交易编码
export const getClientByUser = (userClient, userId) => userClient.filter(i => i.userid === userId)?.map(i => ({
  label: i.clientid,
  value: i.clientid,
}));

// 根据交易所获取品种(期权、期货)
export const getProductByExchange = async (request, params, setProduct) => {
  const resp = await request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryProductStk), params);

  const { data: respData } = resp;
  const products = optDiff(respData, 'productid');

  setProduct(products);
}

// 根据交易所获取品种(现货)
export const getProductByExchangeStk = async (request, params, setProduct) => {
  const resp = await request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryInstrumentType), params);

  const flatProduct = [];
  const { data: respData } = resp;
  if (respData?.length > 0) {
    respData.forEach(item => {
      const idx = flatProduct.findIndex(i => i.value === item.productid);

      if (idx === -1) {
        flatProduct.push({
          label: `${item.productname} [${item.productid}]`,
          value: item.productid,
        });
      }
    });
  }

  setProduct(flatProduct);
}

// 获取系统资金账户
export const getSubAccountList = async (post, defaultParams, setAccount) => {
  const account =  await post(SITE_URL.EXTEND(MESSAGE_TYPE.QrySubClient), defaultParams);

  const { data: accountData } = account;
  if (accountData?.length > 0) {
    const accountGroup = optDiff(accountData, 'accountid');
    setAccount(accountGroup);
  }
}

// 获取场上历史用户列表
export const getHistUserList = async (get, date, setUser) => {
  const resp = await get(SITE_URL.HISTORY_PATH('UserSimple', {
    histat: date,
  }));

  const { data: respData } = resp;
  if (respData?.code === 200) {
    const userGroup = respData.data.map(item => ({
      label: item.username ? `${item.userid} (${item.username})` : item.userid,
      value: item.userid,
    }));
    setUser(userGroup);
  } else {
    setUser([]);
  }
}

// 获取场上历史用户交易编码关系
export const getHistUserClientList = async (get, date, setUserClient, showGroup = false) => {
  const resp = await get(SITE_URL.HISTORY_PATH('UserClientSimple', {
    histat: date,
  }));

  const { data: respData } = resp;
  if (respData?.code === 200) {
    const userClientGroup = respData.data.map(item => showGroup ? ({
      username: item.username ? `${item.userid} (${item.username})` : item.userid,
      userid: item.userid,
      clientid: item.clientid,
    }) : ({
      label: `${item.userid} - ` + (item.username ? `${item.clientid} (${item.username})` : item.clientid),
      value: `${item.clientid}:${item.userid}`,
    }));
    setUserClient(userClientGroup);
  } else {
    setUserClient([]);
  }
}

// 获取错误信息
export const getErrorCodeMsg = async (get, setErrCodeMsg) => {
  let errCodes = TiSessionStorage.getJson(ERRCODE) || [];
  if (errCodes.length === 0) {
    const resp = await get(`${PUBLIC_PATH.ERROR_CODE}?t=${+new Date()}`, {
      baseURL: ''
    });

    const { data: respData } = resp;
    if (Array.isArray(respData)) {
      TiSessionStorage.setJson(ERRCODE, respData);
      errCodes = respData;
    }
  }

  setErrCodeMsg(errCodes);
}
