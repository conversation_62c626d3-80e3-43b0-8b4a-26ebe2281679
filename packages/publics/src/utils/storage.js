import { SYSTEM_NAME } from './consts';

const GLOBAL_NAMESPACE = `titd.${SYSTEM_NAME[0]}.`;
export const LOCALE = GLOBAL_NAMESPACE + 'locale';
export const SESSION = GLOBAL_NAMESPACE + 'session';
export const USER = GLOBAL_NAMESPACE + 'user';
export const SERVER = GLOBAL_NAMESPACE + 'server';
export const CLIENT = GLOBAL_NAMESPACE + 'client';
export const PRODUCT = GLOBAL_NAMESPACE + 'product';
export const IMPORTLOG = GLOBAL_NAMESPACE + 'importlog';
export const EXCHANGE = GLOBAL_NAMESPACE + 'exchange';
export const UNLOCK = GLOBAL_NAMESPACE + 'unlock';
export const HISTORY_DATE = GLOBAL_NAMESPACE + 'historydate';
export const ERRCODE = GLOBAL_NAMESPACE + 'errcode';

// 不同服务器存放位置不一样
export const MEM_USER = paramId => `${GLOBAL_NAMESPACE}${paramId}.user`;

class LocalStorage {
  get(key) {
    return localStorage.getItem(key);
  }

  set(key, value) {
    if (!value && value === undefined) return;
    localStorage.setItem(key, value);
  }

  getJson(key) {
    const json = localStorage.getItem(key);
    return json ? JSON.parse(json) : null;
  }

  setJson(key, value) {
    if (!value && value === undefined) return;
    localStorage.setItem(key, JSON.stringify(value));
  }

  remove(key) {
    localStorage.removeItem(key);
  }

  clear() {
    // localStorage.clear();
    localStorage.removeItem(SESSION);
  }
}

export const TiLocalStorage = new LocalStorage();

class SessionStorage {
  get(key) {
    return sessionStorage.getItem(key);
  }

  set(key, value) {
    if (!value && value === undefined) return;
    sessionStorage.setItem(key, value);
  }

  getJson(key) {
    const json = sessionStorage.getItem(key);
    return json ? JSON.parse(json) : null;
  }

  setJson(key, value) {
    if (!value && value === undefined) return;
    sessionStorage.setItem(key, JSON.stringify(value));
  }

  remove(key) {
    sessionStorage.removeItem(key);
  }

  clear() {
    // sessionStorage.clear();
    sessionStorage.removeItem(SESSION);
  }
}

export const TiSessionStorage = new SessionStorage();
