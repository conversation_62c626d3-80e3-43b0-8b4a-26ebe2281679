import { useState, useEffect } from 'react';

import { Request } from './request';

const useAxios = axiosParams => {
  const [response, setResponse] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  const fetchData = async params => {
    try {
      setLoading(true);
      const result = await Request.request(params);
      setResponse(result.data);
      setError(null);
    } catch (err) {
      setError(err);
      setResponse(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(axiosParams);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 返回状态和重新获取数据的方法
  return { response, error, loading, refetch: () => fetchData(axiosParams) };
};

export default useAxios;
