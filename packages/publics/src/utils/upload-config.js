import { Upload } from 'antd';

export const uploadConfig = (intl, message, func) => ({
  name: 'file',
  maxCount: 1,
  beforeUpload: file => {
    const isCSV = file.name.match(/.csv/);
    if (!isCSV) {
      message.error(file.name + intl.formatMessage({ id: 'app.options.db.datacompare.error' }));
    }
    return !!isCSV || Upload.LIST_IGNORE;
  },
  progress: {
    strokeColor: {
      '0%': '#108ee9',
      '100%': '#87d068',
    },
    strokeWidth: 3,
    format: percent => `${parseFloat(percent.toFixed(2))}%`,
  },
  onChange: async info => {
    const { status, response } = info.file;
    // if (status !== 'uploading') {
    //   console.log(info.file, info.fileList);
    // }
    if (status === 'done') {
      message.success(info.file.name + intl.formatMessage({ id: 'app.general.upload.success' }));
      func && func(response);
    } else if (status === 'error') {
      message.error(info.file.name + intl.formatMessage({ id: 'app.general.upload.error' }));
    }
  },
  // onDrop(e) {
  //   console.log('Dropped files', e.dataTransfer.files);
  // },
});
