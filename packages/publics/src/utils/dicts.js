import { DEFAULT_CONFIGS } from './consts';

// 用户状态
export const userStatusDict = ['正常', '禁止登录'];
export const userStatusColor = ['green', 'red'];

// 用户类型: -1:所有用户
export const userTypeDict = ['普通用户', '风控用户'];

// 交易所
export const exchangeCodeDict = ['', 'SSE', 'SZSE', 'CFFEX', 'SHFE', 'INE', 'DCE', 'CZCE', '', 'BSE'];
export const exchangeDict = [
  ['-', '上交所', '深交所'],
  ['-', '', '', '中金所', '上期所'],
  ['-', '上交所', '深交所', '', '', '', '', '', '', '北交所'],
][DEFAULT_CONFIGS.SYSTEM_TYPE];
export const exchangeAllDict = ['', '上交所', '深交所', '中金所', '上期所', '能源中心', '大商所', '郑商所', '', '北交所'];
export const exchangeAllFullDict = ['', '上海证券交易所', '深圳证券交易所', '中国金融期货交易所', '上海期货交易所', '上海能源交易所 || 上海国际能源交易中心', '大连商品期货交易所', '郑州商品期货交易所', '', '北京证券交易所'];
export const exchangeC2NDict = {
  'SSE': '上交所',
  'SZSE': '深交所',
  'CFFEX': '中金所',
  'SHFE': '上期所',
  'INE': '能源中心',
  'DCE': '大商所',
  'CZCE': '郑商所',
  'BSE': '北交所',
}

// 交易类型
export const clientTypeDict = ['-', '投机', '套利', '套保'];

// 交易状态
export const clientStatusDict = [
  ['正常', '禁止开仓', '禁止交易', '无权限'],
  ['正常', '禁止开仓', '禁止交易', '无权限'],
  ['正常', '禁止买入', '禁止交易', '无权限'],
][DEFAULT_CONFIGS.SYSTEM_TYPE];
export const clientChgStatusDict = [
  ['正常', '禁止开仓', '禁止交易'],
  ['正常', '禁止开仓', '禁止交易'],
  ['正常', '禁止买入', '禁止交易'],
][DEFAULT_CONFIGS.SYSTEM_TYPE];

// 账户类型
export const DEFAULT_ACCOUNT_TYPE = ([2, 2, 1])[DEFAULT_CONFIGS.SYSTEM_TYPE];
export const accountTypeDict = ['-', '现货账户', '衍生品账户'];

// 交易权限
export const clientRightDict = [
  ['-', '检查自成交', '行权日不能开义务仓', '禁止行权', '禁止组合行权'],
  ['-', '检查自成交', '行权日不能开义务仓', '禁止行权', '禁止组合行权'],
  ['-', '检查自成交', '可交易ST', '退市整理期可交易'].concat(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? ['', '', '优先归还负债'] : []),
][DEFAULT_CONFIGS.SYSTEM_TYPE];

// 买卖方向
export const sideDict = DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? ['-', '普买', '普卖', '融买', '融卖', '买券还券', '卖券还款', '直接还券', '直接还款'] : ['-', '买', '卖'];
export const sideColor = ['', 'red', 'green'];

// 订单状态
export const ordStatusDict = {
  0: '已缓存', // 返回数据
  1: '已报入',
  2: '部分成交',
  3: '全部成交',
  4: '已撤单',
  8: '已拒绝',
  20: '已缓存', // 发送请求
};
export const ordStatusExtraEict = {
  50: '已顶单',
  51: '已撮合',
};
// 撤单状态
export const ordAcitonStatusDict = ['-', '已报入', '', '', '', '', '', '', '已拒绝'];
export const ordActionExtraDict = {
  50: '系统撤单',
};
export const ordStatusColor = ['', 'blue', 'purple', 'green', 'orange', '', '', '', 'red'];

// 操作类型
export const ordActionTypeDict = {
  8: '证券锁定',
  13: '报单录入',
  14: '报单撤销',
  15: '股票报单录入',
  16: '股票报单撤销',
  17: '报价录入',
  18: '报价撤销',
  19: '询价操作',
  20: '行权',
  21: '行权撤销',
  22: '组合与拆分',
  23: '组合行权',
  24: '单边平仓',
  25: '股票非交易报单录入',
  26: '股票非交易报单撤销',
};

// 组合拆分
export const combDict = {
  66: '组合',
  67: '拆分',
};

// 服务器列表
export const serverDict = [{
  label: '所有服务器',
  value: 0,
  exchange: ''
}];

// 报单价格条件
export const priceTypeDict = [
  '-',
  '市价',
  '限价',
  '本方最优',
  '市价剩余转限价',
  '最优五档即时成交剩余撤销的市价订单',
  '最优五档即时成交剩余转限价的市价订单',
  '本方最优价格申报的市价订单',
  '对手方最优价格申报的市价订单'
];

// 有效期类型
export const timeInforceDict = [
  '-',
  '当日有效(GFD)',
  '本节有效(GIS)',
  '即时成交剩余撤销(IOC)',
  '全部成交否则撤销(FOK)',
];

// 订单所有类型
export const ownerTypeDict = {
  0: '-',
  1: '个人投资者发起',
  101: '交易所发起',
  102: '经营机构（包括其风险管理部门）发起',
  103: '机构投资者发起',
  104: '自营交易发起',
  105: '流动性服务提供商发起',
  106: '结算结构发起',
};

// 开平标记
export const offsetFlagDict = ['-', '开仓', '平仓', '平今'];

// 备兑标签
export const coveredDict = ['-', '备兑', '非备兑'];

// 成交量类型
export const volumeDict = ['-', '任何数量', '最小数量', '全部数量'];

// 触发条件
export const trigDict = ['-', '立即', '止损'];

// 产品类型 (ASCII码 <-> 字符)
export const productShortDict = {
  'S': '股票',   // 83
  'O': '期权',   // 79
};
export const productDict = {
  'S': '股票',   // 83
  'F': '期货',   // 70
  'O': '期权',   // 79
  'E': '基金',   // 69
  'D': '债券、集合资产管理计划、债券预发行、定向可转债取',   // 68
  'R': '权证',   // 82
  'C': '公募REITs',   // 67
  'G': '国债',   // 71
};
export const productTypeDict = {
  0: '-',
  70: '期货',
  79: '期权',
};

// 期权类型
export const optionsDict = {
  'C': '认购',   // 67
  'P': '认沽',   // 80
};

// 合约方向
export const legSideDict = {
  'L': '权利仓',   // 76
  'S': '义务仓',   // 83
};

// 组合类型(label 与 value 相同)
export const combIdDict = ['CNSJC', 'PXSJC', 'PNSJC', 'CXSJC', 'KS', 'KKS', 'ZBD'];

// 交易权限
export const tradingRightDict = ['不允许', '允许'];

// 是或否
export const yesNoDict = ['否', '是'];
export const yesNoColor = ['red', 'green'];

// 用户范围
export const userRangeDict = ['当前用户', '所有用户'];
// 系统选择
export const sysRangeDict = ['本系统', '其他系统'];

// 角色
export const roleDict = ['-', '超级管理员', '风控', '结算', '运维', '开户', '结算开户', '监控'];

// 菜单
export const menuTypeDict = {
  'M': '目录',
  'C': '菜单',
  'F': '功能',
}
export const menuVisibleDict = ['显示', '隐藏'];

// 用户状态
export const adminStatusDict = ['正常', '停用'];

// Session 是否在线
export const isTimeoutDict = ['在线', '离线', '全部'];

// 分配模式
export const alloctTypeDict = [
  ['-', '按权益', '按可用资金'],
  ['-', '按权益', '按可用资金'],
  ['-', '-', '按可用资金'],
][DEFAULT_CONFIGS.SYSTEM_TYPE];
// 资金分配方式
export const assignTypeDict = ['-', '按比例', '按金额'];

// 资金划转
export const transferDict = ['主席 -> 本系统', '本系统 -> 主席'];
export const seatDict = [{}, {}, {
  'jinzheng': '金证',
  // 'CTP': 'CTP',
}][DEFAULT_CONFIGS.SYSTEM_TYPE];
export const seatTypeDict = ['主席'];
export const transferStatusDict = ['失败', '未操作', '成功'];
export const transferStatusColor = ['red', '', 'green'];
export const transferDirectDict = ['出金', '入金'];
export const bclientDict = ['管理端', '客户端'];
export const shareDirectDict = ['划出', '划入'];

// 登录状态
export const loginStatusDict = ['成功', '失败'];

// 开平转换类型
// export const openCloseDict = ['-', '对手不足按持仓数量转', '对手不足按报单数量开'];
export const openCloseDict = ['-', '对手不足按实际数量转', '对手不足不做开平转换'];

// 每秒笔数限制
export const isLimitDict = ['限制', '不限制'];

// 自成交处理
// export const selfTradeDict = ['返回错单', '顶单', '自动增仓'];
export const selfTradeDict = {
  // 0: '不控制成交',
  0: '-',
  1: '不能有任何自成交',
  // 2: '可以根据设置有一定数量的自成交',
  3: '将前面报单撤销(顶单)',
  4: '将前面报单撤销以后内部账户自己撮合',
};

// 交易行为
export const creditSideDict = ['融资', '融券'];
export const cashTypeDict = ['普通头寸', '专项头寸'];
export const creditTypeSmallDict = ['头寸调整',  '授信额度调整'];
export const creditTypeDict = ['-', '资金头寸调整', '股份头寸调整', '融券授信额度调整', '融资授信额度调整'];
export const creditTypeMap = new Map([
  [1, '资金头寸'],
  [4, '融资授信额度'],
  [3, '融券授信额度'],
]);

// 网关类型
export const gatewayTypeDict = ['-', '私有', '共用'];
// 网关平台类型
export const frontTypeDict = {
  1: ['-', '期权', '现货', '债券'],
  2: ['-', '现货集中竞价交易平台', '综合金融服务平台', '非交易处理平台', '衍生品集中竞价交易平台', '国际市场互联平台', '固定交易处理平台'],
}
export const gatewayStatusDict = ['未连接', '已连接未登录', '已登录'];
export const gatewayStatusColor = ['red', 'orange', 'green'];
export const gatewayActionDict = ['暂停', '启动'];

// 黑名单查询范围
export const blackRangeDict = ['全部', '在有效时间内', '不在有效时间内'];

// 还款详情 (ASCII码 <-> 字符)
export const repayTypeDict = ['直接还款', '', '卖出还款', '平仓还款'];
export const debtsTypeDict = {
  'a': '本金及费用',     // 97
  'b': '融券负债市值',     // 98
  'c': '融资利息',         // 99
  'd': '融券费用',         // 100
  'e': '融资逾期息费',     // 101
  'f': '融券逾期费用',     // 102
  'g': '融资逾期息费罚息', // 103
  'h': '融券逾期费用罚息', // 104
  'i': '融券逾期权益',     // 105
  'j': '融券逾期权益罚息', // 106
  'k': '融资逾期负债罚息', // 107
  'l': '融券逾期负债罚息', // 108
  'w': '融券权益',        // 119
}

// 颜色
// export const tagColors = ['magenta', 'red', 'volcano', 'orange', 'gold', 'lime', 'green', 'cyan', 'blue', 'geekblue', 'purple'];
export const tagColors = ['green', 'orange', 'red', 'purple', 'blue', 'magenta', 'cyan', 'geekblue'];
export const tagNormalColors = ['blue', 'green', 'orange', 'red', 'purple', 'volcano', 'cyan', 'magenta', 'geekblue', 'gold', 'lime'];

// 业务类型
export const businessTypeDict = [
  '-',
  '股票发行',
  '配股、科创板配售',
  '配转债',
  '要约预售',
  '要约撤销',
  '开放式基金申购',
  '开放式基金赎回',
  '开放式基金认购',
  '开放式基金转托管',
  '开放式基金分红设置',
  '开放式基金转换',
  '余券划转',
  '还券划转',
  '担保品划入',
  '担保品划出',
  '券源划入',
  '券源划出',
  '质押式回购(逆回购)'
];

// 获取交易所信息的工具函数
export const getExchangeInfo = {
  // 获取所有有效的交易所列表（排除空值和'-'）
  getAllExchanges: () => exchangeDict.filter(exchange => exchange && exchange !== '-'),

  // 获取指定索引的交易所
  getExchange: (index) => exchangeDict[index] || '-',

  // 获取交易所的索引
  getExchangeIndex: (exchangeName) => exchangeDict.indexOf(exchangeName),

  // 获取所有交易所（包括'-'和空值）
  getRawExchanges: () => [...exchangeDict],

  // 检查是否是有效的交易所
  isValidExchange: (exchangeName) => exchangeDict.includes(exchangeName) && exchangeName !== '-'
};
