import { DEFAULT_CONFIGS, CURD } from './consts';
import { TiLocalStorage, TiSessionStorage } from './storage';

export const errorResp = (resp, navigate, message) => {
  message && message.error(resp?.errmessage || CURD.ServerError, 10);

  if (resp?.errcode === 300) {
    TiLocalStorage.clear();
    TiSessionStorage.clear();

    DEFAULT_CONFIGS.USER = null;

    navigate('/auth/login', { replace: true });
  }

  if (resp?.errcode === 403) {
    navigate('/exceptions/403', { replace: true });
  }
}

export const histErrorResp = (resp, navigate, location, message, setCheckDate) => {
  message.error(resp?.data || CURD.ServerError, 10);

  if (resp?.code === -400301) {
    TiLocalStorage.clear();
    TiSessionStorage.clear();

    DEFAULT_CONFIGS.USER = null;

    navigate('/auth/login', {
      replace: true,
      state: {
        from: location
      }
    });
  }

  if (resp?.code === -400211) {
    setCheckDate && setCheckDate('');

    navigate('/hist/checkdate', {
      replace: true,
      state: {
        from: location
      }
    });
  }
}
