// select 选项
export const selectOptions = dict => {
  return dict?.map((item, idx) => {
    if (item === '' || item === '-') {
      return null;
    }

    return {
      label: item,
      value: idx,
    };
  }).filter(i => i);
}

export const selectObjOptions = (dict, {isCode, isNum} = {}) => {
  return dict ? Object.keys(dict).map(key => {
    if (key === '0') {
      return null;
    }

    let value = key;
    if (isCode) { value = key.charCodeAt() }
    if (isNum) { value = Number(key) }

    return {
      label: dict[key],
      value: value,
    };
  }).filter(i => i) : [];
}

export const selectMapOptions = (dict, exclude) => {
  if (!dict) {
    return [];
  }

  const options = [];
  dict.forEach((value, key) => {
    if (exclude && exclude.includes(key)) return;

    options.push({
      label: value,
      value: key,
    });
  });

  return options;
}

export const selectExchange = (dict, ids) => {
  return dict?.map((item, idx) => {
    if (item === '' || item === '-') {
      return null;
    }

    if (ids && ids.indexOf(String(idx)) === -1) {
      return null;
    }

    return {
      label: item,
      value: idx,
    };
  }).filter(i => i);
}

export const selectOptionsStr = dict => {
  return dict?.map(item => {
    if (item === '' || item === '-') {
      return null;
    }

    return {
      label: item,
      value: item,
    };
  }).filter(i => i);
}

// checkbox 选项
export const checkOptions = dict => {
  return dict?.map((item, idx) => {
    if (item === '' || item === '-') {
      return null;
    }

    return {
      text: item,
      value: idx,
    };
  }).filter(i => i);
}

export const checkOptionsMap = (dict, {isCode, isNum} = {}) => {
  return dict ? Object.keys(dict).map(key => {
    if (key === '0') {
      return null;
    }

    let value = key;
    if (isCode) { value = key.charCodeAt() }
    if (isNum) { value = Number(key) }

    return {
      text: dict[key],
      value: value,
    };
  }).filter(i => i) : [];
}
