import { FormattedMessage, useIntl } from 'react-intl';

function useFormattedMessage() {
  const intl = useIntl();

  const $t = (id, values) => {
    // First try direct access
    if (intl.messages[id]) {
      return intl.formatMessage({ id }, values);
    }

    // If direct access fails, try to find a matching key that starts with the id
    const matchingKeys = Object.keys(intl.messages).filter(key =>
      key.startsWith(id + '.') || key === id
    );

    if (matchingKeys.length > 0) {
      // Use the first matching key
      return intl.formatMessage({ id: matchingKeys[0] }, values);
    }

    // If no match is found, return the original id
    if (values?.defaultMsg) {
      return values.defaultMsg;
    }
    return id;
  };

  const FormattedMsg = (id, defaultMsg) => (<FormattedMessage id={id} defaultMessage={defaultMsg} />);

  return {
    $t,
    FormattedMsg,
  };
}

export { useFormattedMessage };
