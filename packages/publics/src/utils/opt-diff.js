export const optDiff = (arr, name) => {
  if (arr?.length > 0) {
    const arrSet = new Set(arr.map(item => item[name]).filter(i => i !== ''));
    const arrName = [...arrSet].map(item => ({
      label: item,
      value: item
    }));
    return arrName;
  }
  return [];
}

export const optDiffTwo = (arr, id = 'userid', name = 'username') => {
  if (arr?.length > 0) {
    const tempArr = [];
    arr.forEach(item => {
      const idx = tempArr.findIndex(i => i.value === item[id]);
      if (idx === -1) {
        tempArr.push({
          label: item[name],
          value: item[id],
        });
      }
    });
    return tempArr;
  }
  return [];
}
