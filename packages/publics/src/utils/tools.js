import { TinyColor } from '@ctrl/tinycolor';

// 判断当前是否需要开启哈希（Hash）模式
export const isNeedOpenHash = () => {
  if (
    typeof import.meta.env !== 'undefined' &&
    (import.meta.env.NODE_ENV?.toUpperCase() === 'TEST' || import.meta.env.NODE_ENV?.toUpperCase() === 'DEV')
  ) {
    return false;
  }
  return true;
};

// 设置颜色透明度
export const setAlpha = (baseColor, alpha) => new TinyColor(baseColor).setAlpha(alpha).toRgbString();

// 设置颜色明度
export const lighten = (baseColor, brightness) => new TinyColor(baseColor).lighten(brightness).toHexString();

// 判断是否是图片链接
export const isImg = path => /\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(path);

// 判断是不是一个 url
export const isUrl = path => {
  if (!path) return false;
  if (!path.startsWith('http')) {
    return false;
  }
  try {
    const url = new URL(path);
    return !!url;
  } catch (error) {
    return false;
  }
}

// export const sortByName = (id) => {
//   return (a, b) => a[id].slice(0, 1).charCodeAt(0) - b[id].slice(0, 1).charCodeAt(0);
// }

export const sortByName = id => {
  return (a, b) => a[id].localeCompare(b[id]);
}

export const sortByNumber = id => {
  return (a, b) => a[id] - b[id];
}

export const sortByString = id => {
  return (a, b) => {
    for (let i = 0; i < a[id].length; i++) {
      if (b[id][i] !== undefined) {
        if (a[id].charCodeAt(i) > b[id].charCodeAt(i)) {
          return 1;
        } else {
          return -1;
        }
      } else {
        return -1;
      }
    }
  }
}

// 数组去除空值："", " ", 0, null, NaN, undefined 和 false
export const arrFilter = arr => [arr].filter(i => i && i.trim());

// 对象去除空值："", {}, null
export const objFilter = obj => {
  for(let key in obj) {
    if (obj[key] === null || obj[key] === '' || JSON.stringify(obj[key]) === '{}'){
      delete obj[key]
    }
  }

  return obj;
}

// 树拉平
export const flattenTree = tree => {
  return tree.reduce((arr, {label, value, children = []}) =>
    arr.concat([{label, value}], flattenTree(children)), []);
}

// 数组转树
export const treeGroup = (arr, hasOther) => {
  let tree = [];
  arr.forEach(item => {
    const idx = tree.findIndex(i => i.value === item.producttype);
    const node = {
      label: `${item.productname} [${item.productid}]`,
      value: item.productid,
    };

    if (idx === -1) {
      tree.push({
        label: item.producttypename,
        value: item.producttype,
        children: [node]
      });
    } else {
      tree[idx].children.push(node);
    }
  });

  if (hasOther) {
    tree.push({
      label: '其他',
      value: 'OTHER',
    });
  }

  return tree;
}

// 深拷贝
export const deepCopy = obj => {
  const _obj = JSON.stringify(obj);
  const newObj = JSON.parse(_obj);
  return newObj;
}

// AutoComplet 组件根据 lable 过滤
export const autoFilterOption = (inputValue, option) => {
  // 确保 option.label 存在且是字符串，然后转换为小写进行不区分大小写的比较
  return option.label && typeof option.label === 'string'
    ? option.label.toLowerCase().includes(inputValue.toLowerCase())
    : false;
};

// 过滤表格列：隐藏的列不显示
export const hideColumns = columns => columns.map(i => i.hide ? null : i).filter(i => i);
