import { SYSTEM_NAME } from './consts';

export const defaultSettings = {
  antdCls: 'antd',
  // 全局命名前缀
  prefixCls: 'titd',
  // 全局主题: 'auto' | 'light' | 'dark'
  theme: 'auto',
  // nav主题: 'auto' | 'realDark' | 'light' | undefined
  navTheme: 'auto',
  // 布局: 'side' | 'top' | 'mix'
  layout: 'mix',
  // 'Fluid' | 'Fixed', layout为'top'生效
  contentWidth: 'Fluid',
  // Sticky header
  fixedHeader: false,
  // Sticky siderbar
  fixSiderbar: true,
  /**
   * @name menu 相关的一些配置，可以配置菜单的行为
   *
   * @example 关闭菜单国际化  menu={{ locale: false }}
   * @example 默认打开所有的菜单 menu={{ defaultOpenAll:true }}
   * @example 让菜单处于loading 状态 menu={{ loading: true }}
   * @example 异步加载菜单数据 menu={{params:{ pathname } request: async (params) => { return [{name:"主页",path=params.pathname}]} }}
   * @example 使用 MenuGroup 来聚合菜单 menu={{ mode: 'group' }}
   * @example 取消自动关闭菜单 menu={{ autoClose: false }}
   * @example 忽略收起时自动关闭菜单 menu={{ ignoreFlatMenu: true }}
   */
  menu: {
    // 菜单国际化
    locale: false,
    // 收起时也展示标题
    collapsedShowTitle: false,
    // 收起时也展示 分组菜单的标题
    collapsedShowGroupTitle: false,
    // 默认打开所有的菜单
    defaultOpenAll: false,
    //  是否忽略用户手动折叠过的菜单状态，如选择忽略，折叠按钮切换之后也可实现展开所有菜单
    ignoreFlatMenu: false,
    // 菜单的 loading 配置
    loading: false,
    // 菜单的 loading 发生改变
    // onLoadingChange: loading => void,
    // 菜单聚合的模式: 'sub' | 'group'
    type: 'sub',
    // 取消自动关闭菜单
    autoClose: false,
  },
  // Layout 的 title，也会显示在浏览器标签上，设置为 false，在 layout 中只展示 pageName，而不是 pageName - title
  title: `TITD柜台(${SYSTEM_NAME[1]}) - 速子信息`,
  // 主色
  colorPrimary: '#0081cc',
  // 全局增加滤镜
  // colorWeak: false,
  // 切割菜单, 只在 mix 模式下生效
  splitMenus: true,
  // 菜单为空时隐藏 Sider
  suppressSiderWhenMenuEmpty: false,
  // 侧边菜单模式: 'sub' | 'group'
  siderMenuType: 'sub',
  // 是否pwa缓存
  pwa: true,
}
