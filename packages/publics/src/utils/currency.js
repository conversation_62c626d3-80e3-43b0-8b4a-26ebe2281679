// 取n位小数
export const getFloat = (num, n) => {
  // 非数字
  if (isNaN(num)) { return num; }

  n = n ? parseInt(n) : 0;

  if (n <= 0) {
    return Math.round(num);
  }

  num = Math.round(num * Math.pow(10, n)) / Math.pow(10, n);
  // num = Number(num).toFixed(n);

  return num;
}

// 生成指定位数小数
const createDecimalWithZeros = (num, zeros) => num * Math.pow(10, -(zeros + 1));

// 货币格式化：三位,分割
// export const formatDot = text => {
//   return String(text)?.replace(/\B(?=(\d{3})+(?!\d))/g, ',') || text;
// }
export const formatDot = (text, max = 4, min = 0) => {
  if (isNaN(text)) { return text; }
  if (text === 0 || Math.abs(text) < createDecimalWithZeros(1, max)) { return '0'; }

  // js 自带方法
  let result = text.toLocaleString('zh-CN', {
    // signDisplay: 'negative',
    minimumFractionDigits: min,
    maximumFractionDigits: max,
  });

  // 小数点后4位是0
  if (result === '-0') {
    result = '0';
  }
  return result;
}

// 货币格式化：万、亿、兆
export const formatName = text => {
  const k = 10000;
  const sizes = ['', '万', '亿', '兆'];
  let unit = '';

  if (text > k) {
    const i = Math.floor(Math.log(text) / Math.log(k));
    text = getFloat(text / Math.pow(k, i), 4);
    unit = sizes[i];
  } else {
    text = getFloat(text, 4);
  }

  return unit !== '' ? text + ' ' + unit : text;
}

// 向右移位
function shiftRight(number, digit) {
  digit = parseInt(digit, 10);
  const value = number.toString().split('e');
  return +(value[0] + 'e' + (value[1] ? +value[1] + digit : digit));
}
// 向左移位
function shiftLeft(number, digit) {
  digit = parseInt(digit, 10);
  const value = number.toString().split('e');
  return +(value[0] + 'e' + (value[1] ? +value[1] - digit : -digit));
}

// 金额转大写
export const digitUppercase = function (n) {
  const fraction = ['角', '分'];
  const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  const unit = [
    ['元', '万', '亿', '兆'],
    ['', '拾', '佰', '仟'],
  ];
  const head = n < 0 ? '欠' : '';
  n = Math.abs(n);

  let s = '';
  for (let i = 0; i < fraction.length; i++) {
    s += (digit[Math.floor(shiftRight(n, 1 + i)) % 10] + fraction[i]).replace(
      /零./,
      ''
    );
  }
  s = s || '整';
  n = Math.floor(n);
  for (let i = 0; i < unit[0].length && n > 0; i++) {
    let p = '';
    for (let j = 0; j < unit[1].length && n > 0; j++) {
      p = digit[n % 10] + unit[1][j] + p;
      n = Math.floor(shiftLeft(n, 1));
    }
    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
  }
  return (
    head + s
      .replace(/(零.)*零元/, '元')
      .replace(/(零.)+/g, '零')
      .replace(/^整$/, '零元整')
  );
};

// 保留n位小数
export const toDecimal = (value, n) => {
  return Math.round(value * Math.pow(10, n)) / Math.pow(10, n);
}
