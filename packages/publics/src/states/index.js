import { atom } from 'recoil';

import { HOME_MENU } from '../utils/consts';

export const menuListState = atom({
  key: 'menuListState',
  default: []
});

export const sideMenuState = atom({
  key: 'sideMenuState',
  default: []
});

export const tabMenuState = atom({
  key: 'tabMenuState',
  default: {
    menuList: [HOME_MENU],
    activeKey: HOME_MENU.code,
  }
});

export const exchangeState = atom({
  key: 'exchangeState',
  default: {}
});

export const crumbMenuState = atom({
  key: 'crumbMenuState',
  default: [],
});

export const versionState = atom({
  key: 'versionState',
  default: null,
});

export const checkDateState = atom({
  key: 'checkDateState',
  default: ''
});
