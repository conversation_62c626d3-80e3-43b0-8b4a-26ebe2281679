/**
 * 全局 Context
 * breadcrumb
 * menuData
 * isMobile
 * collapsed
 * hasSiderMenu
 * hasHeader
 * siderWidth
 * isChildrenLayout
 * hasFooterToolbar
 * hasFooter
 * hasPageContainer
 * setHasFooterToolbar
 * setHasPageContainer
 * pageTitleInfo: { title, id, pageName }
 * matchMenus
 * matchMenuKeys
 * currentMenu
 * breadcrumbProps
 * waterMarkProps
 */

import { createContext } from 'react';

export const RouteContext = createContext({});
