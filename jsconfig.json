{
  "compilerOptions": {
    "target": "ESNext", // 使用的 ECMAScript 版本
    "module": "ESNext", // 使用的模块系统
    "allowJs": true,    // 允许处理 JavaScript 文件
    "checkJs": false,   // 关闭 JavaScript 文件的类型检查
    "noEmit": true,     // 不生成任何输出文件 (因为你不是在编译 TypeScript)
    "moduleResolution": "Node",
    "esModuleInterop": true, // 允许导入 CommonJS 模块
    "baseUrl": ".",
    "paths": {
      "@titd/publics/*": ["packages/publics/src/*"]
    },
    "jsx": "react-jsx",
  },
  "include": ["src", "packages", "apps"],
  "exclude": ["node_modules"]
}
