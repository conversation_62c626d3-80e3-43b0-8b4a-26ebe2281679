{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [{
    "label": "OPT:Dev",
    "type": "shell",
    "command": "./apps/titd-manage-options/run.sh",
    "args": ["dev"],
    "problemMatcher": []
  },{
    "label": "OPT:Build",
    "type": "shell",
    "command": "./apps/titd-manage-options/run.sh",
    "args": ["build"],
    "problemMatcher": []
  }, {
    "label": "STK:Dev",
    "type": "shell",
    "command": "./apps/titd-manage-stock/run.sh",
    "args": ["dev"],
    "problemMatcher": []
  }, {
    "label": "STK:Build",
    "type": "shell",
    "command": "./apps/titd-manage-stock/run.sh",
    "args": ["build"],
    "problemMatcher": []
  }]
}
