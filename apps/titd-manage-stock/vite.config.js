import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';

import path from 'path';
// eslint-disable-next-line no-undef
const pathResolve = pathUrl => path.join(__dirname, pathUrl);

// https://vite.dev/config/
export default defineConfig({
  base: './',
  plugins: [react()],
  resolve: {
    alias: {
      '@@': pathResolve('.'),
      '@': pathResolve('src')
    }
  },
  server: {
    port: 3000,
    open: false,
    fs: {
      strict: false,
      allow: []
    },
    proxy: {
      '/titdstk': {
        // target: 'http://172.18.18.113:8060',
        target: 'http://172.18.18.213',
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, '') // 不可以省略rewrite
      },
      '/titddata': {
        target: 'http://172.18.18.213',
        changeOrigin: true
      },
      '/download': {
        // target: 'http://172.18.18.111:80',
        target: 'http://172.18.18.213',
        changeOrigin: true
      },
      '/history': {
        // target: 'http://172.18.18.111:80',
        target: 'http://172.18.18.213',
        changeOrigin: true
      },
      '/lastimport.log': {
        target: 'http://172.18.18.213',
        changeOrigin: true
      }
    }
  }
})
