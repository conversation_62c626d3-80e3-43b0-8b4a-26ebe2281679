import { consts, dicts, formOptions, iconDom } from '@titd/publics/utils';

const { DEFAULT_CONFIGS } = consts;
const { userStatusDict, yesNoDict } = dicts;
const { selectOptions } = formOptions;
const { keyIcon, copyIcon, hideIcon, showIcon } = iconDom;

export const searchForm = ($t, userOptions) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.db.userrisk.userid'),
  fieldProps: {
    options: userOptions
  }
}, {
  valueType: 'input',
  name: 'name',
  label: $t('app.options.db.userinfo.name'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.options.db.userinfo.status'),
  fieldProps: {
    options: selectOptions(userStatusDict),
  }
}, {
  valueType: 'select',
  name: 'isglobal',
  label: $t('app.options.db.userrisk.isglobal'),
  fieldProps: {
    options: selectOptions(yesNoDict),
  }
}];

export const addForm = ($t, isModify, userChange) => [{
  valueType: 'hidden',
  name: 'usertype',
  label: $t('app.options.db.userinfo.usertype'),
  noStyle: true
}, {
  valueType: 'hidden',
  name: 'tradeflow',
  label: $t('app.options.db.userinfo.tradeflow'),
  noStyle: true
}, {
  valueType: 'hidden',
  name: 'logincount',
  label: $t('app.options.db.userinfo.logincount'),
  noStyle: true
}, {
  valueType: 'hidden',
  name: 'loginsuccess',
  label: $t('app.options.db.userinfo.loginsuccess'),
  noStyle: true
}, {
  valueType: 'hidden',
  name: 'loginfailed',
  label: $t('app.options.db.userinfo.loginfailed'),
  noStyle: true
}, {
  valueType: 'input',
  name: 'userid',
  label: $t('app.options.db.userrisk.userid'),
  rules: !isModify ? [
    { required: true },
    { min: 3 },
    { max: 15 },
    { pattern: new RegExp(/^[a-z0-9_]*$/, 'g'), message: $t('app.options.userid.msg') },
    { validator: userChange }
  ] : null,
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'password',
  name: 'password',
  label: $t('app.auth.password'),
  rules: [{ required: !isModify }],
  fieldProps: {
    disabled: isModify,
  }
}, {
  valueType: 'input',
  name: 'name',
  label: $t('app.options.db.userinfo.name'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.options.db.userinfo.status'),
  fieldProps: {
    allowClear: false,
    options: selectOptions(userStatusDict),
  }
}, {
  valueType: 'radio',
  name: 'isglobal',
  label: $t('app.options.db.userrisk.isglobal'),
  fieldProps: {
    disabled: isModify,
    optionType: 'button',
    buttonStyle: 'solid',
    options: selectOptions(yesNoDict),
  }
}, DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? {
  valueType: 'radio',
  name: 'traderight',
  label: $t('app.options.db.userrisk.traderight'),
  fieldProps: {
    optionType: 'button',
    buttonStyle: 'solid',
    options: selectOptions(yesNoDict),
  }
} : null, {
  valueType: 'textarea',
  name: 'ipmac',
  label: $t('app.options.db.userrisk.ipmac'),
  rules: [
    { pattern: new RegExp(/^(\|?((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}(_([0-9a-fA-F]{2})(-[0-9a-fA-F]{2}){5}))+$/, 'g'), message: $t('app.options.db.userrisk.ipmac.msg') }
  ],
  extra: $t('app.options.db.userrisk.ipmac.extra'),
  span: 1,
}];

export const resetPwdForm = $t => [{
  valueType: 'text',
  name: 'userid',
  label: $t('app.options.db.userrisk.userid'),
}, {
  valueType: 'password',
  name: 'newpassword',
  label: $t('app.options.newpassword'),
  rules: [{ required: true }],
  fieldProps: {
    prefix: keyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}, {
  valueType: 'password',
  name: 'repassword',
  label: $t('app.options.repassword'),
  rules: [
    { required: true },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newpassword') === value) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error(
            $t('app.options.repassword.error')
          ));
        }
      }
    }),
  ],
  fieldProps: {
    prefix: copyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}];
