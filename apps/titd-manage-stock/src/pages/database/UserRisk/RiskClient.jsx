import { useState, useImperativeHandle, forwardRef } from 'react';
import { useNavigate } from 'react-router';
import { Transfer, Table, Typography, Divider } from 'antd';
import { useMount } from 'ahooks';
import { diff } from 'radash';

import {
  TableFields,
} from '@titd/publics/components';
import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { TableBadgeDot } = TableFields;
const { exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;

const TableTransfer = ({ leftColumns, rightColumns, ...restProps }) => (
  <Transfer {...restProps}>
    {({
      direction,
      filteredItems,
      onItemSelectAll,
      onItemSelect,
      selectedKeys,
    }) => {
      const columns = direction === 'left' ? leftColumns : rightColumns;
      const rowSelection = {
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows
            .map(({ key }) => key);
          const diffKeys = selected
            ? diff(treeSelectedKeys, selectedKeys)
            : diff(selectedKeys, treeSelectedKeys);
          onItemSelectAll(diffKeys, selected);
        },
        onSelect({ key }, selected) {
          onItemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys,
      };

      return (
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={filteredItems}
          size="small"
          scroll={{ x: 'max-content' }}
          onRow={({ key }) => ({
            onClick: () => {
              onItemSelect(key, !selectedKeys.includes(key));
            },
          })}
        />
      );
    }}
  </Transfer>
);

let oldChecked = [];

const RiskClient = (props, ref) => {
  const {
    user,
    defaultParams,
  } = props;

  const navigate = useNavigate();
  const { $t } = useFormattedMessage();

  const [clientData, setClientData] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);

  const titles = [
    <Text type="secondary" key="0">{$t('app.options.db.userrisk.none')}</Text>,
    <Text type="secondary" key="1">{$t('app.options.db.userrisk.done')}</Text>,
  ];

  const fetchFunc = async (type, params, func, msg = true) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else if (msg) {
      errorResp(resp, navigate);
    }
  }

  useImperativeHandle(ref, () => ({
    submit: () => {
      // console.log(clientData, oldChecked, checkedKeys);

      const delKeys = diff(oldChecked, checkedKeys);
      const addKeys = diff(checkedKeys, oldChecked);

      // console.log(delKeys, addKeys);
      if (delKeys.length > 0) {
        clientAction(delKeys, 'delete');
      }

      if (addKeys.length > 0) {
        clientAction(addKeys, 'insert');
      }
    },
  }));

  const clientAction = (keys, type) => {
    keys.forEach(item => {
      const record = clientData.find(({ key }) => key === item);

      if (type === 'insert') {
        clientInsertFunc(record);
      } else if (type === 'delete') {
        clientDeleteFunc(record);
      }
    });
  }

  const clientInsertFunc = (record) => {
    const postData = {
      ...defaultParams,
      userid: user,
      clientid: record.clientid,
      // segmentid: record.segmentid,
      // defaultsvrid: record.defaultsvrid,
      // clienttype: record.clienttype,
      // exchangeid: record.exchangeid,
    }

    // console.log('交易编码 Insert', postData);

    fetchFunc(MESSAGE_TYPE.InsUserClientRisk, postData);
  }
  const clientDeleteFunc = (record) => {
    const deleteData = {
      ...defaultParams,
      userid: user,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
    }

    // console.log('交易编码 Delete', deleteData);

    fetchFunc(MESSAGE_TYPE.DelUserClient, deleteData);
  }

  const tableColumns = [{
  //   title: $t('app.options.segmentid'),
  //   dataIndex: 'segmentid',
  // }, {
  //   title: $t('app.options.defaultsvrid'),
  //   dataIndex: 'defaultsvrid',
  // }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }];

  useMount(() => {
    getClient();
  });

  const getClient = () => {
    fetchFunc(MESSAGE_TYPE.QryUserClient, {
      ...defaultParams,
      usertype: -1, // 0:交易用户,1:风控用户,-1:所有
    }, (resp) => {
      if (resp?.length > 0) {
        const clientList = [];
        const checkKeys = [];

        resp.forEach(item => {
          const itemKey = `${item.clientid}-${item.clienttype}-${item.exchangeid}`;
          const existNo = clientList.findIndex(({ key }) => key === itemKey);

          // 去重
          if (existNo === -1) {
            clientList.push({
              ...item,
              key: itemKey,
              // defaultsvrid: item.defaultsvr ? item.enablesvrid : '',
            });
          }

          // 选定
          if (item.userid === user) {
            checkKeys.push(itemKey);
          }
        });

        setClientData(clientList);

        setCheckedKeys(checkKeys);
        oldChecked = checkKeys;
      } else {
        setClientData([]);

        setCheckedKeys([]);
        oldChecked = [];
      }
    });
  }

  return (<>
    <Divider orientation="left" plain>{$t('app.options.db.userrisk.clientid')}</Divider>

    <div style={{ overflowX: 'auto' }}>
      <TableTransfer
        titles={titles}
        dataSource={clientData}
        targetKeys={checkedKeys}
        onChange={(nextTargetKeys) => {
          setCheckedKeys(nextTargetKeys);
        }}
        leftColumns={tableColumns}
        rightColumns={tableColumns}
      />
    </div>
  </>);
}

export default forwardRef(RiskClient);
