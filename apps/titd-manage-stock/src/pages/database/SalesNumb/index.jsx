import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn, TableBadgeDot } = TableFields;
const { ModalForm } = forms;
const { updateLink } = links;
const { insertBtn, updateBtn } = btns;
const { exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getClientList } = getList;

const SalesNumbBase = () => {
  const queryForm = useRef();
  const modalForm = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }
  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.salesnumb'),
    dataIndex: 'str1',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'intvolume1',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.exchangeid') + $t('app.options.salesnumb'),
    dataIndex: 'str2',
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 90,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
      ]} />
    )
  }];

  const [clientOptions, setClientOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const createSearchForm = useMemo(() => searchForm($t, clientOptions), [$t, clientOptions]);
  const createAddForm = useMemo(() => addForm($t, isModify, clientOptions), [$t, isModify, clientOptions]);

  useMount(() => {
    getClientList(Request.post, defaultParams, setClientOptions);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QrySalesNumb), params);

    const { data: respData } = resp;

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);

    modalForm.current.show();
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    setIsModify(true);

    modalForm.current.show(1, record);
  }

  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdSalesNumb
      : MESSAGE_TYPE.InsSalesNumb, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.salesnumb', {
        defaultMsg: '营业部代码',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />
  </>);
}

const SalesNumb = props => (
  // <KeepAlive name={keepName}>
  <SalesNumbBase {...props} />
  // </KeepAlive>
);

export { SalesNumb };
