import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = $t => [{
  valueType: 'select',
  name: 'intvolume1',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}];

export const addForm = ($t, isModify) => [{
  valueType: 'input',
  name: 'str1',
  label: $t('app.options.salesnumb'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
  }
}, {
  valueType: 'select',
  name: 'intvolume1',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'input',
  name: 'str2',
  label: $t('app.options.exchangeid') + $t('app.options.salesnumb')
}];
