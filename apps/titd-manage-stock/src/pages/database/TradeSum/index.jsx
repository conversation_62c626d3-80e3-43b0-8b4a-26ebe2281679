import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Card, Checkbox, Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageWithOther,
  charts,
  TableFields,
  btns,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  errorResp,
  dateFormat,
  formOptions,
  currency,
  expandAllKeys,
} from '@titd/publics/utils';

const { Text } = Typography;
const { TableBadgeDot, NegaNumber } = TableFields;
const { ChartColumn } = charts;
const { expandBtn } = btns;
const { exchangeDict, exchangeAllDict, tagNormalColors } = dicts;
const { MESSAGE_TYPE, SITE_URL } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { selectOptions } = formOptions;
const { formatDate, formatDateNoTime } = dateFormat;
const { getFloat } = currency;

const TradeSumBase = () => {
  const queryForm = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [expandedKeys, setExpandedKeys] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const columns = [{
  //   dataIndex: 'id',
  //   width: 70,
  //   align: 'center',
  //   render: text => <IndexColumn border>{text}</IndexColumn>,
  // }, {
    title: $t('app.options.tradingday'),
    dataIndex: 'tradeday',
    render: (text, render) => render.level === 1 ? <Text strong>{formatDate(text)}</Text> : formatDate(text),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => text ? TableBadgeDot(text, exchangeAllDict, tagNormalColors, true) : '-',
  }, {
    title: $t('app.options.db.exsubmit.serverid'),
    dataIndex: 'serverid',
    render: text => text || '-',
  }, {
    title: $t('app.stock.tradevolume'),
    dataIndex: 'tradevolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.trademoney'),
    dataIndex: 'trademoney',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.tradecommi'),
    dataIndex: 'tradecommi',
    align: 'right',
    render: text => NegaNumber(text),
  }];

  useUnmount(() => cancel());

  const getTableData = async params => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeSum), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      // 汇总
      const tradeMap = {};
      respData.forEach(item => {
        if (!tradeMap[item.tradeday]) {
          const newArr = [];
          for (let i = 0; i < exchangeDict.length; i++) {
            newArr[i] = [];
          }
          tradeMap[item.tradeday] = newArr;
        }
        tradeMap[item.tradeday][item.exchangeid].push(item);
      });

      // console.log(tradeMap);
      const allTrade = Object.keys(tradeMap).map(key => {
        const values = tradeMap[key];
        let allVolume = 0, allMoney = 0, allCommi = 0;
        const allChild = [];
        values.forEach((exItem, exIdx) => {
          let exVolume = 0, exMoney = 0, exCommi = 0;
          const exChild = [];

          if (exItem.length > 0) {
            exItem.forEach((item, idx) => {
              exVolume += item.tradevolume;
              exMoney += item.trademoney;
              exCommi += item.tradecommi;

              exChild.push({
                ...item,
                code: key * 1000 + exIdx * 10 + idx,
                level: 2,
              });
            });

            allVolume += exVolume;
            allMoney += exMoney;
            allCommi += exCommi;

            allChild.push({
              code: key * 10 + exIdx,
              tradeday: key,
              exchangeid: exIdx,
              tradevolume: exVolume,
              trademoney: getFloat(exMoney, 2),
              tradecommi: getFloat(exCommi, 4),
              level: 2,
              children: exChild,
            });
          }
        });

        return {
          code: key,
          tradeday: key,
          tradevolume: allVolume,
          trademoney: getFloat(allMoney, 2),
          tradecommi: getFloat(allCommi, 4),
          level: 1,
          children: allChild,
        }
      });

      // 设置默认展开
      const allKeys = expandAllKeys(allTrade, false);
      setExpandedKeys(allKeys);

      return allTrade;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const [exChecked, setExChecked] = useState([1,2]);

  const createSearchForm = useMemo(() => searchForm($t), [$t]);
  const chartData = useMemo(() => {
    // 图表数据
    let chartSource = [];
    dataSource?.forEach(item => {
      if (exChecked.length === exchangeDict.length - 1) {
        // if (item.tradevolume > 0 || item.trademoney > 0 || item.tradecommi > 0) {
        const addSource = [{
          date: formatDate(item.tradeday),
          value: Number(item.tradevolume),
          type: '成交数量',
        }, {
          date: formatDate(item.tradeday),
          value: Number(item.trademoney),
          type: '成交金额',
        }, {
          date: formatDate(item.tradeday),
          value: Number(item.tradecommi),
          type: '成交手续费',
        }];

        chartSource = [
          ...chartSource,
          ...addSource,
        ];
        // }
      } else {
        item.children?.forEach(childItem => {
          if (exChecked.includes(childItem.exchangeid)) {
            const addSource = [{
              date: formatDate(childItem.tradeday),
              value: Number(childItem.tradevolume),
              type: '成交数量',
            }, {
              date: formatDate(childItem.tradeday),
              value: Number(childItem.trademoney),
              type: '成交金额',
            }, {
              date: formatDate(childItem.tradeday),
              value: Number(childItem.tradecommi),
              type: '成交手续费',
            }];

            chartSource = [
              ...chartSource,
              ...addSource,
            ];
          }
        });
      }
    });

    return chartSource;
  }, [dataSource, exChecked]);

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    const dateArr = values?.tradeday?.map(item => formatDateNoTime(item));

    run({
      ...defaultParams,
      ...objFilter(values),
      tradeday: dateArr[0],
      endno: dateArr[1],
    });
  }
  const onReset = () => {
    run(null);
  }

  function onExpand(expanded, record) {
    // console.log('expand:', expanded, record);
    if (expanded) {
      expandedKeys.push(record.code);
    } else {
      const idx = expandedKeys.indexOf(record.code);
      if (idx > -1) {
        expandedKeys.splice(idx, 1);
      }
    }
    setExpandedKeys([...expandedKeys]);
  }
  function toExpand() {
    // console.log('Expand');
    const allKeys = expandedKeys.length > 0 ? [] : expandAllKeys(dataSource);
    setExpandedKeys(allKeys);
  }

  return (
    <PageWithOther
      title={$t('app.menu.db.tradesum', {
        defaultMsg: '成交汇总',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        onSearch: onSearch,
        onReset: onReset,
        isLoading: loading,
      }}
      tableProps={{
        customKey: 'code',
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        checkable: false,
        expandable: {
          expandedRowKeys: expandedKeys,
          onExpand: onExpand
        },
        actionBtns: [
          expandBtn(toExpand, expandedKeys.length > 0),
        ],
        onRefresh: onRefresh,
      }}
    >
      {dataSource.length > 0 && (
        <Card className="ti-table-content">
          <Checkbox.Group
            options={selectOptions(exchangeDict)}
            defaultValue={exChecked}
            onChange={checkedValues => {
              setExChecked(checkedValues);
            }}
            style={{
              marginBottom: '10px',
            }}
          />
          <ChartColumn chartData={chartData} />
        </Card>
      )}
    </PageWithOther>
  );
}

const TradeSum = props => (
  // <KeepAlive name={keepName}>
  <TradeSumBase {...props} />
  // </KeepAlive>
);

export { TradeSum };
