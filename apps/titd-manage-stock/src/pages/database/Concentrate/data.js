import { dicts, formOptions, tools } from '@titd/publics/utils';

const { exchangeDict } = dicts;
const { selectOptions, selectObjOptions } = formOptions;
const { autoFilterOption } = tools;

export const searchForm = ($t, clientFocus, clientGroup, groupOptions) => [{
  valueType: 'autocomplete',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    onFocus: clientFocus,
    options: clientGroup,
    filterOption: autoFilterOption,
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'select',
  name: 'groupid',
  label: $t('app.stock.concentrate.group'),
  fieldProps: {
    options: selectObjOptions(groupOptions),
  }
}];

export const addForm = ($t, isModify, groupOptions, isNodebt, isMax) => [{
  valueType: 'select',
  name: 'groupid',
  label: $t('app.stock.concentrate.group'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectObjOptions(groupOptions, { isNum: true }),
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  rules: [{ min: 5 }],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'switch',
  name: 'nodebt',
  label: $t('app.stock.position.check.nodebt'),
  fieldProps: {
    disabled: isModify,
  },
  otherProps: {
    valuePropName: 'checked',
  }
}, {
  valueType: 'number',
  name: 'limitmin',
  label: $t('app.stock.position.check.limitmin'),
  fieldProps: {
    readOnly: isModify || isNodebt,
    addonAfter: '%',
  }
}, {
  valueType: 'switch',
  name: 'tomax',
  label: $t('app.stock.position.check.tomax'),
  fieldProps: {
    disabled: isModify || isNodebt,
  },
  otherProps: {
    valuePropName: 'checked',
  }
}, {
  valueType: 'number',
  name: 'limitmax',
  label: $t('app.stock.position.check.limitmax'),
  fieldProps: {
    readOnly: isNodebt || isMax,
    addonAfter: '%',
  }
}, {
  valueType: 'number',
  name: 'singleratio',
  label: $t('app.stock.position.checksingle'),
  fieldProps: {
    addonAfter: '%',
  }
}, {
  valueType: 'number',
  name: 'groupratio',
  label: $t('app.stock.position.checkgroup'),
  fieldProps: {
    addonAfter: '%',
  }
}];
