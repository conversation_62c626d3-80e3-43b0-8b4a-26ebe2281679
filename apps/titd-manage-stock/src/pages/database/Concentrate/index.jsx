import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, NegaNumber } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeDict, exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getClientList } = getList;

const ConcentrateBase = () => {
  const modalForm = useRef();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.stock.concentrate.group'),
    dataIndex: 'groupid',
    render: text => groupOptions[text] || text,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => text ? TableBadgeDot(text, exchangeAllDict, tagNormalColors, true) : (<Text type="secondary">
      {$t('app.general.every') + $t('app.options.exchangeid')}
    </Text>),
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => text || (<Text type="secondary">
      {$t('app.general.all') + $t('app.options.clientid')}
    </Text>),
  }, {
    title: $t('app.stock.position.check.limits'),
    dataIndex: 'limitmin',
    align: 'center',
    render: (text, record) => text === 10000 ? $t('app.stock.position.check.nodebt') : formatLimits(text, record.limitmax),
  }, {
    title: $t('app.stock.position.checksingle') + '(%)',
    dataIndex: 'singleratio',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.position.checkgroup') + '(%)',
    dataIndex: 'groupratio',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const formatLimits = (min, max) => {
    if (min === 10000) return $t('app.stock.position.check.nodebt');
    if (min === 0 && max === 0) return '-';
    min = min && `${min}%`;
    if (max === 0 || max === 10000) return `${min} ≤ R`;
    return `${min} ≤ R < ${max}%`;
  }

  const [clientOptions, setClientOptions] = useState([]);
  const [groupOptions, setGroupOptions] = useState({});
  const [isModify, setIsModify] = useState(false);
  const [isNodept, setIsNodept] = useState(false);
  const [isMax, setIsMax] = useState(false);

  const clientFocus = useCallback(() => {
    getClientList(Request.post, defaultParams, setClientOptions);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, clientFocus, clientOptions, groupOptions), [$t, clientFocus, clientOptions, groupOptions]);
  const createAddForm = useMemo(() => addForm($t, isModify, groupOptions, isNodept, isMax), [$t, isModify, groupOptions, isNodept, isMax]);

  useMount(() => {
    getGroupList();
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryConcentrate), params);

    const { data: respData } = resp;

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        limitmin: item.limitmin >= 10000 ? item.limitmin : Math.round(item.limitmin * 100),
        limitmax: item.limitmax >= 10000 ? item.limitmax : Math.round(item.limitmax * 100),
        singleratio: Math.round(item.singleratio * 100),
        groupratio: Math.round(item.groupratio * 100),
      }));
      return tableData;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getGroupList = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryConcentrateGroup), defaultParams);

    const { data: respGroup } = resp;
    if (respGroup?.length > 0) {
      const groupMap = {};
      respGroup.forEach(item => {
        groupMap[item.groupid] = item.groupname;
      });

      setGroupOptions(groupMap);
    }
  }

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setIsNodept(false);
    modalForm.current.show(0);
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    setIsModify(true);

    const newRecord = {
      ...record,
    }

    // 隐藏exchangid为0的显示
    if (!newRecord.exchangeid) {
      newRecord.exchangeid = '';
    }
    if (newRecord.limitmin === 10000) {
      newRecord.nodebt = true;
      newRecord.limitmin = undefined;
      setIsNodept(true);
    } else {
      setIsNodept(false);
    }
    if (newRecord.limitmax === 10000) {
      newRecord.tomax = true;
      setIsMax(true);
    } else {
      setIsMax(false);
    }
    if (newRecord.limitmax >= 10000) {
      newRecord.limitmax = undefined;
    }

    modalForm.current.show(1, newRecord);
  }
  const deleteFunc = async record => {
    const deleteData = {
      ...defaultParams,
      groupid: record.groupid,
      clientid: record.clientid,
      limitmin: record.limitmin === 10000 ? record.limitmin : record.limitmin / 100,
    }

    record.exchangeid && (deleteData.exchangeid = record.exchangeid);

    fetchFunc(MESSAGE_TYPE.DelConcentrate, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const deleteName = record => {
    const min = record.limitmin && `${record.limitmin}%`;
    return `${groupOptions[record.groupid]} - ${exchangeDict[record.exchangeid]} ≥ ${min}`;
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => deleteName(i));
    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach(record => {
          deleteFunc(record);
        });
      }
    });
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: deleteName(record),
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }

  const formValueChange = changedValues => {
    if ('nodebt' in changedValues) {
      if (changedValues.nodebt) {
        setIsNodept(true);
        setIsMax(false);
        modalForm.current.set({
          limitmin: undefined,
          limitmax: undefined,
          tomax: false,
        });
      } else {
        setIsNodept(false);
      }
    }

    if ('tomax' in changedValues) {
      if (changedValues.tomax) {
        setIsMax(true);
        modalForm.current.set({
          limitmax: undefined,
        });
      } else {
        setIsMax(false);
      }
    }
  }

  const addSubmit = async (form, type) => {
    // 恢复exchangid为0
    if (form.exchangeid === '') {
      form.exchangeid = 0;
    }

    let postData = {
      ...defaultParams,
      ...form,
      singleratio: form.singleratio / 100,
      groupratio: form.groupratio / 100,
    }

    if (form.nodebt) {
      postData = {
        ...postData,
        limitmin: 10000,
        limitmax: 10001,
      }
    } else {
      if (form.tomax) {
        postData = {
          ...postData,
          limitmin: form.limitmin / 100,
          limitmax: 10000,
        }
      } else {
        postData = {
          ...postData,
          limitmin: form.limitmin / 100,
          limitmax: form.limitmax / 100,
        }
      }
    }

    delete postData.nodebt;
    delete postData.tomax;

    fetchFunc(type
      ? MESSAGE_TYPE.UpdConcentrate
      : MESSAGE_TYPE.InsConcentrate, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.martrade.concentrate', {
        defaultMsg: '集中度管理',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      onValuesChange={formValueChange}
    />
  </>);
}

const Concentrate = props => (
  // <KeepAlive name={keepName}>
  <ConcentrateBase {...props} />
  // </KeepAlive>
);

export { Concentrate };
