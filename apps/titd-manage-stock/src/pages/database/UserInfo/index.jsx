import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, Space, Tag, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount, useDebounceFn } from 'ahooks';

import {
  PageContent,
  TiTable,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm, resetPwdForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  formOptions,
  dateFormat,
  updateRowData,
  errorResp,
} from '@titd/publics/utils';
// import { USER, TiSessionStorage } from '@utils/storage';

import Client from './Client';

const { Text } = Typography;
const { IndexColumn, TableTag, TableBadgeDot, NegaNumber, TableTitle } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink, resetPwdLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { userStatusDict, exchangeAllDict, tagColors, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;
const { getSegmentSvrList } = getList;
const { checkOptions } = formOptions;
const { formatDateFull } = dateFormat;

const defaultForm = {
  status: 0,
  tradeflow: 20,
  logincount: 50,
  loginsuccess: 50,
  loginfailed: 50,
  // minlocalid: 0,
  // maxlocalid: 0,
}

let userGroup = [];
let riskUserGroup = [];

const UserInfoBase = () => {
  const modalForm = useRef(null);
  const modalClient = useRef(null);
  const resetForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [checkUser, setCheckUser] = useState(null);
  const [clientData, setClientData] = useState({});
  const [expandRows, setExpandRows] = useState([]);

  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();
  const defaultSearch = {
    ...defaultParams,
    status: -1,
  };

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    key: 'userid',
    sorter: sortByName('userid'),
    sortOrder: sortedInfo.columnKey === 'userid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.db.userinfo.name'),
    dataIndex: 'name',
  }, {
    title: $t('app.options.db.userinfo.status'),
    dataIndex: 'status',
    filters: checkOptions(userStatusDict),
    filteredValue: filteredInfo.status || null,
    onFilter: (value, record) => record.status === value,
    render: text => TableTag(text, userStatusDict, tagColors),
  }, {
  //   title: '用户类型',
  //   dataIndex: 'usertype',
  //   render: text => TableTag(text || 0, userTypeDict, tagColors),
  // }, {
    title: $t('app.options.db.userinfo.logincount'),
    dataIndex: 'logincount',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.db.userinfo.tradeflow'),
      $t('app.options.db.userinfo.tradeflow.long')
    ),
    dataIndex: 'tradeflow',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.db.userinfo.loginsuccess'),
      $t('app.options.db.userinfo.loginsuccess.long')
    ),
    dataIndex: 'loginsuccess',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.db.userinfo.loginfailed'),
      $t('app.options.db.userinfo.loginfailed.long')
    ),
    dataIndex: 'loginfailed',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.cancelmax'),
    dataIndex: 'maxerrordernum',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
  //   title: $t('app.options.db.userinfo.minlocalid'),
  //   dataIndex: 'minlocalid',
  // }, {
  //   title: $t('app.options.db.userinfo.maxlocalid'),
  //   dataIndex: 'maxlocalid',
  // }, {
    title: $t('app.options.db.userinfo.lastloginip'),
    dataIndex: 'lastloginip',
  }, {
    title: $t('app.options.db.userinfo.lastlogintime'),
    dataIndex: 'lastlogintime',
    render: text => formatDateFull(text),
  }, {
    title: $t('app.menu.db.appid'),
    dataIndex: 'appid',
    render: text => <Space size={[0, 16]} wrap>
      {text?.map((item, idx) => <Tag key={idx}>{item}</Tag>)}
    </Space>,
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.USER,
    // onCell: () => {
    //   return { onClick: e => e.stopPropagation() }
    // },
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
        resetPwdLink(toResetPwd, record),
      ]} />
    )
  }];

  const [userOptions, setUserOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);
  const [segmentList, setSegmentList] = useState([]);
  const [serverMap, setServerMap] = useState({});
  const [appOptions, setAppOptions] = useState([]);

  const appFocus = useCallback(async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAppId), defaultParams);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const appIds = respData.map(item => ({
        label: item.appid,
        value: item.appid,
      }));
      setAppOptions(appIds);
    }
  }, [defaultParams]);

  const { run: checkUserRun } = useDebounceFn((rules, value, callback) => {
    const allUser = [...userGroup, ...riskUserGroup];
    // console.log(userGroup, riskUserGroup, allUser);
    const isExist = allUser.indexOf(value);
    if (isExist > -1 && !isModify) {
      callback(new Error($t('app.options.userid.error')));
    } else {
      if (value !== '') {
        setCheckUser({
          userid: value,
        });
      }

      callback();
    }
  }, { wait: 500 });

  const createSearchForm = useMemo(() => searchForm($t, userOptions), [$t, userOptions]);
  const createAddForm = useMemo(() => addForm($t, isModify, checkUserRun, appFocus, appOptions), [$t, isModify, checkUserRun, appFocus, appOptions]);
  const createResetPwdForm = useMemo(() => resetPwdForm($t), [$t]);

  useMount(() => {
    getSegmentSvrList(Request.post, defaultParams, setSegmentList, navigate, setServerMap);

    getUserRisk();
  });
  useUnmount(() => cancel());

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserInfo), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        // 搜索遍历用
        const userIds = respData.map(item => ({
          label: item.name ? `${item.userid} (${item.name})` : item.userid,
          value: item.userid,
          status: item.status,
        }));
        setUserOptions(userIds);

        // 新增判断用
        userGroup = respData.map(i => i.userid);
      } else {
        setUserOptions([]);
        userGroup = [];
      }
    }

    // 重新载入，选择项、展开项清空
    setSelectKeys([]);
    setExpandRows([]);

    setFilteredInfo({});
    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    run,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultSearch,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  // const getNewestData = async record => {
  //   const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserInfo), {
  //     ...defaultSearch,
  //     userid: record.userid,
  //   });

  //   if (resp && resp.length > 0) {
  //     return resp[0];
  //   }

  //   return null;
  // }
  const getUserData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserInfoDetail), {
      ...defaultSearch,
      userid: record.userid,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }

    return null;
  }
  const getUserRisk = () => {
    fetchFunc(MESSAGE_TYPE.QryUserRisk, {
      ...defaultParams,
      isglobal: -1,
    }, resp => {
      if (resp?.length > 0) {
        riskUserGroup = resp.map(i => i.userid);
      } else {
        riskUserGroup = [];
      }
    });
  }

  const onRefresh = showAuto => {
    run({
      ...defaultSearch,
      showAuto: showAuto,
    });
    setClientData({});
  }
  const onSearch = values => {
    if (values.status === undefined) {
      delete values.status;
    }

    run({
      ...defaultSearch,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultSearch);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setCheckUser(null);

    modalForm.current.show(0, defaultForm);
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getUserData(record);

    if (newRecord) {
      setIsModify(true);
      setCheckUser({
        userid: newRecord.userid,
        clients: newRecord.clientitem,
      });

      modalForm.current.show(1, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
      });
      mutate(newData);
    } else {
      message.error($t('app.general.deleted'));
      onRefresh();
    }
  }
  const deleteClientFunc = async record => {
    const deleteData = {
      ...defaultParams,
      userid: record.userid,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
    }

    fetchFunc(MESSAGE_TYPE.DelUserClient, deleteData, () => {
      // message.success(CURD.Delete + CURD.StatusOkMsg);
    });
  }
  const deleteClient = async userId => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserClient), {
      ...defaultParams,
      usertype: 0,
      userid: userId,
    });

    const { data: clientData } = resp;
    if (clientData.length > 0) {
      clientData.forEach((record) => {
        deleteClientFunc(record);
      });
    }
  }
  const deleteFunc = async record => {
    // 先删除用户关联的交易编码信息
    deleteClient(record.userid);

    const deleteData = {
      ...defaultParams,
      userid: record.userid,
    }

    fetchFunc(MESSAGE_TYPE.DelUserInfo, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More', records);
    const ids = records.map(i => i.userid);
    Confirm({
      modal,
      content: <>{ids.join(', ')}</>,
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record);
        });
      }
    });
  }
  const toDelete = record => {
    // console.log('Delete', record);
    Confirm({
      modal,
      content: <>{record.userid}</>,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }
  const toResetPwd = record => {
    // console.log('ResetPwd', record);
    resetForm.current.show(2, record);
  }
  // 分开提交
  // const addSubmit = async (form, type) => {
  //   // 先提交修改的权限
  //   modalClient.current.submit();

  //   const postData = {
  //     ...defaultParams,
  //     ...form,
  //   }

  //   fetchFunc(type
  //     ? MESSAGE_TYPE.UpdUserInfo
  //     : MESSAGE_TYPE.InsUserInfo, postData, () => {
  //       message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);

  //       onRefresh(true);
  //       getClient(form.userid);
  //     });
  // }
  // 合并提交
  const addSubmit = (form, type) => {
    // 获取交易编码和资金分配
    const clientValues = modalClient.current?.getValues();

    if (clientValues?.clientData?.length > 0) {
      const postData = {
        ...defaultParams,
        ...form,
        clientitem: clientValues.clientData,
        ...clientValues.frozenData,
      }
      // console.log(postData);

      fetchFunc(MESSAGE_TYPE.UserInfoDetail, postData, () => {
        message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);

        onRefresh(!type);
        getClient(form.userid);
        modalForm.current.close();
      });
    } else {
      message.error('请绑定一个' + $t('app.options.clientid'));
    }
  }
  const formCancel = () => {
    modalClient.current.reset();
  }

  const clientColumns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.segmentid'),
    dataIndex: 'segmentid',
  }, {
    title: $t('app.options.defaultsvrid'),
    dataIndex: 'defaultsvrid',
  }, {
  //   title: TableTitle(
  //     $t('app.options.serverid'),
  //     $t('app.options.serverid.long')
  //   ),
  //   dataIndex: 'serverid',
  //   render: text => serverList.find(item => item.value === text)?.label || text
  // }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => <Text strong>{text}</Text>
  }, {
  //   title: $t('app.options.clienttype'),
  //   dataIndex: 'clienttype',
  //   render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  // }, {
    title: $t('app.options.bizpbu'),
    dataIndex: 'bizpbu',
  }, {
    title: $t('app.options.salesnumb'),
    dataIndex: 'salesnumb',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
  }];

  // 展开表格
  const expandable = {
    expandedRowRender: record => {
      return <TiTable
        columns={clientColumns}
        dataSource={clientData[record.userid] || []}
        hasToolBar={false}
        checkable={false}
        bordered={false}
      />
    },
    onExpand: (expanded, record) => {
      // if (clientData[record.userid]) {
      //   return;
      // }
      if (expanded) {
        getClient(record.userid);
      }
    },
    expandedRowKeys: expandRows,
    onExpandedRowsChange: (expandedRows) => {
      setExpandRows(expandedRows);
    }
  }

  const getClient = async userId => {
    const params = {
      ...defaultParams,
      usertype: 0,
      userid: userId,
    }

    fetchFunc(MESSAGE_TYPE.QryUserClient, params, resp => {
      const clientTableData = resp.map((item, idx) => ({
        ...item,
        id: idx + 1,
        defaultsvrid: item.defaultsvr ? item.enablesvrid : '',
      })) || [];
      setClientData({
        ...clientData,
        [userId]: clientTableData
      });
    });
  }

  const resetPwdSubmit = form => {
    const postData = {
      ...defaultParams,
      userid: form.userid,
      newpassword: form.newpassword,
    }

    fetchFunc(MESSAGE_TYPE.UpdPassword, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    // console.log('Various parameters', pagination, filters, sorter);
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.userinfo', {
        defaultMsg: '用户信息',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        expandable: expandable,
        handleChange: tableChange,
      }}
    />

    {/* 新增修改 - 普通用户 */}
    <ModalForm
      ref={modalForm}
      formData={createAddForm}
      formGroup
      spanDefault={3}
      width={'80%'}
      onOk={addSubmit}
      onCancel={formCancel}
      loading={isLoading}
      lateClose={true}
    >
      <Client
        ref={modalClient}
        currentForm={modalForm}
        user={checkUser}
        segmentList={segmentList}
        serverMap={serverMap}
      />
    </ModalForm>

    {/* 重置密码 */}
    <ModalForm
      ref={resetForm}
      onOk={resetPwdSubmit}
      formData={createResetPwdForm}
    />
  </>);
}

const UserInfo = props => (
  // <KeepAlive name={keepName}>
  <UserInfoBase {...props} />
  // </KeepAlive>
);

export { UserInfo };
