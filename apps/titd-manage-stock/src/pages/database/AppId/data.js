export const searchForm = ($t, autoOptions) => [{
  valueType: 'autocomplete',
  name: 'appid',
  label: $t('app.menu.db.appid'),
  fieldProps: {
    options: autoOptions
  }
}];

// export const addForm = (intl, isModify, idChange, randomBtn) => [{
export const addForm = ($t, isModify, randomBtn) => [{
  valueType: 'input',
  name: 'appid',
  label: $t('app.menu.db.appid'),
  rules: !isModify ? [
    { required: true },
    { max: 30 },
    // { pattern: new RegExp(/^[a-zA-Z0-9]{3,10}_[a-zA-Z0-9]{3,10}_[a-zA-Z0-9.]{2,8}$/, 'g'), message: $t('app.options.db.appid.msg') },
    // { validator: idChange }
  ] : null,
  // extra: $t('app.options.db.appid.extra'),
  fieldProps: {
    // placeholder: $t('app.options.db.appid.placeholder'),
    readOnly: isModify,
  }
}, {
  valueType: 'input',
  name: 'identify',
  label: $t('app.menu.db.appid') + $t('app.options.db.appid.identify'),
  tooltip: $t('app.options.db.appid.identify.msg'),
  extra: $t('app.options.db.appid.identify.extra'),
  rules: [
    { required: true },
    { pattern: new RegExp(/^[a-zA-Z0-9]{10,20}$/, 'g'), message: $t('app.options.db.appid.identify.msg') }
  ],
  fieldProps: {
    addonBefore: randomBtn && randomBtn()
  }
}];
