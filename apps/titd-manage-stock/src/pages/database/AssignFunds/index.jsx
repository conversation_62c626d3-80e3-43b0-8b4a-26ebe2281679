import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TiTable,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  currency,
  getList,
  errorResp,
  optDiff,
  updateRowData,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTitle, TableTag, TableBadgeDot, NegaNumber } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { alloctTypeDict, assignTypeDict, exchangeDict, exchangeAllDict, accountTypeDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { getFloat, formatDot, digitUppercase } = currency;
const { sortByName, objFilter } = tools;
const { getAccountList } = getList;

const defaultMainType = 1;
const defaultExchangeType = 1;
const defaultDisable = new Array(exchangeDict.length).fill(false);

const AssignFundsBase = () => {
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [sortedInfo, setSortedInfo] = useState({});

  const [fundsMap, setFundsMap] = useState();
  const [frozenData, setFrozenData] = useState({});
  const [expandRows, setExpandRows] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    key: 'accountid',
    sorter: sortByName('accountid'),
    sortOrder: sortedInfo.columnKey === 'accountid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.mem.account.prebalance'),
    dataIndex: 'prebalance',
    align: 'right',
    // render: text => NegaNumber(text, 2, 2)
  }, {
    title: $t('app.options.db.funds.allocttype'),
    dataIndex: 'allocttype',
    render: text => TableTag(text, alloctTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.funds.scalemode1'),
    dataIndex: 'scalemode1',
    render: text => TableBadgeDot(text, assignTypeDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.db.funds.scale1'),
      $t('app.options.db.funds.scalemode1.tip')
    ),
    dataIndex: 'scale1',
    align: 'right',
    render: (text, record) => formatFunds(text, record.scalemode1),
  }, {
    title: $t('app.options.db.funds.scalemode2'),
    dataIndex: 'scalemode2',
    render: text => TableBadgeDot(text, assignTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.funds.scale2'),
    dataIndex: 'exchangeScale',
    // render: text => text.join(', ')
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const [accountOptions, setAccountOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);
  const [accountGroup, setAccountGroup] = useState([]);
  const [digitUpper, setDigitUpper] = useState('');
  const [maintype, setMainType] = useState(defaultMainType);
  const [exchangetype, setExchangeType] = useState(defaultExchangeType);
  const [disableMap, setDisableMap] = useState(defaultDisable);

  const createSearchForm = useMemo(() => searchForm($t, accountOptions), [$t, accountOptions]);
  const createAddForm = useMemo(() => addForm(modalForm, $t, isModify, accountGroup, digitUpper, maintype, exchangetype, disableMap), [$t, isModify, accountGroup, digitUpper, maintype, exchangetype, disableMap]);

  useUnmount(() => cancel());

  const formatExgScale = (scale, mode) => {
    if (scale && scale.length > 0) {
      return scale.map((item, idx) => {
        const exValue = item.isRemaining ? (<Text strong>剩余全部</Text>) : formatFunds(item.alloction, mode);

        return (
          <Text key={idx} style={{ display: 'inline-block', minWidth: '150px' }}>
            {`${exchangeDict[item.exchangeid] || item.exchangeid}： `}
            {exValue}
          </Text>
        );
      });
    } else {
      return [];
    }
  };
  const formatFunds = (text, mode) => mode === 1 ? (<>
    <Text strong>{getFloat(text, 2)}</Text>
    <Text type="secondary"> %</Text>
  </>) : (<>
    <Text type="secondary">¥ </Text>
    <Text strong>{NegaNumber(text, 2)}</Text>
  </>);

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAccountAlloct), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        const autoId = optDiff(respData, 'accountid');
        setAccountOptions(autoId);
      } else {
        setAccountOptions([]);
      }
    }

    // 重新载入，选择项、展开项清空
    setSelectKeys([]);
    setExpandRows([]);

    setSortedInfo({});

    if (respData?.length > 0) {
      // 获取账户总资金
      const funds = fundsMap || await getAllFunds();

      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        prebalance: funds[item.accountid] || '-',
        exchangeScale: formatExgScale(item.scale2, item.scalemode2),
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getAllFunds = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAllFunds), defaultParams);

    const { data: respData } = resp;
    const funds = {};
    if (respData?.length > 0) {
      respData.forEach(item => funds[item.accountid] = formatDot(item.prebalance, 2, 2));
    }
    setFundsMap(funds);

    return funds;
  }
  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAccountAlloct), {
      ...defaultParams,
      accountid: record.accountid,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }

    return null;
  }

  const onRefresh = () => {
    refresh();
    setFrozenData({});
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');

    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountGroup);

    setIsModify(false);
    setMainType(defaultMainType);
    setExchangeType(defaultExchangeType);
    setDigitUpper('');

    modalForm.current.show(0);
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      setIsModify(true);
      setMainType(newRecord.scalemode1);
      setExchangeType(newRecord.scalemode2);

      // 显示大写
      if (newRecord.scalemode1 === 2 && newRecord.scale1) {
        setDigitUpper(digitUppercase(newRecord.scale1));
      } else {
        setDigitUpper('');
      }
      // 次席分配
      if (newRecord.scale2?.length > 0) {
        newRecord.scale2.forEach(item => {
          const exIdx = item.exchangeid;
          newRecord['isRemaining' + exIdx] = item.isRemaining;
          !item.isRemaining && (newRecord['alloction' + exIdx] = getFloat(item.alloction, 2));
          disableMap[exIdx] = item.isRemaining;
        });
        setDisableMap([...disableMap]);
      }
      // 总资金
      newRecord.prebalance = fundsMap[newRecord.accountid] || '-';
      modalForm.current.show(1, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
        exchangeScale: formatExgScale(newRecord.scale2, newRecord.scalemode2),
      });
      mutate(newData);
    } else {
      message.error($t('app.general.deleted'));
      onRefresh();
    }
  }
  const deleteFunc = async record => {
    const deleteData = {
      ...defaultParams,
      accountid: record.accountid,
    }

    fetchFunc(MESSAGE_TYPE.DelAccountAlloct, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => i.accountid);
    Confirm({
      modal,
      content: <>{ids.join(', ')}</>,
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record);
        });
      }
    });
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: <>{record.accountid}</>,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }

  const formValueChange = (changedValues, values) => {
    if ('accountid' in changedValues) {
      const value = changedValues.accountid;
      if (value && fundsMap) {
        modalForm.current.set({
          prebalance: fundsMap[value] || '-',
        });
      }
    }
    if ('scalemode1' in changedValues) {
      const value = changedValues.scalemode1;
      setMainType(value);

      modalForm.current.set({
        scale1: value === 1 ? 100 : null
      });
      setDigitUpper('');
    }
    if ('scalemode2' in changedValues) {
      const value = changedValues.scalemode2;
      setExchangeType(value);

      exchangeDict.forEach((item, idx) => {
        if (item !== '' && item !== '-') {
          modalForm.current.set({
            ['alloction' + idx]: value === 1 ? 0 : null,
            ['isRemaining' + idx]: false,
          });
          disableMap[idx] = false;
        }
      });
      setDisableMap([...disableMap]);
    }
    if ('scale1' in changedValues) {
      const value = changedValues.scale1;
      if (value !== '') {
        setDigitUpper(digitUppercase(value));
      } else {
        setDigitUpper('');
      }
    }

    exchangeDict.forEach((item, idx) => {
      if ((item !== '' && item !== '-') && `isRemaining${idx}` in changedValues) {
        const value = changedValues['isRemaining' + idx];
        if (value) {
          const isSwitched = exchangeDict.findIndex((_, i) => values['isRemaining' + i] && idx !== i);
          if (isSwitched > -1) {
            message.error($t('app.options.db.funds.scalemode2.tip'));
            modalForm.current.set({
              ['isRemaining' + idx]: false
            });
            return false;
          }

          modalForm.current.set({
            ['alloction' + idx]: values.scalemode2 === 1 ? 0 : null
          });
        }
        disableMap[idx] = value;
        setDisableMap([...disableMap]);
      }
    });
  }
  const addSubmit = async (form, type) => {
    const exchangeScale = exchangeDict.map((item, idx) => {
      if (item === '' || item === '-') {
        return null;
      }

      const isRemain = form['isRemaining' + idx];

      const scale = {
        exchangeid: idx,
        isRemaining: isRemain ? 1 : 0,
        alloction: isRemain ? 0 : form['alloction' + idx],
      }

      delete form['isRemaining' + idx];
      delete form['alloction' + idx];

      return scale;
    }).filter(i => i);

    delete form.prebalance;

    const postData = {
      ...defaultParams,
      ...form,
      scale2: exchangeScale,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdAccountAlloct
      : MESSAGE_TYPE.AddAccountAlloct, postData, () => {
        message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
        onRefresh(!type);
      });
  }

  const FrozenColumns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accounttype'),
    dataIndex: 'accounttype',
    render: text => TableBadgeDot(text, accountTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.funds.available'),
    dataIndex: 'frozenfunds',
    align: 'right',
    render: text => text ? (<>
      <Text type="secondary">¥ </Text>
      <Text strong>{formatDot(text)}</Text>
    </>) : '-',
  }];

  // 展开表格
  const expandable = {
    expandedRowRender: record => {
      return <TiTable
        columns={FrozenColumns}
        dataSource={frozenData[record.accountid] || []}
        hasToolBar={false}
        checkable={false}
        bordered={false}
      />
    },
    onExpand: (expanded, record) => {
      // if (clientData[record.userid]) {
      //   return;
      // }
      if (expanded) {
        getFrozen(record.accountid);
      }
    },
    expandedRowKeys: expandRows,
    onExpandedRowsChange: (expandedRows) => {
      setExpandRows(expandedRows);
    }
  }

  const getFrozen = async accountId => {
    const params = {
      ...defaultParams,
      accountid: accountId,
    }

    fetchFunc(MESSAGE_TYPE.QryFrozenFunds, params, resp => {
      const frozenTableData = resp.map((item, idx) => ({
        ...item,
        id: idx + 1
      })) || [];

      setFrozenData({
        ...frozenData,
        [accountId]: frozenTableData
      });
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.assignfunds', {
        defaultMsg: '资金分配',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        expandable: expandable,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      initValues={{
        allocttype: 2,
        scalemode1: defaultMainType,
        scale1: 100,
        scalemode2: defaultExchangeType,
      }}
      onValuesChange={formValueChange}
    />
  </>);
}

// const AssignFundsAlive = ({ keepName, ...props }) => (
const AssignFunds = props => (
  // <KeepAlive name={keepName}>
  <AssignFundsBase {...props} />
  // </KeepAlive>
);

export { AssignFunds };
