import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, autoOptions, productFocus, productOptions) => [{
  valueType: 'autocomplete',
  name: 'templateid',
  label: $t('app.options.templateid'),
  fieldProps: {
    options: autoOptions
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'select',
  name: 'productid',
  label: $t('app.options.productid'),
  fieldProps: {
    showSearch: true,
    onFocus: productFocus,
    options: productOptions,
    // notFoundContent: loading ? <Spin size="small" /> : '无数据',
    notFoundContent: $t('app.general.nodata'),
  }
}];

export const addForm = ($t, isModify, tplChange, productFocus, productOptions) => [{
  valueType: 'input',
  name: 'templateid',
  label: $t('app.options.templateid'),
  rules: !isModify ? [
    { required: true },
    { min: 3, max: 26 },
    { validator: tplChange }
  ] : null,
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'treeSelect',
  name: 'productid',
  label: $t('app.options.productid'),
  rules: [{ required: true }],
  extra: $t('app.options.productid.tip'),
  fieldProps: {
    treeData: productOptions,
    treeCheckable: true,
    // treeCheckStrictly: true,
    treeDefaultExpandAll: true,
    onOpenChange: productFocus,
    // notFoundContent: loading ? <Spin size="small" /> : '无数据',
    notFoundContent: $t('app.general.nodata'),
  }
}];
