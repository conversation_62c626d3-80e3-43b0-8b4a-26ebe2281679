import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, Space, App } from 'antd';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  getList,
  optDiff,
  errorResp,
} from '@titd/publics/utils';

const { Text, Link } = Typography;
const { IndexColumn } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;
const { getAccountList } = getList;

const StkCommiTplUserBase = () => {
  const modalForm = useRef();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [selectRecord, setSelectRecord] = useState(null);

  // const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    key: 'accountid',
    sorter: sortByName('accountid'),
    sortOrder: sortedInfo.columnKey === 'accountid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.stock.template.templateid'),
    dataIndex: 'templateid',
    render: text => templateFunc(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const templateFunc = text => {
    return text && (<Space>
      <Link underline onClick={() => navigate('/db/stkcommitpl', { state: { datas: text } })}>{text}</Link>
    </Space>);
  }

  const [accountGroup, setAccountGroup] = useState([]);
  const [tplGroup, setTplGroup] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const createSearchForm = useMemo(() => searchForm($t, accountGroup, tplGroup), [$t, accountGroup, tplGroup]);
  const createAddForm = useMemo(() => addForm($t, isModify, accountGroup, tplGroup), [$t, isModify, accountGroup, tplGroup]);

  useMount(() => {
    getUserAndTpl();
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryCommiTemplateUser), params);

    const { data: respData } = resp;

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    // setFilteredInfo({});
    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });


  const getUserAndTpl = () => {
    // 获取用户列表
    getAccountList(Request.post, defaultParams, setAccountGroup);
    // 获取模板列表
    getTplGroup();
  }
  // 取手续费模版
  const getTplGroup = useCallback(async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryCommiTemplate), defaultParams);

    const { data: tpl } = resp;

    // 取不同项
    if (tpl?.length > 0) {
      const tplIds = optDiff(tpl, 'templateid');
      setTplGroup(tplIds);
    }
  }, [defaultParams]);


  const onRefresh = () => {
    refresh();
  };
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');

    // 重新获取列表
    getUserAndTpl();

    setIsModify(false);
    modalForm.current.show();
  }
  const toUpdate = record => {
    setIsModify(true);
    modalForm.current.show(1, record);

    setSelectRecord(record);
  }
  const deleteFunc = async (record, showMsg) => {
    const deleteData = {
      ...defaultParams,
      accountid: record.accountid,
      templateid: record.templateid,
    }

    fetchFunc(MESSAGE_TYPE.DelCommiTemplateUser, deleteData, () => {
      showMsg && message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => i.accountid);
    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record, true);
        });

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.accountid,
      onOk: () => {
        // console.log('ok', record);
        deleteFunc(record, true);

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }

  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    // 修改操作为先删除再新增
    if (type) {
      // console.log(selectRecord, form);
      deleteFunc(selectRecord);
    }

    fetchFunc(MESSAGE_TYPE.InsCommiTemplateUser, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    // console.log('Various parameters', pagination, filters, sorter);

    // setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.stkcommitpluser', {
        defaultMsg: '手续费分配',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />
  </>);
}

const StkCommiTplUser = props => (
  // <KeepAlive name={keepName}>
  <StkCommiTplUserBase {...props} />
  // </KeepAlive>
);

export { StkCommiTplUser };
