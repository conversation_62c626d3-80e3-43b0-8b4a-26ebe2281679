export const searchForm = ($t, accountOptions, tplOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions
  }
}, {
  valueType: 'autocomplete',
  name: 'templateid',
  label: $t('app.stock.template.templateid'),
  fieldProps: {
    options: tplOptions
  }
}];

export const addForm = ($t, isModify, accountOptions, tplOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: accountOptions,
  }
}, {
  valueType: 'select',
  name: 'templateid',
  label: $t('app.stock.template.templateid'),
  rules: [{ required: true }],
  fieldProps: {
    // mode: 'multiple',
    options: tplOptions
  }
}];
