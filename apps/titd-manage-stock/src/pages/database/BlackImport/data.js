import { dicts, formOptions } from '@titd/publics/utils';

const { clientChgStatusDict, blackRangeDict } = dicts;
const { selectOptions } = formOptions;

const normFile = e => {
  if (Array.isArray(e)) {
    return e;
  }
  return e && e.fileList;
};

export const searchForm = ($t, accountOptions) => [{
  valueType: 'autocomplete',
  name: 'str1',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions,
  }
// }, {
//   valueType: 'select',
//   name: 'svrid',
//   label: intl.formatMessage({ id: 'app.options.exchangeid' }),
//   fieldProps: {
//     options: selectOptions(exchangeDict)
//   }
}, {
  valueType: 'input',
  name: 'str2',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'picker',
  name: 'intvolume1',
  label: $t('app.options.order.begtime'),
}, {
  valueType: 'picker',
  name: 'intvolume2',
  label: $t('app.options.order.endtime'),
}, {
  valueType: 'select',
  name: 'intvolume3',
  label: $t('app.stock.blacklist.range'),
  fieldProps: {
    options: selectOptions(blackRangeDict),
  }
}];

export const importForm = ($t, instrUploadProps, userUploadProps) => [{
//   valueType: 'select',
//   name: 'userlist',
//   label: intl.formatMessage({ id: 'app.options.userid' }),
//   rules: [{ required: true }],
//   fieldProps: {
//     mode: 'tags',
//     tokenSeparators: [',', '|'],
//   }
// }, {
  valueType: 'uploadDragger',
  name: instrUploadProps.name,
  label: $t('app.stock.blacklist.instrument'),
  rules: [{ required: true }],
  otherProps: {
    valuePropName: 'fileList',
    getValueFromEvent: normFile
  },
  fieldProps: instrUploadProps
}, {
  valueType: 'uploadDragger',
  name: userUploadProps.name,
  label: $t('app.stock.blacklist.user'),
  rules: [{ required: true }],
  otherProps: {
    valuePropName: 'fileList',
    getValueFromEvent: normFile
  },
  fieldProps: userUploadProps
}, {
  valueType: 'radio',
  name: 'status',
  label: $t('app.options.mem.userclient.status'),
  rules: [{ required: true }],
  fieldProps: {
    optionType: 'button',
    buttonStyle: 'solid',
    options: selectOptions(clientChgStatusDict)
  }
}];

export const addForm = ($t, isModify, accountOptions) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.accountid'),
  rules: [{ required: true }],
  fieldProps: {
    readOnly: isModify,
    options: accountOptions,
  },
}, {
//   valueType: 'input',
//   name: 'username',
//   label: intl.formatMessage({ id: 'app.options.db.userinfo.name' }),
//   fieldProps: {
//     readOnly: isModify,
//   },
// }, {
//   valueType: 'select',
//   name: 'svrid',
//   label: intl.formatMessage({ id: 'app.options.exchangeid' }),
//   rules: [{ required: true }],
//   fieldProps: {
//     disabled: isModify,
//     options: selectOptions(exchangeDict)
//   }
// }, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
  rules: [{ required: true }],
  fieldProps: {
    readOnly: isModify,
  },
}, {
  valueType: 'input',
  name: 'instrumentname',
  label: $t('app.options.instrument.instrumentname'),
  fieldProps: {
    readOnly: isModify,
  },
}, {
  valueType: 'picker',
  name: 'beginday',
  label: $t('app.options.order.begtime'),
  rules: [{ required: true }],
}, {
  valueType: 'picker',
  name: 'endday',
  label: $t('app.options.order.endtime'),
  rules: [{ required: true }],
}, {
  valueType: 'radio',
  name: 'status',
  label: $t('app.options.mem.userclient.status'),
  rules: [{ required: true }],
  fieldProps: {
    optionType: 'button',
    buttonStyle: 'solid',
    // options: isModify ? selectOptions(clientChgStatusDict).slice(1) : selectOptions(clientChgStatusDict)
    options: selectOptions(clientChgStatusDict).slice(1)
  }
}, {
  valueType: 'switch',
  name: 'synctomem',
  label: $t('app.stock.blacklist.syncto'),
}];
