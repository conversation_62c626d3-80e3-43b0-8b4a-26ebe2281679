import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import dayjs from 'dayjs';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, importForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
  uploadConfig,
  dateFormat,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTag, TableBadgeDot, TableTitle } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, importBtn } = btns;
const { exchangeAllDict, clientStatusDict, tagNormalColors, tagColors } = dicts;
const { DEFAULT_CONFIGS, MESSAGE_TYPE, SITE_URL, UPLOAD_URL, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList } = getList;
const { formatDate } = dateFormat;

const defaultBlackRange = 1;

const BlackImportBase = () => {
  const modalForm = useRef();
  const modalImport = useRef();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
  }), [limit]);
  const defaultSearch = {
    ...defaultParams,
    intvolume3: defaultBlackRange,
  }

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'userid',
  }, {
    title: $t('app.options.db.userinfo.name'),
    dataIndex: 'username',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'svrid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.instrument.instrumentname'),
    dataIndex: 'instrumentname',
  }, {
    title: TableTitle(
      $t('app.options.order.begtime'),
      $t('app.options.order.time.long'),
    ),
    dataIndex: 'beginday',
    render: text => formatDate(text),
  }, {
    title: TableTitle(
      $t('app.options.order.endtime'),
      $t('app.options.order.time.long'),
    ),
    dataIndex: 'endday',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.mem.userclient.status'),
    dataIndex: 'tdstatus',
    render: text => TableTag(text, clientStatusDict, tagColors),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const [isModify, setIsModify] = useState(false);
  const [accountOptions, setAccountOptions] = useState([]);

  const instrUploadProps = useMemo(() => ({
    ...uploadConfig($t, message),
    name: 'instrFile',
    action: UPLOAD_URL(MESSAGE_TYPE.UplBlackInstr),
    data: defaultParams,
  }), [$t, message, defaultParams]);
  const userUploadProps = useMemo(() => ({
    ...uploadConfig($t, message),
    name: 'userFile',
    action: UPLOAD_URL(MESSAGE_TYPE.UplBlackUser),
    data: defaultParams,
  }), [$t, message, defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, accountOptions), [$t, accountOptions]);
  const createImportForm = useMemo(() => importForm($t, instrUploadProps, userUploadProps), [$t, instrUploadProps, userUploadProps]);
  const createAddForm = useMemo(() => addForm($t, isModify, accountOptions), [$t, isModify, accountOptions]);

  useMount(() => {
    // 获取资金账户列表
    getAccountList(Request.post, defaultParams, setAccountOptions);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryBlackInstr), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    cancel,
    mutate,
  } = useRequest(getTableData, {
    defaultParams: [defaultSearch],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultSearch,
      ...objFilter(values),
      endno: limit,
      intvolume1: values.intvolume1 ? dayjs(values.intvolume1).format('YYYYMMDD') : 0,
      intvolume2: values.intvolume2 ? dayjs(values.intvolume2).format('YYYYMMDD') : 0,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultSearch);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    modalForm.current.show(0);
  }
  const toUpdate = record => {
    // console.log('Update', record);
    setIsModify(true);
    modalForm.current.show(1, {
      ...record,
      status: record.tdstatus,
      beginday: dayjs(String(record.beginday)),
      endday: dayjs(String(record.endday)),
    });
  }
  const deleteFunc = record => {
    let deleteData = {
      ...defaultParams,
    }

    if (record) {
      deleteData = {
        ...deleteData,
        userid: record.userid,
        instrumentid: record.instrumentid,
      }
    }

    fetchFunc(MESSAGE_TYPE.DelBlackUser, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: `${record.userid} - ${record.instrumentid}`,
      onOk: () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }

  const toImport = () => {
    modalImport.current.show($t('app.general.import.title'));
  }

  const addSubmit = async (form, type) => {
    // 同步到场上
    if (form.synctomem) {
      const syncData = {
        ...defaultParams,
        str1: form.userid,
        str2: form.instrumentid,
        intvolume1: form.status,
      }

      // console.log(syncData);
      fetchFunc(MESSAGE_TYPE.MemInsBlackList, syncData, resp => {
        const showMsg = $t('app.stock.blacklist.syncto') + '：' + resp.errmessage;
        if (resp.errcode === 200) {
          message.success(showMsg);
        } else {
          message.error(showMsg);
        }
      });
    }

    const postData = {
      ...defaultParams,
      ...form,
      beginday: form.beginday ? dayjs(form.beginday).format('YYYYMMDD') : 0,
      endday: form.endday ? dayjs(form.endday).format('YYYYMMDD') : 0,
    }

    delete postData.synctomem;

    // console.log(postData);
    fetchFunc(MESSAGE_TYPE.InsBlackInstr, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const importSubmit = async form => {
    const postData = {
      ...defaultParams,
      intvolume1: form.status,
    }

    // console.log(postData);
    fetchFunc(MESSAGE_TYPE.ImpBlackInstr, postData, resp => {
      if (resp.errcode) {
        message.error(resp.errmessage);
      } else {
        message.success($t('app.stock.blacklist.instrument') + $t('app.general.import.success', {
          num: resp.intvolume1,
        }));
        onRefresh();
      }
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.black.blackimport', {
        defaultMsg: '交易黑名单管理',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
        initValues: {
          intvolume3: defaultBlackRange,
        }
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        actionBtns: [
          insertBtn(toInsert),
          importBtn(toImport),
          // emptyBtn(toEmpty),
        ],
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />

    {/* 导入 */}
    <ModalForm
      ref={modalImport}
      onOk={importSubmit}
      formData={createImportForm}
    />
  </>);
}

const BlackImport = props => (
  // <KeepAlive name={keepName}>
  <BlackImportBase {...props} />
  // </KeepAlive>
);

export { BlackImport };
