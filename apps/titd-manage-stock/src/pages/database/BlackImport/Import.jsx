import { useState, useMemo, useImperativeHandle, forwardRef } from 'react';
import { Modal, Upload, App } from 'antd';
import { InboxOutlined } from '@ant-design/icons';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  uploadConfig,
} from '@titd/publics/utils';

const { Dragger } = Upload;
const { MESSAGE_TYPE, UPLOAD_URL } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;

const pathGroup = [
  MESSAGE_TYPE.UplBlackInstr, // 合约上传
  MESSAGE_TYPE.UplBlackUser,  // 用户上传
];

const ModalImport = (props, ref) => {
  const {
    onOk,
  } = props;

  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [isOpen, setIsOpen] = useState(false);
  const [actionType, setActionType] = useState(0);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const uploadProps = useMemo(() => ({
    ...uploadConfig($t, message),
    action: UPLOAD_URL(pathGroup[actionType]),
    data: defaultParams,
  }), [actionType, $t, message, defaultParams]);

  useImperativeHandle(ref, () => ({
    show: path => {
      setActionType(path || 0);
      setIsOpen(true);
    },
    close: () => setIsOpen(false),
  }));

  return (
    <Modal
      getContainer={false}
      title={$t({ id: actionType ? 'app.stock.blacklist.user' : 'app.stock.blacklist.instrument' }) + $t({ id: 'app.general.import.title' })}
      open={isOpen}
      onOk={() => onOk(actionType)}
      onCancel={() => setIsOpen(false)}
    >
      <div key={Math.random()}>
        <Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">单击或拖动文件到此区域进行上传</p>
          <p className="ant-upload-hint">严禁上传公司数据或其他机密文件</p>
        </Dragger>
      </div>
    </Modal>
  );
}

export default forwardRef(ModalImport);
