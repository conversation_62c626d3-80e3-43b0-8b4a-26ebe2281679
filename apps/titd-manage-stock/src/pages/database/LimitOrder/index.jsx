import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount, useDebounceFn } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm, modifyForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn, TableTag, TableBadgeDot, NegaNumber } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeDict, exchangeAllDict, isLimitDict, tagNormalColors, yesNoColor } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserList, getClientList } = getList;

const LimitOrderBase = () => {
  const queryForm = useRef();
  const aModalForm = useRef();
  const mModalForm = useRef();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
  }, {
    title: $t('app.stock.limit.order.volume'),
    dataIndex: 'volume1',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.userclient.islimit'),
    dataIndex: 'volume2',
    render: text => TableTag(text, isLimitDict, yesNoColor)
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const [userOptions, setUserOptions] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);
  const [clientGroup, setClientGroup] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, clientOptions), [$t, clientOptions]);
  const createAddForm = useMemo(() => addForm($t, userOptions, clientGroup), [$t, userOptions, clientGroup]);
  const createModifyForm = useMemo(() => modifyForm($t, true), [$t])

  useMount(() => {
    getClientList(Request.post, defaultParams, setClientOptions);
    getUserList(Request.post, defaultParams, setUserOptions);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryLimitOrder), params);

    const { data: respData } = resp;

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');
    setClientGroup([]);
    aModalForm.current.show();
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    mModalForm.current.show(1, record);
  }
  const deleteFunc = async (record, showMsg) => {
    const deleteData = {
      ...defaultParams,
      exchangeid: record.exchangeid,
      clientid: record.clientid,
    }

    fetchFunc(MESSAGE_TYPE.DelLimitOrder, deleteData, () => {
      showMsg && message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => i.clientid);

    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach(record => deleteFunc(record, true));
      }
    });
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.clientid,
      onOk: () => deleteFunc(record, true),
    });
  }

  const addSubmit = async form => {
    // console.log(form, clientGroup);
    let formGroup = [];
    if (clientGroup?.length > 0) {
      formGroup = clientGroup.map((item, index) => ({
        clientid: item.clientid,
        exchangeid: item.exchangeid,
        volume1: form['volume1_' + index],
        volume2: form['volume2_' + index],
      }));
    } else {
      delete form.userid
      formGroup = [form];
    }

    if (formGroup.length > 0) {
      formGroup.forEach(item => modifySubmit(item));
    }
  }

  const modifySubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdLimitOrder
      : MESSAGE_TYPE.InsLimitOrder, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const { run: clientRun } = useDebounceFn(async value => {
    if (value) {
      const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserClient), {
        ...defaultParams,
        userid: value,
      });

      const { data: clients } = resp;
      if (clients?.length > 0) {
        const clientForm = {};
        const clientMap = clients.map((item, idx) => {
          clientForm['clientid_' + idx] = item.clientid;
          clientForm['exchangeid_' + idx] = exchangeDict[item.exchangeid] || item.exchangeid;
          clientForm['volume2_' + idx] = 0;

          return {
            clientid: item.clientid,
            exchangeid: item.exchangeid,
          };
        });
        setClientGroup(clientMap);

        setTimeout(() => {
          aModalForm.current.set(clientForm);
        }, 50);
      } else {
        setClientGroup([]);
      }
    } else {
      setClientGroup([]);
    }
  }, { wait: 500 });

  const formValueChange = changedValues => {
    if ('userid' in changedValues) {
      const value = changedValues.userid;
      clientRun(value);
    }
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.limit.limitorder', {
        defaultMsg: '报单限额',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
      }}
    />

    <ModalForm
      ref={aModalForm}
      onOk={addSubmit}
      formData={createAddForm}
      initValues={{
        volume2: 0,
      }}
      onValuesChange={formValueChange}
    />

    <ModalForm
      ref={mModalForm}
      onOk={modifySubmit}
      formData={createModifyForm}
    />
  </>);
}

const LimitOrder = props => (
  // <KeepAlive name={keepName}>
  <LimitOrderBase {...props} />
  // </KeepAlive>
);

export { LimitOrder };
