import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict, isLimitDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, clientOptions) => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'select',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    showSearch: true,
    options: clientOptions,
  }
}];

export const modifyForm = ($t, isModify = false) => [{
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'number',
  name: 'volume1',
  label: $t('app.stock.limit.order.volume'),
  rules: [{ required: true }],
}, {
  valueType: 'select',
  name: 'volume2',
  label: $t('app.stock.userclient.islimit'),
  rules: [{ required: true }],
  fieldProps: {
    options: selectOptions(isLimitDict)
  }
}];

export const addForm = ($t, userOptions, clientGroup) => {
  const baseForm = idx => [{
    valueType: 'divider',
    otherProps: {
      nowrap: true
    },
    fieldProps: {
      style: {
        'marginTop': 0
      }
    },
    span: 1
  }, {
    valueType: 'text',
    name: 'clientid_' + idx,
    label: $t('app.options.clientid'),
  }, {
    valueType: 'text',
    name: 'exchangeid_' + idx,
    label: $t('app.options.exchangeid'),
  }, {
    valueType: 'number',
    name: 'volume1_' + idx,
    label: $t('app.stock.limit.order.volume'),
  }, {
    valueType: 'select',
    name: 'volume2_' + idx,
    label: $t('app.stock.userclient.islimit'),
    fieldProps: {
      options: selectOptions(isLimitDict)
    }
  }];

  let groupPart = [];
  if (clientGroup?.length > 0) {
    clientGroup.forEach((_, idx) => {
      const client = baseForm(idx);

      groupPart = [
        ...groupPart,
        ...client,
      ];
    });
  } else {
    groupPart = modifyForm($t);
  }

  return [{
    valueType: 'autocomplete',
    name: 'userid',
    label: $t('app.options.userid'),
    fieldProps: {
      options: userOptions,
    }
  }, ...groupPart];
};
