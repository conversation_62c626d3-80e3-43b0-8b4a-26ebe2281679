import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, productFocus, productOptions) => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'select',
  name: 'productid',
  label: $t('app.options.productid'),
  fieldProps: {
    showSearch: true,
    onFocus: productFocus,
    options: productOptions,
    // notFoundContent: loading ? <Spin size="small" /> : '无数据',
    notFoundContent: $t('app.general.nodata'),
  }
}];

export const addForm = ($t, isModify, clientOptions, productFocus, productOptions) => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'treeSelect',
  name: 'procuctid',
  label: $t('app.options.productid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    treeData: productOptions,
    treeCheckable: false,
    // treeCheckStrictly: true,
    treeDefaultExpandAll: true,
    onDropdownVisibleChange: productFocus,
    // notFoundContent: loading ? <Spin size="small" /> : '无数据',
    notFoundContent: $t('app.general.nodata'),
  }
}, {
  valueType: 'autocomplete',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    disabled: isModify,
    options: clientOptions
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
  fieldProps: {
    readOnly: isModify
  }
}, {
  valueType: 'number',
  name: 'limitamount1',
  label: $t('app.stock.risk.buymoney'),
}, {
  valueType: 'number',
  name: 'limitamount2',
  label: $t('app.stock.risk.sellmoney'),
// }, {
//   valueType: 'number',
//   name: 'limitamount3',
//   label: $t('app.stock.risk.totalmoney'),
}];
