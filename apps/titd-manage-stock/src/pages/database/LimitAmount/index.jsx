import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, NegaNumber } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter, treeGroup } = tools;
const { getProductByExchangeStk, getClientList } = getList;

const LimitAmountBase = () => {
  const queryForm = useRef();
  const modalForm = useRef();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'procuctid',
    render: text => formatProduct(text),
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => text || (<Text type="secondary">
      {$t('app.general.all') + $t('app.options.clientid')}
    </Text>),
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: text => text || (<Text type="secondary">
      {$t('app.general.every') + $t('app.options.instrumentid')}
    </Text>),
  }, {
    title: $t('app.stock.risk.buymoney'),
    dataIndex: 'limitamount1',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.risk.sellmoney'),
    dataIndex: 'limitamount2',
    align: 'right',
    render: text => NegaNumber(text),
  // }, {
  //   title: intl.formatMessage({ id: 'app.stock.risk.totalmoney' }),
  //   dataIndex: 'limitamount3',
  //   align: 'right',
  //   render: text => NegaNumber(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const formatProduct = text => {
    const pd = productGroup.find(i => i.value === text);
    return pd ? pd.label : text;
  }

  const [exchangeId, setExchangeId] = useState();
  const [productSearch, setProductSearch] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [productGroup, setProductGroup] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);
  // const [instrumentOptions, setInstrumentOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const searchFocus = useCallback(() => {
    const qForm = queryForm.current;
    const searchParams = {
      ...defaultParams,
      exchangeid: qForm?.get('exchangeid') || 0,
    }

    getProductByExchangeStk(Request, searchParams, setProductSearch);
  }, [defaultParams]);

  const getProducts = useCallback(async (exchangeId) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryInstrumentType), {
      ...defaultParams,
      exchangeid: exchangeId,
    });

    const products = treeGroup(resp.data);

    setProductOptions(products);
  }, [defaultParams]);

  const productFocus = useCallback((open) => {
    if (open) {
      if (exchangeId) {
        getProducts(exchangeId);
      } else {
        setProductOptions([]);
        message.warning('请先选择交易所');
      }
    }
  }, [exchangeId, getProducts, message]);

  const createSearchForm = useMemo(() => searchForm($t, searchFocus, productSearch), [$t, searchFocus, productSearch]);
  const createAddForm = useMemo(() => addForm($t, isModify, clientOptions, productFocus, productOptions), [$t, isModify, clientOptions, productFocus, productOptions]);

  useMount(() => {
    getClientList(Request.post, defaultParams, setClientOptions);

    // 展示用
    getProductByExchangeStk(Request, defaultParams, setProductGroup);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryLimitAmount), params);

    const { data: respData } = resp;

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setExchangeId(null);

    modalForm.current.show();
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    setIsModify(true);
    setExchangeId(record.exchangeid);

    modalForm.current.show(1, record);
  }
  const deleteFunc = async (record, showMsg) => {
    const deleteData = {
      ...defaultParams,
      exchangeid: record.exchangeid,
      procuctid: record.procuctid,
      instrumentid: record.instrumentid,
      clientid: record.clientid,
    }

    fetchFunc(MESSAGE_TYPE.DelLimitAmount, deleteData, () => {
      showMsg && message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => formatProduct(i.procuctid));

    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach(record => deleteFunc(record, true));
      }
    });
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: formatProduct(record.procuctid),
      onOk: () => deleteFunc(record, true),
    });
  }

  const formValueChange = changedValues => {
    if ('exchangeid' in changedValues) {
      setExchangeId(changedValues.exchangeid);

      // 有值则清空品种
      modalForm.current?.set({
        'productid': undefined,
        // 'instrumentid': undefined,
      });
    }
  }
  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdLimitAmount
      : MESSAGE_TYPE.InsLimitAmount, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.limit.limitamount', {
        defaultMsg: '限购额度',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      onValuesChange={formValueChange}
    />
  </>);
}

const LimitAmount = props => (
  // <KeepAlive name={keepName}>
  <LimitAmountBase {...props} />
  // </KeepAlive>
);

export { LimitAmount };
