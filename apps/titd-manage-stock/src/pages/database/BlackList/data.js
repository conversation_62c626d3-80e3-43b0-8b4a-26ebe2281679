import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict, clientChgStatusDict } = dicts;
const { selectOptions } = formOptions;

const normFile = e => {
  if (Array.isArray(e)) {
    return e;
  }
  return e && e.fileList;
};

export const searchForm = ($t, accountOptions, clientOptions) => [{
  valueType: 'autocomplete',
  name: 'productid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions,
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'select',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    showSearch: true,
    options: clientOptions,
  }
}];

export const addForm = ($t, isModify) => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'input',
  name: 'productid',
  label: $t('app.options.instrumentid'),
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'radio',
  name: 'status',
  label: $t('app.options.mem.userclient.status'),
  rules: [{ required: true }],
  extra: $t('app.stock.blacklist.tip'),
  fieldProps: {
    optionType: 'button',
    buttonStyle: 'solid',
    options: selectOptions(clientChgStatusDict)
  }
}];

export const importForm = ($t, accountOptions, userUploadProps, instrUploadProps) => [{
  valueType: 'select',
  name: 'accountidlist',
  label: $t('app.options.accountid'),
  rules: [{ required: true }],
  fieldProps: {
    mode: 'tags',
    tokenSeparators: [',', '|'],
    options: accountOptions,
  }
}, {
  valueType: 'upload',
  name: userUploadProps.name,
  noLabel: true,
  otherProps: {
    valuePropName: 'fileList',
    getValueFromEvent: normFile
  },
  fieldProps: userUploadProps
}, {
  valueType: 'select',
  name: 'instrumentidlist',
  label: $t('app.options.instrumentid'),
  rules: [{ required: true }],
  fieldProps: {
    mode: 'tags',
    tokenSeparators: [',', '|'],
  }
}, {
  valueType: 'upload',
  name: instrUploadProps.name,
  noLabel: true,
  otherProps: {
    valuePropName: 'fileList',
    getValueFromEvent: normFile
  },
  fieldProps: instrUploadProps
}, {
  valueType: 'radio',
  name: 'status',
  label: $t('app.options.mem.userclient.status'),
  rules: [{ required: true }],
  extra: $t('app.stock.blacklist.tip'),
  fieldProps: {
    optionType: 'button',
    buttonStyle: 'solid',
    options: selectOptions(clientChgStatusDict)
  }
}];
