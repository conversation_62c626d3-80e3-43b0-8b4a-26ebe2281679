import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, List, Tag, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm, importForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
  uploadConfig,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTag, TableBadgeDot } = TableFields;
const { ModalForm } = forms;
const { chgStatusLink } = links;
const { insertBtn } = btns;
const { exchangeAllDict, clientStatusDict, tagNormalColors, tagColors } = dicts;
const { DEFAULT_CONFIGS, MESSAGE_TYPE, SITE_URL, UPLOAD_URL, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList, getClientList } = getList;

const BlackListBase = () => {
  const navigate = useNavigate();
  const modalForm = useRef();
  const modalImport = useRef();
  const queryForm = useRef();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [tips, setTips] = useState();

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
  }), [limit]);

  const fetchFunc = async (type, params, func, errFunc) => {
    try {
      const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);
      if (resp.status === 200) {
        func && func(resp.data);
      } else {
        errorResp(resp.data, navigate, message);
      }
    } catch (err) {
      errFunc && errFunc(err);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => text || (<Text type="secondary">
      {$t('app.general.all') + $t('app.options.clientid')}
    </Text>),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    render: text => text || '-',
  }, {
    title: $t('app.options.db.userinfo.name'),
    dataIndex: 'productid',
    render: text => text || '-',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: text => text || (<Text type="secondary">
      {$t('app.general.all') + $t('app.options.instrumentid')}
    </Text>),
  }, {
    title: $t('app.options.mem.userclient.status'),
    dataIndex: 'tdstatus',
    render: text => TableTag(text, clientStatusDict, tagColors),
  }, {
    title: $t('app.stock.blacklist.errmsg'),
    dataIndex: 'responsestr',
    render: text => text && formatStr(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 110,
    render: (_, record) => record.status !== 3 && (
      <ActionLinks links={[
        chgStatusLink(toChgStatus, record),
      ]} />
    )
  }];

  const formatStr = text => {
    const arr = text?.split(';')?.filter(i => i);

    const obj = arr.map((item, idx) => {
      const itemArr = item.split(',');
      const svrid = itemArr[0].match(/:(.*)/);
      const reason = itemArr[1].match(/:(.*)/);

      // console.log(svrid[1], reason[1]);
      return (
        <List.Item key={idx}>
          <Tag>{svrid[1]}</Tag> {reason[1]}
        </List.Item>
      );
    });

    return (
      <div style={{
        maxHeight: 100,
        overflow: 'auto',
      }}>
        <List size="small" >{obj}</List>
      </div>
    );
  }

  const [isModify, setIsModify] = useState(false);
  const [accountOptions, setAccountOptions] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);

  const instrFunc = resp => {
    // console.log('instrFunc', resp);
    if (resp && resp !== '') {
      modalImport.current.set({
        instrumentidlist: resp.split('|')?.filter(i => i !== ''),
      });
    }
  }
  const userFunc = resp => {
    // console.log('userFunc', resp);
    if (resp && resp !== '') {
      modalImport.current.set({
        accountidlist: resp.split('|')?.filter(i => i !== ''),
      });
    }
  }

  const instrUploadProps = useMemo(() => ({
    ...uploadConfig($t, message, instrFunc),
    name: 'instrFile',
    action: UPLOAD_URL(MESSAGE_TYPE.MemUplBlackInstr),
    data: defaultParams,
  }), [$t, message, defaultParams]);
  const userUploadProps = useMemo(() => ({
    ...uploadConfig($t, message, userFunc),
    name: 'userFile',
    action: UPLOAD_URL(MESSAGE_TYPE.MemUplBlackUser),
    data: defaultParams,
  }), [$t, message, defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, accountOptions, clientOptions), [$t, accountOptions, clientOptions]);
  const createAddForm = useMemo(() => addForm($t, isModify), [$t, isModify]);
  const createImportForm = useMemo(() => importForm($t, accountOptions, userUploadProps, instrUploadProps), [$t, accountOptions, userUploadProps, instrUploadProps]);

  useMount(() => {
    // 获取资金账户列表
    getAccountList(Request.post, defaultParams, setAccountOptions);
    // 获取股东代码列表
    getClientList(Request.post, defaultParams, setClientOptions);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryClientStatus), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }
  const onSearch = (values) => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  // const toInsert = () => {
  //   setIsModify(false);
  //   setTips(null);
  //   modalForm.current.show(0);
  // }
  const toImport = () => {
    modalImport.current.show(0);
  }

  const toChgStatus = record => {
    // console.log(record);
    const newRecord = {
      ...record,
      productid: record.instrumentid,
      status: record.tdstatus,
    }

    setIsModify(true);
    setTips(null);
    modalForm.current.show($t('app.general.chgstatus'), newRecord);
  }

  const addSubmit = form => {
    // console.log(form);
    if (tips) {
      modalForm.current.close();
      onRefresh();
    } else {
      const postData = {
        ...defaultParams,
        ...form,
      }

      fetchFunc(MESSAGE_TYPE.UpdClientStatus, postData, resp => {
        setTips(resp);
      }, err => {
        console.log(err);
      });
    }
  }
  const importSubmit = form => {
    const postData = {
      ...defaultParams,
      str1: form.accountidlist?.join('|'),
      str2: form.instrumentidlist?.join('|'),
      intvolume1: form.status,
    }

    // console.log(postData);
    fetchFunc(MESSAGE_TYPE.MemInsBlackList, postData, resp => {
      if (resp.errcode === 200) {
        message.success(resp.errmessage);
      } else {
        message.error(resp.errmessage);
      }
      onRefresh();
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.black.blacklist', {
        defaultMsg: '场上交易黑名单',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toImport),
        ],
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      loading={loading}
      lateClose={true}
      okText={tips && $t('app.general.close')}
    >
      {tips && (
        <List
          size="small"
          dataSource={tips}
          renderItem={item => (
            <List.Item>
              {item.svrid}
              {item.responsecode && item.responsecode === -1 ? (
                <Text type="danger">{item.responsestr}</Text>
              ) : (
                <Text type="success">{CURD.StatusOkMsg}</Text>
              )}
            </List.Item>
          )}
        />
      )}
    </ModalForm>

    <ModalForm
      ref={modalImport}
      onOk={importSubmit}
      formData={createImportForm}
    />
  </>);
}

const BlackList = props => (
  // <KeepAlive name={keepName}>
  <BlackListBase {...props} />
  // </KeepAlive>
);

export { BlackList };
