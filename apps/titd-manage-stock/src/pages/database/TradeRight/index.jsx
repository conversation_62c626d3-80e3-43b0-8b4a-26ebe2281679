import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, Space, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  formOptions,
  optDiff,
  updateRowData,
  errorResp,
} from '@titd/publics/utils';

const { Text, Link } = Typography;
const { IndexColumn, TableTag, TableBadgeDot } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeDict, exchangeAllDict, clientRightDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;
const { getClientList } = getList;
const { checkOptions } = formOptions;

const TradeRightBase = () => {
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    key: 'clientid',
    sorter: sortByName('clientid'),
    sortOrder: sortedInfo.columnKey === 'clientid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>
  }, {
  //   title: $t('app.options.clienttype'),
  //   dataIndex: 'clienttype',
  //   filters: checkOptions(clientTypeDict),
  //   filteredValue: filteredInfo.clienttype || null,
  //   onFilter: (value, record) => record.clienttype === value,
  //   render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  // }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    filters: checkOptions(exchangeDict),
    filteredValue: filteredInfo.exchangeid || null,
    onFilter: (value, record) => record.exchangeid === value,
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.templateid'),
    dataIndex: 'templateid',
    render: text => templateFunc(text),
  }, {
    title: $t('app.options.rightid'),
    dataIndex: 'rightid',
    render: text => rightFunc(text)
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const templateFunc = text => text && (<Space>
    {text.map((item, idx) => {
      return (<Link underline key={idx} onClick={() => navigate('/db/tradetpl', { state: { datas: item } })}>{item}</Link>);
    })}
  </Space>);
  const rightFunc = text => text && text?.map((item, idx) => (
    <span key={idx}>
      {TableTag(item, clientRightDict, tagNormalColors, true)}
    </span>
  ));

  const [exchangeId, setExchangeId] = useState();
  const [clientOptions, setClientOptions] = useState([]);
  const [tplOptions, setTplOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const clientFocus = useCallback(() => {
    getClientList(Request.post, defaultParams, setClientOptions);
  }, [defaultParams]);

  const tplFocus = useCallback(async () => {
    if (exchangeId) {
      const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeTemplate), {
        ...defaultParams,
        exchangeid: exchangeId,
      });

      const { data: respData } = resp;
      if (respData?.length > 0) {
        const tplIds = optDiff(respData, 'templateid');
        setTplOptions(tplIds);
      } else {
        setTplOptions([]);
      }
    } else {
      setTplOptions([]);
      message.warning('请先选择交易所');
    }
  }, [defaultParams, exchangeId]); // eslint-disable-line react-hooks/exhaustive-deps

  // 取权限模版
  // const getOptions = useCallback(async (value) => {
  //   const optionParams = {
  //     ...defaultParams,
  //     exchangeid: value || 0,
  //   };

  //   const tpl = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeTemplate), optionParams);

  //   // 取不同项
  //   if (tpl?.length > 0) {
  //     const tplIds = optDiff(tpl, 'templateid');
  //     setTplGroup(tplIds);

  //     // 如果权限模版有值则清空
  //     if (value) {
  //       modalForm.current?.set({
  //         'templateid': []
  //       });
  //     }
  //   }
  // }, [post, defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, clientFocus, clientOptions, false), [$t, clientFocus, clientOptions]);

  // const { run } = useDebounceFn((rules, value, callback) => {
  //   var isExist = clientOptions.find(item => item.value === value);
  //   if (isExist && !isModify) {
  //     callback(new Error('交易编码已存在，请重新输入'));
  //   } else {
  //     callback();
  //   }
  // }, { wait: 500 });

  const createAddForm = useMemo(() => addForm($t, isModify, tplFocus, tplOptions), [$t, isModify, tplFocus, tplOptions]);

  useUnmount(() => cancel());

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeRight), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        const clients = optDiff(respData, 'clientid').map(item => ({
          ...item,
          rightLen: resp.length,
        }));
        setClientOptions(clients);
      } else {
        setClientOptions([]);
      }
    }

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    setFilteredInfo({});
    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    run,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeRight), {
      ...defaultParams,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }
    return null;
  }

  const onRefresh = showAuto => {
    run({
      ...defaultParams,
      showAuto: showAuto,
    });
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setExchangeId(null);

    modalForm.current.show();
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      setIsModify(true);
      setExchangeId(newRecord.exchangeid);

      modalForm.current.show(1, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
      });
      mutate(newData);
    } else {
      message.error($t('app.general.deleted'));
      onRefresh();
    }
  }
  const deleteFunc = async (record) => {
    const deleteData = {
      ...defaultParams,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
    }

    fetchFunc(MESSAGE_TYPE.DelTradeRight, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }
  const toDeleteMore = (records) => {
    // console.log('Delete More');
    const ids = records.map(i => i.clientid);
    Confirm({
      modal,
      content: <>{ids.join(', ')}</>,
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record);
        });
      }
    });
  }
  const toDelete = (record) => {
    // console.log('Delete');
    Confirm({
      modal,
      content: <>{record.clientid}</>,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }

  const formValueChange = changedValues => {
    if ('exchangeid' in changedValues) {
      const value = changedValues.exchangeid;

      setExchangeId(value);

      modalForm.current?.set({
        'templateid': [],
      });
    }
  }
  const addSubmit = async (form, type) => {
    // 已存在交易权限则返回
    if (type === 0) {
      const findNum = dataSource.findIndex(item => item.clientid + item.clienttype + item.exchangeid === form.clientid + form.clienttype + form.exchangeid);

      if (findNum !== -1) {
        message.error('已存在交易权限');
        return;
      }
    }

    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdTradeRight
      : MESSAGE_TYPE.InsTradeRight, postData, () => {
        message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
        onRefresh(!type);
      });
  }

  const tableChange = (pagination, filters, sorter) => {
    // console.log('Various parameters', pagination, filters, sorter);
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.traderight', {
        defaultMsg: '交易权限',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      onValuesChange={formValueChange}
      initValues={{
        clienttype: 1,
      }}
    />
  </>);
}

const TradeRight = props => (
  // <KeepAlive name={keepName}>
  <TradeRightBase {...props} />
  // </KeepAlive>
);

export { TradeRight };
