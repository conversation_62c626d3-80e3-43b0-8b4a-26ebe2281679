import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Tooltip, Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  currency,
  tools,
  optDiff,
  formOptions,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot } = TableFields;
const { accountTypeDict, exchangeDict, exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { formatDot, formatName } = currency;
const { sortByName, objFilter } = tools;
const { checkOptions } = formOptions;

const FrozenFundsBase = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();
  const { $t } = useFormattedMessage();

  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    key: 'accountid',
    sorter: sortByName('accountid'),
    sortOrder: sortedInfo.columnKey === 'accountid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.accounttype'),
    dataIndex: 'accounttype',
    filters: checkOptions(accountTypeDict),
    filteredValue: filteredInfo.accounttype || null,
    onFilter: (value, record) => record.accounttype === value,
    render: text => TableBadgeDot(text, accountTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    filters: checkOptions(exchangeDict),
    filteredValue: filteredInfo.exchangeid || null,
    onFilter: (value, record) => record.exchangeid === value,
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.funds.available'),
    dataIndex: 'frozenfunds',
    align: 'right',
    render: text => text ? (
      <Tooltip title={formatDot(text)}>
        <Text strong>{formatName(text)}</Text>
      </Tooltip>
    ) : '-',
  }];

  const [accountOptions, setAccountOptions] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, accountOptions), [$t, accountOptions]);

  useUnmount(() => cancel());

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryFrozenFunds), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        const autoId = optDiff(respData, 'accountid');
        setAccountOptions(autoId);
      } else {
        setAccountOptions([]);
      }
    }

    setFilteredInfo({});
    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const tableChange = (pagination, filters, sorter) => {
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.frozenfunds', {
        defaultMsg: '资金信息',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: refresh,
        checkable: false,
        handleChange: tableChange,
      }}
    />
  </>);
}

// const FrozenFundsAlive = ({ keepName, ...props }) => (
const FrozenFunds = props => (
  // <KeepAlive name={keepName}>
  <FrozenFundsBase {...props} />
  // </KeepAlive>
);

export { FrozenFunds };
