import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict } = dicts;
const { selectOptions, selectObjOptions } = formOptions;

export const searchForm = ($t, groupOptions) => [{
  valueType: 'select',
  name: 'groupid',
  label: $t('app.stock.concentrate.group'),
  fieldProps: {
    options: selectObjOptions(groupOptions),
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}];
