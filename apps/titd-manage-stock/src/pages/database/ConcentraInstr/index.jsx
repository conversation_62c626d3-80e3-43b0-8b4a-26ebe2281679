import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Space, Tag, List, Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';
import VirtualList from 'rc-virtual-list';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot } = TableFields;
const { DrawerForm } = forms;
const { detailLink } = links;
const { exchangeDict, exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;


const maxLen = 20;
const otherHeight = 140;

const ConcentraInstrBase = () => {
  const queryForm = useRef();
  const drawerForm = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const [listData, setListData] = useState([]);
  // TODO 根据页面变化调节高度
  const [listHeight] = useState(window.innerHeight - otherHeight);
  const [lastRecord, setLastRecord] = useState(null);
  // const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [total, setTotal] = useState(0);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.stock.concentrate.group'),
    dataIndex: 'groupid',
    render: text => groupOptions[text] || text,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (_, record) => formatInstrument(record),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 80,
    render: (_, record) => record.instrumentid?.length === maxLen && (
      <ActionLinks links={[
        detailLink(toDetail, record),
      ]} />
    )
  }];

  const tagGroup = (searchId, group) => {
    return group.map((item, idx) => {
      const itemData = item.split('|');

      return <Tag color={searchId === itemData[0] ? 'orange' : ''} key={idx}>{itemData[1] ? `${itemData[0]} (${itemData[1]})` : itemData[0]}</Tag>;
    });
  }
  const formatInstrument = (record, isCollapsed = true) => {
    const text = record.instrumentid;
    const searchId = record.instrid;
    const len = text?.length;

    if (len > 0) {
      // let tagArr;
      // if (isCollapsed && len > maxLen) {
      //   tagArr = tagGroup(searchId, text.slice(0, maxLen));
      //   tagArr.push(
      //     <Tag
      //       key={len}
      //       style={{
      //         background: '#fff',
      //         borderStyle: 'dashed',
      //       }}
      //     >+ {len - maxLen}</Tag>
      //   );
      // } else {
      //   tagArr = tagGroup(searchId, text);
      // }
      let tagArr = tagGroup(searchId, text);
      if (isCollapsed && len === maxLen) {
        tagArr.push(
          <Tag
            key={len}
            style={{
              background: '#fff',
              borderStyle: 'dashed',
            }}
          >+ 更多</Tag>
        );
      }
      return (
        <Space size={[0, 16]} wrap style={{ maxWidth: '800px' }}>{tagArr}</Space>
      );
    }
    return null;
  };

  const [groupOptions, setGroupOptions] = useState({});

  const createSearchForm = useMemo(() => searchForm($t, groupOptions), [$t, groupOptions]);

  useMount(() => {
    getGroupList();
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryConcentrateInstr), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        instrid: params.instrumentid,
      }));
      return tableData;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getGroupList = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryConcentrateGroup), defaultParams);

    const { data: respGroup } = resp;
    if (respGroup?.length > 0) {
      const groupMap = {};
      respGroup.forEach(item => {
        groupMap[item.groupid] = item.groupname;
      });

      setGroupOptions(groupMap);
    }
  }

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toDetail = record => {
    // console.log('Detail', record, lastRecord);
    if (lastRecord?.groupid !== record.groupid || lastRecord?.exchangeid !== record.exchangeid) {
      setLastRecord(record);
      setListData([]);

      // 填充已经有数据
      if (record.instrumentid?.length === maxLen) {
        const postParams = {
          ...defaultParams,
          beginno: 0,
          endno: 0,
          groupid: record.groupid,
          exchangeid: record.exchangeid,
        }

        getMoreList(postParams);
      }
    }
    drawerForm.current.show(`${groupOptions[record.groupid]} - ${exchangeDict[record.exchangeid]}`, record);
  }

  const formValueChange = changedValues => {
    if ('groupid' in changedValues) {
      if (changedValues.groupid) {
        queryForm.current?.set({
          'instrumentid': undefined,
        });
      }
    }

    if ('instrumentid' in changedValues) {
      if (changedValues.instrumentid) {
        queryForm.current?.set({
          'groupid': undefined,
        });
      }
    }
  }

  // useEffect(() => {
  //   if (listParams && !isLast) {
  //     getMoreList(listParams);
  //   }
  // }, [listParams, isLast]); // eslint-disable-line react-hooks/exhaustive-deps

  const getMoreList = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryConcentrateInstr), params);

    const { data: respData } = resp;
    const respInstr = respData[0]?.instrumentid;
    if (respInstr?.length > 0) {
      const instrList = respInstr?.map(item => item.split('|'));

      setListData(instrList);
      setTotal(respData[0].rsptotnum);
    }
  }

  const createVirtualList = () => {
    return (<>
      <List size="small">
        <VirtualList
          data={listData}
          height={listHeight}
          itemHeight={39}
          itemKey="clientid"
          // onScroll={onScroll}
        >
          {item => (
            <List.Item key={item[0]}>
              <Text>{item[0]}</Text>
              <Text>{item[1]}</Text>
            </List.Item>
          )}
        </VirtualList>
      </List>
      <div style={{ textAlign: 'right', marginTop: '10px' }}>共 {total} 条</div>
    </>);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.martrade.concentrainstr', {
        defaultMsg: '集中度合约',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
        onValuesChange: formValueChange,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        checkable: false,
      }}
    />

    <DrawerForm
      width={300}
      ref={drawerForm}
      formData={[]}
    >{listData.length > 0 && createVirtualList()}</DrawerForm>
  </>);
}

const ConcentraInstr = props => (
  // <KeepAlive name={keepName}>
  <ConcentraInstrBase {...props} />
  // </KeepAlive>
);

export { ConcentraInstr };
