import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  formOptions,
  optDiff,
  errorResp,
  updateRowData,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeDict, exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;
const { getUserList, getRiskUserList, getSegmentSvrList } = getList;
const { checkOptions } = formOptions;

const UserClientBase = () => {
  const queryForm = useRef(null);
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  // const [selectRecord, setSelectRecord] = useState(null);
  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultUserType = 0;
  const defaultParams = DEFAULT_SESSION_PARAMS();
  const defaultSearch = {
    ...defaultParams,
    usertype: defaultUserType,
  }

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.segmentid'),
    dataIndex: 'segmentid',
  }, {
    title: $t('app.options.defaultsvrid'),
    dataIndex: 'defaultsvrid',
  }, {
  //   title: TableTitle(
  //     $t('app.options.serverid'),
  //     $t('app.options.serverid.long')
  //   ),
  //   dataIndex: 'serverid',
  //   render: text => serverList.find(item => item.value === text)?.label || text,
  // }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    key: 'clientid',
    // defaultSortOrder: 'ascend',
    // sortDriections: ['ascend', 'descend', 'ascend'],
    sorter: sortByName('clientid'),
    sortOrder: sortedInfo.columnKey === 'clientid' ? sortedInfo.order : '',
    render: text => <Text strong>{text}</Text>,
  }, {
  //   title: $t('app.options.clienttype'),
  //   dataIndex: 'clienttype',
  //   filters: checkOptions(clientTypeDict),
  //   filteredValue: filteredInfo.clienttype || null,
  //   onFilter: (value, record) => record.clienttype === value,
  //   render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  // }, {
    title: $t('app.options.bizpbu'),
    dataIndex: 'bizpbu',
  }, {
    title: $t('app.options.salesnumb'),
    dataIndex: 'salesnumb',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    filters: checkOptions(exchangeDict),
    filteredValue: filteredInfo.exchangeid || null,
    onFilter: (value, record) => record.exchangeid === value,
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => record.usertype ? null : (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const [searchUser, setSearchUser] = useState([]);
  const [userGroup, setUserGroup] = useState([]);
  const [riskGroup, setRiskGroup] = useState([]);
  const [clientGroups, setClientGroups] = useState([]);
  const [isModify, setIsModify] = useState(false);
  const [userType, setUserType] = useState(defaultUserType);
  const [noActions, setNoActions] = useState(defaultUserType);

  const [segmentList, setSegmentList] = useState([]);
  const [serverMap, setServerMap] = useState({});
  const [serverList, setServerList] = useState([]);

  const userFocus = useCallback(() => {
    if (userType) {
      getRiskUserList(Request.post, defaultParams, setSearchUser);
    } else {
      getUserList(Request.post, defaultParams, setSearchUser);
    }
  }, [defaultParams, userType]);
  const createSearchForm = useMemo(() => searchForm($t, userFocus, searchUser, clientGroups), [$t, userFocus, searchUser, clientGroups]);
  const createAddForm = useMemo(() => addForm($t, isModify, [...userGroup, ...riskGroup], segmentList, serverList), [$t, isModify, userGroup, riskGroup, segmentList, serverList]);

  useMount(() => {
    getUserList(Request.post, defaultParams, setUserGroup);
    getRiskUserList(Request.post, defaultParams, setRiskGroup);
    // getServerList(request.post, defaultParams, setServerList, navigate);
    getSegmentSvrList(Request.post, defaultParams, setSegmentList, navigate, setServerMap);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserClient), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        // const users = optDiff(resp, 'userid');
        // setUserGroup(users);
        const clients = optDiff(respData, 'clientid');
        setClientGroups(clients);
      } else {
        setClientGroups([]);
      }
    }

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    setFilteredInfo({});
    setSortedInfo({});

    const usertype = params.reset ? defaultUserType : userType;
    setNoActions(usertype);

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        defaultsvrid: item.defaultsvr ? item.enablesvrid : '',
        usertype: usertype,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    run,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultSearch,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserClient), {
      ...defaultParams,
      userid: record.userid,
      clientid: record.clientid,
      clienttype: record.clienttype,
      bizpbu: record.bizpbu,
      salesnumb: record.salesnumb,
      exchangeid: record.exchangeid,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }

    return null;
  }

  const onRefresh = showAuto => {
    run({
      ...defaultSearch,
      showAuto: showAuto,
    });
  }
  const onSearch = values => {
    run({
      ...defaultSearch,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    // FIXED userType异步更改
    setUserType(defaultUserType);

    run({
      ...defaultSearch,
    });
  }

  const toInsert = () => {
    // console.log('Insert');

    // 获取最新用户列表
    getUserList(Request.post, defaultParams, setUserGroup);

    setIsModify(false);
    modalForm.current.show(0, {
      clienttype: 1,
    });
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      newRecord.defaultsvr && (newRecord.defaultsvrid = newRecord.enablesvrid);
      if (newRecord.segmentid) {
        createServerList(newRecord.segmentid);
      }

      setIsModify(true);
      modalForm.current.show(1, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
        usertype: userType,
      });
      mutate(newData);
    } else {
      message.error($t('app.general.deleted'));
      onRefresh();
    }
    // setSelectRecord(record);
  }
  const deleteFunc = async (record, showMsg) => {
    const deleteData = {
      ...defaultParams,
      userid: record.userid,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
    }

    fetchFunc(MESSAGE_TYPE.DelUserClient, deleteData, () => {
      showMsg && message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => i.userid + ':' + i.clientid);

    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach(record => deleteFunc(record, true));
      }
    });
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.userid + ':' + record.clientid,
      onOk: () => deleteFunc(record, true),
    });
  }
  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    // 修改操作为先删除再新增
    // if (type) {
    //   // console.log(selectRecord, form);
    //   deleteFunc(selectRecord);
    // }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdUserClient
      : MESSAGE_TYPE.InsUserClient, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }

  const qFormValueChange = changedValues => {
    if ('usertype' in changedValues) {
      const value = changedValues.usertype;

      setUserType(value);
      // 交易编码赋值
      queryForm.current?.set({
        'userid': undefined,
      });
    }
  }

  const createServerList = value => {
    const servers = serverMap[value];
    if (servers) {
      // 设置默认值
      modalForm.current.set({
        defaultsvrid: servers[0].serverid,
      });

      // 更改服务器列表
      setServerList(servers.map(i => ({
        label: i.servername,
        value: i.serverid,
      })));
    }
  }
  const aFormValueChange = changedValues => {
    if ('segmentid' in changedValues) {
      const value = changedValues.segmentid;
      if (value) {
        createServerList(value);
      } else {
        modalForm.current.set({
          defaultsvrid: undefined,
        });

        setServerList([]);
      }
    }
  }

  const tableChange = (pagination, filters, sorter) => {
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.userclient', {
        defaultMsg: '用户交易编码',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          usertype: defaultUserType
        },
        onSearch: onSearch,
        onReset: onReset,
        onValuesChange: qFormValueChange,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        actionBtns: noActions ? [] : [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      onValuesChange={aFormValueChange}
      initValues={{
        clienttype: 1,
      }}
    />
  </>);
}

const UserClient = props => (
  // <KeepAlive name={keepName}>
  <UserClientBase {...props} />
  // </KeepAlive>
);

export { UserClient };
