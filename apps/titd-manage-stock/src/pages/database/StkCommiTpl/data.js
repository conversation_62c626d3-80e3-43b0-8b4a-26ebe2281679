import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, tplOptions, productFocus, productOptions) => [{
  valueType: 'autocomplete',
  name: 'templateid',
  label: $t('app.stock.template.templateid'),
  fieldProps: {
    options: tplOptions
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'select',
  name: 'productid',
  label: $t('app.stock.template.productid'),
  fieldProps: {
    showSearch: true,
    onFocus: productFocus,
    options: productOptions,
    // notFoundContent: loading ? <Spin size="small" /> : '无数据',
    notFoundContent: $t('app.general.nodata'),
  }
// }, {
//   valueType: 'select',
//   name: 'accountid',
//   label: '资金账户',
//   fieldProps: {
//     showSearch: true,
//     options: accountOptions
//   }
}];

export const addForm = ($t, isModify, otherDisable, productFocus, productOptions) => [{
  valueType: 'input',
  name: 'templateid',
  label: $t('app.stock.template.templateid'),
  rules: !isModify ? [
    { required: true },
    { min: 3, max: 26 },
  ] : null,
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict),
  }
}, {
//   valueType: 'select',
//   name: 'producttype',
//   label: '证券类别',
//   fieldProps: {
//     options: selectObjOptions(stockTypeDict),
//   }
// }, {
  valueType: 'treeSelect',
  name: 'productid',
  label: $t('app.stock.template.productid'),
  fieldProps: {
    disabled: isModify,
    treeData: productOptions,
    treeCheckable: true,
    // treeCheckStrictly: true,
    treeDefaultExpandAll: true,
    onOpenChange: productFocus,
    // notFoundContent: loading ? <Spin size="small" /> : '无数据',
    notFoundContent: $t('app.general.nodata'),
  },
  span: 1
}, otherDisable ? {
  valueType: 'hidden',
  name: 'productother',
  noStyle: true
} : {
  valueType: 'input',
  name: 'productother',
  label: $t('app.stock.template.productother'),
  rules: [{ required: true }]
}, {
  valueType: 'divider',
  otherProps: {
    nowrap: true
  },
  fieldProps: {
    style: {
      'marginTop': 0
    }
  },
  span: 1
}, {
  valueType: 'number',
  name: 'openbymoney',
  label: $t('app.options.mem.commimargin.openbymoney'),
  fieldProps: {
    step: 0.0001,
  }
}, {
  valueType: 'number',
  name: 'openbyvolume',
  label: $t('app.options.mem.commimargin.openbyvolume'),
}, {
  valueType: 'number',
  name: 'closebymoney',
  label: $t('app.options.mem.commimargin.closebymoney'),
  fieldProps: {
    step: 0.0001,
  }
}, {
  valueType: 'number',
  name: 'closebyvolume',
  label: $t('app.options.mem.commimargin.closebyvolume'),
}, {
  valueType: 'number',
  name: 'closetodaybymoney',
  label: $t('app.options.mem.commimargin.closetodaybymoney'),
  fieldProps: {
    step: 0.0001,
  }
}, {
  valueType: 'number',
  name: 'closetodaybyvolume',
  label: $t('app.options.mem.commimargin.closetodaybyvolume'),
}, {
  valueType: 'number',
  name: 'openmax',
  label: TableTitle(
    $t('app.options.mem.commimargin.openmax.short'),
    $t('app.options.mem.commimargin.openmax')
  ),
}, {
  valueType: 'number',
  name: 'openmin',
  label: TableTitle(
    $t('app.options.mem.commimargin.openmin.short'),
    $t('app.options.mem.commimargin.openmin')
  ),
}, {
  valueType: 'number',
  name: 'closemax',
  label: TableTitle(
    $t('app.options.mem.commimargin.closemax.short'),
    $t('app.options.mem.commimargin.closemax')
  ),
}, {
  valueType: 'number',
  name: 'closemin',
  label: TableTitle(
    $t('app.options.mem.commimargin.closemin.short'),
    $t('app.options.mem.commimargin.closemin')
  ),
}, {
  valueType: 'number',
  name: 'closetodaymax',
  label: TableTitle(
    $t('app.options.mem.commimargin.closetodaymax.short'),
    $t('app.options.mem.commimargin.closetodaymax')
  ),
}, {
  valueType: 'number',
  name: 'closetodaymin',
  label: TableTitle(
    $t('app.options.mem.commimargin.closetodaymin.short'),
    $t('app.options.mem.commimargin.closetodaymin')
  ),
}];
