import { useRef, useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  optDiff,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, NegaNumber } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter, treeGroup } = tools;
const { getProductByExchangeStk } = getList;

const StkCommiTplBase = ({ tplId }) => {
  const queryForm = useRef();
  const modalForm = useRef();
  const navigate = useNavigate();
  const location = useLocation();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [sortedInfo, setSortedInfo] = useState({});
  const [otherDisable, setOtherDisable] = useState(true);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.stock.template.templateid'),
    dataIndex: 'templateid',
    key: 'templateid',
    sorter: sortByName('templateid'),
    sortOrder: sortedInfo.columnKey === 'templateid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.stock.template.productid'),
    dataIndex: 'productid',
    render: text => productFunc(text),
  }, {
    title: $t('app.options.mem.commimargin.openbymoney') + $t('app.stock.mem.commimargin.percent'),
    dataIndex: 'openbymoney',
    align: 'right',
    render: text => NegaNumber(text * 10000, 4)
  }, {
    title: $t('app.options.mem.commimargin.openbyvolume'),
    dataIndex: 'openbyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closebymoney') + $t('app.stock.mem.commimargin.percent'),
    dataIndex: 'closebymoney',
    align: 'right',
    render: text => NegaNumber(text * 10000, 4)
  }, {
    title: $t('app.options.mem.commimargin.closebyvolume'),
    dataIndex: 'closebyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closetodaybymoney') + $t('app.stock.mem.commimargin.percent'),
    dataIndex: 'closetodaybymoney',
    align: 'right',
    render: text => NegaNumber(text * 10000, 4)
  }, {
    title: $t('app.options.mem.commimargin.closetodaybyvolume'),
    dataIndex: 'closetodaybyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.openmax'),
    dataIndex: 'openmax',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.openmin'),
    dataIndex: 'openmin',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closemax'),
    dataIndex: 'closemax',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closemin'),
    dataIndex: 'closemin',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closetodaymax'),
    dataIndex: 'closetodaymax',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closetodaymin'),
    dataIndex: 'closetodaymin',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => record.templateid !== '公司默认' ? (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    ) : null,
  }];

  const productFunc = text => {
    // console.log(stockTypeTree, stockGroup);
    if (productGroup.length > 0) {
      const stock = productGroup.find(i => i.value === text);

      // 其他做特别处理
      if (stock) {
        return stock.label;
      } else {
        return `${$t('app.stock.template.product.other')}: ${text}`
      }
    }

    return text;
  }

  const [tplOptions, setTplOptions] = useState([]);
  const [exchangeId, setExchangeId] = useState();
  // const [accountOptions, setAccountOptions] = useState([]);
  const [productSearch, setProductSearch] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [productGroup, setProductGroup] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const searchFocus = useCallback(() => {
    const qForm = queryForm.current;
    const searchParams = {
      ...defaultParams,
      exchangeid: qForm?.get('exchangeid') || 0,
    }

    getProductByExchangeStk(Request, searchParams, setProductSearch);
  }, [defaultParams]);

  const getProducts = useCallback(async (exchangeId) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryInstrumentType), {
      ...defaultParams,
      exchangeid: exchangeId,
    });

    const { data: respData } = resp;
    const products = treeGroup(respData, true);

    setProductOptions(products);
  }, [defaultParams]);

  const productFocus = useCallback(open => {
    if (open) {
      if (exchangeId) {
        getProducts(exchangeId);
      } else {
        setProductOptions([]);
        message.warning('请先选择交易所');
      }
    }
  }, [exchangeId, getProducts, message]);

  // const { run } = useDebounceFn((value) => {
  //   var isExist = autoOptions.find(item => item.value === value);
  //   if (isExist) {
  //     message.error('权限模版已存在，请重新输入');
  //     return;
  //   }
  // }, { wait: 500 });

  // const { run } = useDebounceFn((_, value, callback) => {
  //   var isExist = tplOptions.find(item => item.value === value);
  //   if (isExist) {
  //     callback(new Error('手续费模版已存在，请重新输入'));
  //   } else {
  //     callback();
  //   }
  // }, { wait: 500 });

  const createSearchForm = useMemo(() => searchForm($t, tplOptions, searchFocus, productSearch), [$t, tplOptions, searchFocus, productSearch]);
  // const createAddForm = useMemo(() => addForm(isModify, run, productGroup), [isModify, run, productGroup]);
  const createAddForm = useMemo(() => addForm($t, isModify, otherDisable, productFocus, productOptions), [$t, isModify, otherDisable, productFocus, productOptions]);

  useMount(() => {
    // 展示用
    getProductByExchangeStk(Request, defaultParams, setProductGroup);
  });
  useUnmount(() => cancel());

  useEffect(() => {
    if (tplId) {
      queryForm.current?.set({
        'templateid': tplId
      });
      onSearch({ templateid: tplId });
    }
  }, [tplId]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async ({showAuto, ...params}) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryCommiTemplate), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        const tpl = optDiff(resp, 'templateid');
        setTplOptions(tpl);
      } else {
        setTplOptions([]);
      }
    }

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);

    // 去除Route的过滤项
    tplId && navigate(location.pathname, { replace: true });
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setExchangeId(null);
    setProductOptions([]);
    setOtherDisable(true);

    modalForm.current.show();
  }
  const toUpdate = record => {
    setIsModify(true);
    setExchangeId(record.exchangeid);
    getProducts(record.exchangeid);

    const newRecord = {...record};
    const stockIdx = productGroup.findIndex(i => i.value === newRecord.productid);
    if (stockIdx === -1) {
      newRecord.productother = record.productid;
      newRecord.productid = ['OTHER'];
    //   setOtherDisable(false);
    // } else {
    //   setOtherDisable(true);
    } else {
      newRecord.productid = [newRecord.productid];
    }
    setOtherDisable(true);

    modalForm.current.show(1, newRecord);
  }
  const deleteFunc = async record => {
    const deleteData = {
      ...defaultParams,
      templateid: record.templateid,
      exchangeid: record.exchangeid,
      productid: record.productid,
    }

    fetchFunc(MESSAGE_TYPE.DelCommiTemplate, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => i.templateid);
    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach(record => {
          deleteFunc(record);
        });

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.templateid,
      onOk: () => {
        // console.log('ok', record);
        deleteFunc(record);

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }

  const formValueChange = changedValues => {
    if ('exchangeid' in changedValues) {
      const value = changedValues.exchangeid;

      setExchangeId(value);

      // 有值则清空品种
      if (value) {
        modalForm.current?.set({
          'productid': []
        });
      }
    }
    if ('productid' in changedValues) {
      const value = changedValues.productid;

      // if (value === 'OTHER') {
      if (value.indexOf('OTHER') > -1) {
        setOtherDisable(false);
      } else {
        setOtherDisable(true);
      }

      // modalForm.current?.set({
      //   'productother': null
      // });
    }
  }
  const addSubmit = async (form, type) => {
    // 其他类别
    // if (form.productid === 'OTHER') {
    //   form.productid = form.productother
    // }
    const idx = form.productid.indexOf('OTHER');
    if (idx > -1) {
      form.productid.splice(idx, 1);
      form.productid.push(form.productother);
    }
    delete form.productother;

    const postData = {
      ...defaultParams,
      ...form,
      productid: form.productid.join('|'),
    }

    // if (form.productid) {
    //   postData.productid = form.productid.map(item => item.value).join(',');
    // }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdCommiTemplate
      : MESSAGE_TYPE.InsCommiTemplate, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    // console.log('Various parameters', pagination, filters, sorter);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.stkcommitpl', {
        defaultMsg: '手续费模版',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      formGroup
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      onValuesChange={formValueChange}
    />
  </>);
}

const StkCommiTpl = props => {
  const location = useLocation();
  const tplId = location.state?.datas || '';

  return (
    // <KeepAlive name={keepName}>
    <StkCommiTplBase {...props} tplId={tplId} />
    // </KeepAlive>
  );
};

export { StkCommiTpl };
