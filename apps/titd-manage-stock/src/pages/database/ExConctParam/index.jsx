import { useRef, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Card, Typography, Button, App } from 'antd';
import { EditOutlined } from '@ant-design/icons';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  HeadTitle,
  forms,
} from '@titd/publics/components';
import { addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  currency,
  errorResp,
} from '@titd/publics/utils';

const { Title, Paragraph, Text } = Typography;
const { ModalForm } = forms;
const { SITE_URL, MESSAGE_TYPE, CURD } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { getFloat } = currency;

const ExConctParamBase = () => {
  const modalForm = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const createAddForm = useMemo(() => addForm($t), [$t]);

  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryExConctParam), params);

    const { data: respData } = resp;
    if (!respData.errcode) {
      return {
        trigDbratio: respData.trigDbratio ? getFloat(respData.trigDbratio * 100) : 0,
        ctrlConcratio: respData.ctrlConcratio ? getFloat(respData.ctrlConcratio * 100) : 0,
        ctrlMaintratio: respData.ctrlMaintratio ? getFloat(respData.ctrlMaintratio * 100) : 0,
        ctrlTranfmratio: respData.ctrlTranfmratio ? getFloat(respData.ctrlTranfmratio * 100) : 0,
        ctrlTranfcratio: respData.ctrlTranfcratio ? getFloat(respData.ctrlTranfcratio * 100) : 0,
      };
    } else {
      message.error(respData.errmessage);
      return {};
    }
  };

  const {
    data: dataSource = {},
    loading,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }

  const toUpdate = () => {
    // console.log('Update', record);
    modalForm.current.show(1, dataSource);
  }

  const addSubmit = async form => {
    const postData = {
      ...defaultParams,
      trigDbratio: form.trigDbratio / 100,
      ctrlConcratio: form.ctrlConcratio / 100,
      ctrlMaintratio: form.ctrlMaintratio / 100,
      ctrlTranfmratio: form.ctrlTranfmratio / 100,
      ctrlTranfcratio: form.ctrlTranfcratio / 100,
    }

    fetchFunc(MESSAGE_TYPE.UpdExConctParam, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const formatValue = val => (
    <Text strong style={{
      fontSize: '22px',
    }}>{val || '-'}</Text>
  );

  return (<>
    <HeadTitle title={$t('app.menu.db.martrade.exconctparam', {
      defaultMsg: '集中度参数',
    })} />

    <Title level={4}>{$t('app.stock.exconctparam.title')}</Title>
    <Card loading={loading}>
      <Text>{$t('app.stock.exconctparam.text')}</Text>
      <Paragraph>
        {$t(
          'app.stock.exconctparam.parag1',
          {
            trigDbratio: formatValue(dataSource.trigDbratio),
            ctrlConcratio: formatValue(dataSource.ctrlConcratio),
            ctrlMaintratio: formatValue(dataSource.ctrlMaintratio),
          },
        )}
      </Paragraph>
      <Paragraph>
        {$t(
          'app.stock.exconctparam.parag2',
          {
            ctrlTranfmratio: formatValue(dataSource.ctrlTranfmratio),
            ctrlTranfcratio: formatValue(dataSource.ctrlTranfcratio),
          },
        )}
      </Paragraph>

      <div style={{
        textAlign: 'center',
      }}>
        <Button
          type="primary"
          icon={<EditOutlined />}
          onClick={toUpdate}
        >{$t('app.general.update')}</Button>
      </div>
    </Card>

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />
  </>);
}

const ExConctParam = props => (
  // <KeepAlive name={keepName}>
  <ExConctParamBase {...props} />
  // </KeepAlive>
);

export { ExConctParam };
