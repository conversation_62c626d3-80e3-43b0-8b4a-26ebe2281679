import { useState, useMemo, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Transfer, Table, Divider, Typography } from 'antd';
import { diff } from 'radash';

import { TableFields } from '@titd/publics/components';
import {
  dicts,
  useFormattedMessage,
} from '@titd/publics/utils';

const { Text } = Typography;
const { TableBadgeDot } = TableFields;
const { exchangeAllDict, gatewayTypeDict, tagNormalColors } = dicts;

const TableTransfer = ({ leftColumns, rightColumns, ...restProps }) => (
  <Transfer {...restProps}>
    {({
      direction,
      filteredItems,
      onItemSelectAll,
      onItemSelect,
      selectedKeys,
    }) => {
      const columns = direction === 'left' ? leftColumns : rightColumns;
      const rowSelection = {
        onSelectAll(selected, selectedRows) {
          const treeSelectedKeys = selectedRows
            .map(({ key }) => key);
          const diffKeys = selected
            ? diff(treeSelectedKeys, selectedKeys)
            : diff(selectedKeys, treeSelectedKeys);
          onItemSelectAll(diffKeys, selected);
        },
        onSelect({ key }, selected) {
          onItemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys,
      };

      return (
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={filteredItems}
          size="small"
          scroll={{ x: 'max-content' }}
          onRow={({ key }) => ({
            onClick: () => {
              onItemSelect(key, !selectedKeys.includes(key));
            },
          })}
        />
      );
    }}
  </Transfer>
);

const GateTransfer = (props, ref) => {
  const {
    dataSource,
    checkedGate,
  } = props;

  const { $t } = useFormattedMessage();

  const [checkedKeys, setCheckedKeys] = useState([]);

  const gateData = useMemo(() => {
    if (dataSource.length > 0) {
      const findData = dataSource.filter(i => i.gatewaytype === 1);
      return findData.map(item => ({
        ...item,
        key: item.gatewayid,
      }));
    }
    return [];
  }, [dataSource]);

  useEffect(() => {
    setCheckedKeys(checkedGate);
  }, [checkedGate]);

  useImperativeHandle(ref, () => ({
    getValues: () => {
      // console.log('values', checkedKeys);
      return {
        addKeys: diff(checkedKeys, checkedGate),
        removeKeys: diff(checkedGate, checkedKeys),
      };
    },
  }));

  const leftColumns = [{
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.gateway.id'),
    dataIndex: 'gatewayid',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.db.gateway.type'),
    dataIndex: 'gatewaytype',
    render: text => gatewayTypeDict[text] || text,
  }];
  const rightColumns = [{
    title: $t('app.options.db.gateway.id'),
    dataIndex: 'gatewayid',
    render: text => <Text strong>{text}</Text>,
  }];

  const filterOption = (inputValue, option) =>
    option.gatewayid?.indexOf(inputValue) > -1;

  return (<>
    <Divider orientation="left" plain>
      {$t('app.options.db.gateway.id')}
    </Divider>

    <div style={{ overflowX: 'auto' }}>
      <TableTransfer
        showSearch
        titles={[
          <Text type="secondary" key="0">{$t('app.options.db.userinfo.right.none')}</Text>,
          <Text type="secondary" key="1">{$t('app.options.db.userinfo.right.have')}</Text>,
        ]}
        dataSource={gateData}
        filterOption={filterOption}
        targetKeys={checkedKeys}
        onChange={nextTargetKeys => setCheckedKeys(nextTargetKeys)}
        leftColumns={leftColumns}
        rightColumns={rightColumns}
      />
    </div>
  </>);
}

export default forwardRef(GateTransfer);
