import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, Space, Tag, List, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm, allotForm, userAllotBtn } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  formOptions,
  errorResp,
} from '@titd/publics/utils';

import UserTransfer from './Transfer';
import GateTransfer from './GateTransfer';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, TableTag } = TableFields;
const { ModalForm, DrawerForm, Confirm } = forms;
const { updateLink, deleteLink, detailLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeDict, exchangeAllDict, gatewayTypeDict, yesNoDict, tagNormalColors, yesNoColor } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserList } = getList;
const { checkOptions } = formOptions;

const defaultGatewaytype = 1;
const maxLen = 3;

const GatewayBase = () => {
  const queryForm = useRef(null);
  const modalForm = useRef(null);
  const userRef = useRef(null);
  const aModalForm = useRef();
  const gateRef = useRef();
  const drawerForm = useRef();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [selectRecord, setSelectRecord] = useState({});
  const [filteredInfo, setFilteredInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();
  const defaultSearch = {
    ...defaultParams,
    ispreorder: -1,
  };

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    filters: checkOptions(exchangeDict),
    filteredValue: filteredInfo.exchangeid || null,
    onFilter: (value, record) => record.exchangeid === value,
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.gateway.id'),
    dataIndex: 'gatewayid',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.db.gateway.type'),
    dataIndex: 'gatewaytype',
    render: text => gatewayTypeDict[text] || text,
  }, {
    title: $t('app.options.db.gateway.ispreorder'),
    dataIndex: 'ispreorder',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'useridlist',
    render: (text, record) => formatUser(text, record.searchUser),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION + 70,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
        (record.gatewaytype === 1 && record.useridlist?.length > maxLen) && detailLink(toDetail, record),
      ]} />
    )
  }];

  const userGroup = (searchId, group) => {
    return group.map((item, idx) => {
      const userItem = userOptions?.find(i => i.value === item);

      return (<Tag color={searchId === item ? 'orange' : ''} key={idx}>{userItem?.label || item}</Tag>);
    });
  }
  const formatUser = (text, userId) => {
    const len = text?.length;

    if(len > 0) {
      let userArr;
      if(len > maxLen) {
        userArr = userGroup(userId, text.slice(0, maxLen));
        userArr.push(
          <Tag
            key={len}
            style={{
              background: '#fff',
              borderStyle: 'dashed',
            }}
          >+ {len - maxLen}</Tag>
        );
      } else {
        userArr = userGroup(userId, text);
      }

      return (<Space size={[0, 16]} wrap>{userArr}</Space>);
    }
    return null;
  };

  const [userOptions, setUserOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);
  const [oldRecord, setOldRecord] = useState({});
  const [noUser, setNoUser] = useState(false);
  const [checkedGate, setCheckedGate] = useState();

  const createSearchForm = useMemo(() => searchForm($t, userOptions), [$t, userOptions]);
  const createAddForm = useMemo(() => addForm($t, isModify), [$t, isModify]);
  const createAllotForm = useMemo(() => allotForm($t, userOptions), [$t, userOptions]);

  useMount(() => {
    getUserList(Request.post, defaultParams, setUserOptions);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryGateway), params);

    const { data: respData } = resp;
    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    setFilteredInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        searchUser: params.userid,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultSearch],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  };
  const onSearch = values => {
    if (values.ispreorder === undefined) {
      delete values.ispreorder;
    }

    run({
      ...defaultSearch,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultSearch);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setNoUser(!defaultGatewaytype);
    setOldRecord(null);
    modalForm.current.show();
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    setIsModify(true);
    setNoUser(record.gatewaytype !== 1);
    setOldRecord(record);
    modalForm.current.show(1, record);
  }
  const deleteFunc = async record => {
    const deleteData = {
      ...defaultParams,
      gatewayid: record.gatewayid,
    }

    fetchFunc(MESSAGE_TYPE.DelGateway, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      refresh();
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => i.gatewayid);
    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record);
        });

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.gatewayid,
      onOk: () => {
        // console.log('ok', record);
        deleteFunc(record);

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }
  const toAllot = () => {
    // console.log('toAllot');
    onReset();
    setCheckedGate(null);
    aModalForm.current.show(1);
  }
  const toDetail = record => {
    // console.log('Detail', record);
    setSelectRecord(record);
    drawerForm.current.show(`${exchangeDict[record.exchangeid]} - ${record.gatewayid}`, record);
  }

  const formValueChange = changedValues => {
    if ('gatewaytype' in changedValues) {
      const value = changedValues.gatewaytype;

      // 有值则清空品种
      if (value === 1) {
        setNoUser(false);
      } else {
        setNoUser(true);
      }
    }
  }
  const aFormValueChange = changedValues => {
    if ('userid' in changedValues) {
      const value = changedValues.userid;

      if (value) {
        fetchFunc(MESSAGE_TYPE.QryGateway, {
          ...defaultParams,
          ispreorder: -1,
          userid: value,
        }, resp => {
          if (resp?.length > 0) {
            const gateArr = resp.map(item => item.gatewayid);
            setCheckedGate(gateArr);
          } else {
            setCheckedGate([]);
          }
        });
      }
    }
  }

  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    if (!noUser) {
      // 最后一步权限保存，统一处理
      const newUsers = userRef.current?.getValues();
      postData.useridlist = newUsers;
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdGateway
      : MESSAGE_TYPE.InsGateway, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const allotSubmit = async form => {
    const gateMaps = gateRef.current?.getValues();
    const userId = form.userid;

    // console.log(gateMaps, userId);
    let addResults = [], removeResults = [];
    // 增加
    if (gateMaps.addKeys?.length > 0) {
      addResults = await Promise.all(gateMaps.addKeys.map(async item => {
        const findGate = dataSource.find(i => i.gatewayid === item);
        if (findGate) {
          const userList = findGate.useridlist ? [...findGate.useridlist, userId] : [userId];
          const postData = {
            ...defaultParams,
            exchangeid: findGate.exchangeid,
            gatewayid: findGate.gatewayid,
            gatewaytype: findGate.gatewaytype,
            ispreorder: findGate.ispreorder,
            useridlist: userList,
          }

          const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.UpdGateway), postData);
          const { data: respData } = resp;
          if (respData.errcode !== 200) {
            return item + ': ' + respData.errmessage;
          }
          return null;
        }
      }));
    }

    // 减少
    if (gateMaps.removeKeys?.length > 0) {
      removeResults = await Promise.all(gateMaps.removeKeys.map(async item => {
        const findGate = dataSource.find(i => i.gatewayid === item);
        if (findGate) {
          const userList = findGate.useridlist?.filter(i => i !== userId);
          const postData = {
            ...defaultParams,
            exchangeid: findGate.exchangeid,
            gatewayid: findGate.gatewayid,
            gatewaytype: findGate.gatewaytype,
            ispreorder: findGate.ispreorder,
            useridlist: userList,
          }

          const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.UpdGateway), postData);
          const { data: respData } = resp;
          if (respData.errcode !== 200) {
            return item + ': ' + respData.errmessage;
          }
          return null;
        }
      }));
    }

    const results = [...addResults, ...removeResults].filter(i => i);
    if (results.length > 0) {
      message.error(results);
    } else {
      message.success(CURD.Update + CURD.StatusOkMsg);
    }
    onRefresh();
  }

  const tableChange = (_, filters) => {
    // console.log('Various parameters', pagination, filters, sorter);
    setFilteredInfo(filters);
  }

  const createInstrList = record => {
    const userList = record.useridlist?.map(item => {
      const userItem = userOptions?.find(i => i.value === item);
      return userItem?.label || item;
    });

    return (
      <List
        size="small"
        dataSource={userList}
        renderItem={item => <List.Item>{item}</List.Item>}
      />
    );
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.gateway', {
        defaultMsg: '网关管理',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
          userAllotBtn(toAllot),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      initValues={{
        gatewaytype: defaultGatewaytype,
        ispreorder: 1,
      }}
      onValuesChange={formValueChange}
    >
      {!noUser && <UserTransfer
        ref={userRef}
        userOptions={userOptions}
        record={oldRecord}
      />}
    </ModalForm>

    <ModalForm
      formGroup
      ref={aModalForm}
      onOk={allotSubmit}
      formData={createAllotForm}
      onValuesChange={aFormValueChange}
    >
      {checkedGate && <GateTransfer
        ref={gateRef}
        dataSource={dataSource}
        checkedGate={checkedGate}
      />}
    </ModalForm>

    <DrawerForm
      width={300}
      ref={drawerForm}
      formData={[]}
    >{selectRecord.useridlist?.length > 0 && createInstrList(selectRecord)}</DrawerForm>
  </>);
}

// const GatewayAlive = ({ keepName, ...props }) => (
const Gateway = props => (
  // <KeepAlive name={keepName}>
  <GatewayBase {...props} />
  // </KeepAlive>
);

export { Gateway };
