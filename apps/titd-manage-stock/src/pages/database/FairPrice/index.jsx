import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn, TableBadgeDot, NegaNumber } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;

const FairPriceBase = () => {
  const modalForm = useRef();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'str1',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'intvolume1',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.stock.instr.fairprice'),
    dataIndex: 'price1',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  // }, {
  //   title: intl.formatMessage({ id: 'app.stock.instr.price' }),
  //   dataIndex: 'price',
  //   align: 'right',
  //   render: text => NegaNumber(text, 2, 2),
  // }, {
  //   title: intl.formatMessage({ id: 'app.stock.instr.lastprice' }),
  //   dataIndex: 'lastprice',
  //   align: 'right',
  //   render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.instr.fairratio'),
    dataIndex: 'price2',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const [isModify, setIsModify] = useState(false);

  const createSearchForm = useMemo(() => searchForm($t), [$t]);
  const createAddForm = useMemo(() => addForm($t, isModify), [$t, isModify]);

  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryFairPrice), params);

    const { data: respData } = resp;

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        limitmin: item.limitmin >= 10000 ? item.limitmin : Math.round(item.limitmin * 100),
        limitmax: item.limitmax >= 10000 ? item.limitmax : Math.round(item.limitmax * 100),
        singleratio: Math.round(item.singleratio * 100),
        groupratio: Math.round(item.groupratio * 100),
      }));
      return tableData;
    } else {
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    modalForm.current.show(0);
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    setIsModify(true);
    modalForm.current.show(1, record);
  }
  const deleteFunc = async record => {
    const deleteData = {
      ...defaultParams,
      str1: record.str1,
      intvolume1: record.intvolume1,
    }

    fetchFunc(MESSAGE_TYPE.DelFairPrice, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => i.str1);
    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach(record => {
          deleteFunc(record);
        });
      }
    });
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.str1,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }

  const addSubmit = async (form, type) => {
    let postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdFairPrice
      : MESSAGE_TYPE.InsFairPrice, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.martrade.fairprice', {
        defaultMsg: '证券公允价',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />
  </>);
}

const FairPrice = props => (
  // <KeepAlive name={keepName}>
  <FairPriceBase {...props} />
  // </KeepAlive>
);

export { FairPrice };
