import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = $t => [{
  valueType: 'input',
  name: 'str1',
  label: $t('app.options.instrumentid'),
}];

export const addForm = ($t, isModify) => [{
  valueType: 'input',
  name: 'str1',
  label: $t('app.options.instrumentid'),
  rules: [{ required: true }],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'select',
  name: 'intvolume1',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'number',
  name: 'price1',
  label: $t('app.stock.instr.fairprice'),
  fieldProps: {
    precision: 2,
    step: 0.01,
  }
}, {
  valueType: 'number',
  name: 'price2',
  label: $t('app.stock.instr.fairratio'),
  fieldProps: {
    precision: 2,
    step: 0.01,
  }
}];
