import { useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Card, Spin, Space, App } from 'antd';
import { useRequest, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  TableSimple,
  TableFields,
  ActionBtns,
  forms,
  btns,
} from '@titd/publics/components';
import { amountForm } from './data';

import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
  currency,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn, TableTag, NegaNumber, TableBadgeDot } = TableFields;
const { SimpleForm } = forms;
const { transferBtn } = btns;
const { transferStatusDict, exchangeDict, exchangeAllDict, creditTypeDict, shareDirectDict, transferStatusColor, tagNormalColors, yesNoColor } = dicts;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { digitUppercase, formatDot } = currency;

const Transfer = props => {
  const {
    params,
    record,
    onFinish,
  } = props;

  const aForm = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [digitUpper, setDigitUpper] = useState('');

  const [oldAccountid, setOldAccountid] = useState();
  const [respData, setRespData] = useState([]);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const respColumns = [{
    dataIndex: 'transferno',
    title: $t('app.stock.account.transferno'),
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    dataIndex: 'accountid',
    title: $t('app.options.accountid'),
  }, {
    dataIndex: 'updtype',
    title: $t('app.stock.credit.type'),
    render: text => creditTypeDict[text] || text,
  }, {
    dataIndex: 'direct',
    title: $t('app.stock.account.direct'),
    render: text => TableTag(text, shareDirectDict, yesNoColor),
  }, {
    dataIndex: 'destsvrid',
    title: $t('app.stock.account.destsvrid'),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangearr',
    render: text => text && (<Space>{text.map(item => TableBadgeDot(item, exchangeAllDict, tagNormalColors, true))}</Space>),
  }, {
    dataIndex: 'amount',
    title: $t('app.options.mem.account.amount'),
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    dataIndex: 'status',
    title: $t('app.stock.account.status'),
    render: text => TableTag(text + 1, transferStatusDict, transferStatusColor),
  }, {
    dataIndex: 'responsestr',
    title: $t('app.stock.account.logmessage'),
  }];

  const [destList, setDestList] = useState(exchangeDict);

  const createAmountForm = useMemo(() => amountForm($t, digitUpper, destList), [$t, digitUpper, destList]);

  useUnmount(() => cancel());

  useEffect(() => {
    if (record) {
      if (record.accountid !== oldAccountid) {
        setOldAccountid(record.accountid);
        setRespData([]);
      }

      transferReset();

      let newRecord = {
        ...record
      };

      aForm.current.set(newRecord);
    }
  }, [record]); // eslint-disable-line react-hooks/exhaustive-deps

  const transferReset = () => {
    aForm.current.reset();
    setDigitUpper('');
  }

  const formValueChange = (changedValues, values) => {
    if ('updtype' in changedValues) {
      const type = changedValues.updtype;

      if (type === 3) {
        aForm.current.set({
          availcredit: formatDot(record.availcreditRq, 2),
          deposit: record.depositRq,
          withdraw: record.withdrawRq,
        });
      } else if (type === 4) {
        aForm.current.set({
          availcredit: formatDot(record.availcreditRz, 2),
          deposit: record.depositRz,
          withdraw: record.withdrawRz,
        });
      } else {
        aForm.current.set({
          availcredit: undefined,
          deposit: undefined,
          withdraw: undefined,
        });
      }
    }

    if ('sourceexchangeid' in changedValues) {
      const sourceValue = changedValues.sourceexchangeid;

      if (sourceValue) {
        // 出金列表
        const copyList = exchangeDict.map((item, idx) => {
          if (idx === sourceValue) {
            return '';
          }
          return item;
        });

        setDestList(copyList);

        // 重复清空
        const destValue = values.destclusterid;
        if (destValue && sourceValue === destValue) {
          aForm.current.set({
            destclusterid: undefined
          });
        }
      } else {
        setDestList(exchangeDict);
      }
    }
    if ('amount' in changedValues) {
      const value = changedValues.amount;
      if (value !== '') {
        setDigitUpper(digitUppercase(value));
      } else {
        setDigitUpper('');
      }
    }
  }

  const amountSubmit = () => {
    aForm.current.submit(values => {
      // console.log(values);
      const postData = {
        ...params,
        accountid: values.accountid,
        updtype: values.updtype,
        sourceexchangeid: values.sourceexchangeid,
        destexchangeid: values.destexchangeid,
        amount: values.amount,
      }

      fetchFunc(MESSAGE_TYPE.UpdCreditTrans, postData, resp => {
        // message.success(CURD.StatusOkMsg);
        // onFinish();
        if (resp.errcode) {
          message.error(resp.errmessage);
        } else {
          if (resp?.length > 0) {
            const tableData = resp.map((item, idx) => {
              const direct = item.amount < 0 ? 0 : 1;

              return {
                ...item,
                id: idx + 1,
                direct: direct,
                exchangearr: exchangeList[item.destsvrid],
              }
            });
            setRespData(tableData);
          } else {
            setRespData([]);
          }
          onFinish();
        }
      });
    });
  }

  const { loading, run, cancel } = useRequest(amountSubmit, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  return (<>
    <Spin spinning={loading} tip="划转中……">
      <Card className="ti-table-content">
        <SimpleForm
          ref={aForm}
          formGroup
          formData={createAmountForm}
          onValuesChange={formValueChange}
          formStyle={{ maxWidth: '850px' }}
          customBtn={
            <ActionBtns btns={[
              transferBtn(run, 'adjust'),
            ]} />
          }
        />
      </Card>
    </Spin>

    {respData.length > 0 && (
      <Card className="ti-table-content">
        <TableSimple
          columns={respColumns}
          dataSource={respData}
          isLoading={loading}
        />
      </Card>
    )}
  </>);
}

export default Transfer;
