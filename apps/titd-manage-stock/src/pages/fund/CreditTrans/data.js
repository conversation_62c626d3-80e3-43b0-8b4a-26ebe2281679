import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict, creditTypeMap } = dicts;
const { selectOptions, selectMapOptions } = formOptions;

export const searchForm = ($t, accountOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions,
  }
}];

export const amountForm = ($t, digitUpper, destList) => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.options.accountid'),
  span: 1,
}, {
  valueType: 'select',
  name: 'updtype',
  label: $t('app.stock.credit.type'),
  rules: [{ required: true }],
  fieldProps: {
    options: selectMapOptions(creditTypeMap, [1]),
  }
}, {
  valueType: 'plain',
  name: 'availcredit',
  label: $t('app.stock.credit.availcredit'),
  fieldProps: {
    style: {
      fontSize: '20px',
      lineHeight: '20px',
      color: '#0081cc',
    }
  }
}, {
  valueType: 'plain',
  name: 'deposit',
  label: $t('app.stock.common.deposit') + $t('app.stock.common.account'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#52c41a',
    }
  }
}, {
  valueType: 'plain',
  name: 'withdraw',
  label: $t('app.stock.common.withdraw') + $t('app.stock.common.account'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#ff4d4f',
    }
  }
}, {
  valueType: 'select',
  name: 'sourceexchangeid',
  label: $t('app.stock.common.withdraw'),
  rules: [{ required: true }],
  fieldProps: {
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'select',
  name: 'destexchangeid',
  label: $t('app.stock.common.deposit'),
  rules: [{ required: true }],
  fieldProps: {
    options: selectOptions(destList),
  }
}, {
  valueType: 'number',
  name: 'amount',
  label: $t('app.options.mem.account.amount'),
  span: 1,
  extra: digitUpper,
  rules: [{ required: true }],
  fieldProps: {
    min: 0.01,
    precision: 2,
  }
}];
