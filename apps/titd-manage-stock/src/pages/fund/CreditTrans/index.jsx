import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Space, Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

import Transfer from './Transfer';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableBadgeDot } = TableFields;
const { exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList } = getList;

const defaultUpdType = 1;

const CreditTransBase = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([1]);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => text ? (<Text strong>{text}</Text>) : (<Text type="secondary">
      {$t('app.general.all') + $t('app.options.accountid')}
    </Text>),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchanges',
    render: text => <Space>{text?.map(item => TableBadgeDot(item, exchangeAllDict, tagNormalColors, true))}</Space>,
  }, {
    title: $t('app.stock.credit.rz'),
    children: [{
      title: $t('app.stock.credit.precredit'),
      dataIndex: 'precreditRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.availcredit'),
      dataIndex: 'availcreditRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.usedcredit'),
      dataIndex: 'usedcreditRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.frzcredit'),
      dataIndex: 'frzcreditRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.common.deposit') + $t('app.stock.common.account'),
      dataIndex: 'depositRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.common.withdraw') + $t('app.stock.common.account'),
      dataIndex: 'withdrawRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }]
  }, {
    title: $t('app.stock.credit.rq'),
    children: [{
      title: $t('app.stock.credit.precredit'),
      dataIndex: 'precreditRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.availcredit'),
      dataIndex: 'availcreditRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.usedcredit'),
      dataIndex: 'usedcreditRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.frzcredit'),
      dataIndex: 'frzcreditRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.common.deposit') + $t('app.stock.common.account'),
      dataIndex: 'depositRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.common.withdraw') + $t('app.stock.common.account'),
      dataIndex: 'withdrawRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }]
  }];

  const [accountOptions, setAccountOptions] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, accountOptions), [$t, accountOptions]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountOptions);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    if (!params) {
      setSelectKeys([]);
      return [];
    };

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAllCredit), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
        exchanges: exchangeList[item.svrid],
      }));

      // setSelectKeys([1]);
      return tableData;
    } else {
      setSelectKeys([]);
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading: isLoading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const selectRecord = useMemo(() => {
    if (selectKeys.length > 0 && dataSource.length > 0) {
      return dataSource.find(i => i.id === selectKeys[0]);
    }

    return null;
  }, [selectKeys, dataSource]);

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
      updtype: defaultUpdType,
    });
  }
  const onReset = () => {
    run(null);
  }

  return (<>
    <PageContent
      title={$t('app.menu.fund.credittrans', {
        defaultMsg: '额度划拨',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        rowClickable: true,
        selectionType: 'radio',
        selectKeys: selectKeys,
        setSelectKeys: keys => {
          setSelectKeys(keys);
        },
      }}
    />

    {/* 资金划转 */}
    {selectRecord && (
      <Transfer
        params={defaultParams}
        record={selectRecord}
        onFinish={onRefresh}
      />
    )}
  </>);
}

const CreditTrans = props => (
  // <KeepAlive name={keepName} key={keepName}>
  <CreditTransBase {...props} />
  // </KeepAlive>
);

export { CreditTrans };
