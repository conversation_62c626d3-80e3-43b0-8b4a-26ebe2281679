import { dicts, formOptions } from '@titd/publics/utils';

const { transferStatusDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, accountOptions, isStock = false) => {
  const addForm = isStock ? [{
    valueType: 'input',
    name: 'instrumentid',
    label: $t('app.options.instrumentid'),
  }, {
    valueType: 'number',
    name: 'volume',
    label: $t('app.stock.common.number'),
  }] : [];

  return [{
  //   valueType: 'select',
  //   name: 'updtype',
  //   label: intl.formatMessage({ id: 'app.stock.credit.type' }),
  //   rules: [{ required: true }],
  //   fieldProps: {
  //     options: selectMapOptions(creditTypeMap),
  //   }
  // }, {
    valueType: 'autocomplete',
    name: 'accountid',
    label: $t('app.options.accountid'),
    fieldProps: {
      options: accountOptions,
    }
  }, ...addForm, {
    valueType: 'picker',
    name: 'tradeday',
    label: $t('app.stock.account.tradeday'),
    rules: [{ required: true }],
  }, {
    valueType: 'input',
    name: 'transferno',
    label: $t('app.stock.account.transferno'),
  }, {
    valueType: 'select',
    name: 'status',
    label: $t('app.stock.account.status'),
    fieldProps: {
      options: selectOptions(transferStatusDict),
    }
  }];
};
