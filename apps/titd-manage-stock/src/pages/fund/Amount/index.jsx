import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

import Transfer from './Transfer';

const { Text } = Typography;
const { IndexColumn, NegaNumber } = TableFields;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList } = getList;

const AmountBase = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([1]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clusterid'),
    dataIndex: 'clusterid',
    fixed: 'left',
  }, {
    title: $t('app.options.mem.account.prebalance'),
    dataIndex: 'prebalance',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.distribfund'),
    dataIndex: 'distribfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.availusedfund'),
    dataIndex: 'availusedfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.commi'),
    dataIndex: 'commi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.closeprofit'),
    dataIndex: 'closeprofit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.posiprofit'),
    dataIndex: 'posiprofit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.trade.premium'),
    dataIndex: 'premium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.deposit'),
    dataIndex: 'deposit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.withdraw'),
    dataIndex: 'withdraw',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.trade.frozenpremium'),
    dataIndex: 'frozenpremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'frozencommi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.entryfees'),
    dataIndex: 'entryfees',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }];

  const [accountOptions, setAccountOptions] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, accountOptions), [$t, accountOptions]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountOptions);
  });
  useUnmount(() => cancel());


  const getTableData = async params => {
    if (!params) return [];
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAllAccount), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
      }));

      // setSelectKeys([1]);
      return tableData;
    } else {
      setSelectKeys([]);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const selectRecord = useMemo(() => {
    if (selectKeys.length > 0 && dataSource.length > 0) {
      return dataSource.find(i => i.id === selectKeys[0]);
    }

    return null;
  }, [selectKeys, dataSource]);

  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(null);
  }

  return (<>
    <PageContent
      title={$t('app.menu.fund.amount', {
        defaultMsg: '资金划转',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: refresh,
        rowClickable: true,
        selectionType: 'radio',
        selectKeys: selectKeys,
        setSelectKeys: (keys) => {
          setSelectKeys(keys);
        },
      }}
    />

    {/* 资金划转 */}
    {selectRecord && (
      <Transfer
        params={defaultParams}
        record={selectRecord}
        onFinish={refresh}
      />
    )}
  </>);
}

const Amount = props => (
  // <KeepAlive name={keepName} key={keepName}>
  <AmountBase {...props} />
  // </KeepAlive>
);

export { Amount };
