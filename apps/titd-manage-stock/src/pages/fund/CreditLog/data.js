import { dicts, formOptions } from '@titd/publics/utils';

const { transferStatusDict, creditTypeMap } = dicts;
const { selectOptions, selectMapOptions } = formOptions;

export const searchForm = ($t, accountOptions) => [{
  valueType: 'select',
  name: 'updtype',
  label: $t('app.stock.credit.type'),
  rules: [{ required: true }],
  fieldProps: {
    options: selectMapOptions(creditTypeMap, [1]),
  }
}, {
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions,
  }
}, {
  valueType: 'picker',
  name: 'tradeday',
  rules: [{ required: true }],
  label: $t('app.stock.account.tradeday'),
}, {
  valueType: 'input',
  name: 'transferno',
  label: $t('app.stock.account.transferno'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.stock.account.status'),
  fieldProps: {
    options: selectOptions(transferStatusDict),
  }
}];
