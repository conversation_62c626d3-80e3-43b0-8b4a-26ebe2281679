import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { App } from 'antd';
// import KeepAlive from 'react-activation';
import dayjs from 'dayjs';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  links,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  dateFormat,
  expandAllKeys,
  errorResp,
} from '@titd/publics/utils';

import { useStyles } from '../style';

const { IndexColumn, NegaNumber, TableTag } = TableFields;
const { continueLink } = links;
const { transferStatusDict, transferDirectDict, seatDict, transferStatusColor, yesNoColor } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList, getClusterList } = getList;
const { formatDate } = dateFormat;

const TransLogBase = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();
  const { styles } = useStyles();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);
  const [expandedKeys, setExpandedKeys] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();
  const searchParams = useMemo(() => ({
    ...defaultParams,
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    tradeday: Number(dayjs().format('YYYYMMDD')),
    status: 100,
  }), [limit, defaultParams]);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'idno',
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    dataIndex: 'transferno',
    title: $t('app.stock.account.transferno'),
    width: TABLE_WIDTH.ID + 10,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    dataIndex: 'direct',
    title: $t('app.stock.account.direct'),
    render: text => TableTag(text, transferDirectDict, yesNoColor),
  }, {
    dataIndex: 'destsvrid',
    title: $t('app.stock.account.destsvrid'),
    render: text => seatDict[text] || serverMap[text] || text,
  }, {
    dataIndex: 'accountid',
    title: $t('app.options.accountid'),
  }, {
    dataIndex: 'amount',
    title: $t('app.options.mem.account.amount'),
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    dataIndex: 'status',
    title: $t('app.stock.account.status'),
    render: text => TableTag(text + 1, transferStatusDict, transferStatusColor),
  }, {
    dataIndex: 'tradeday',
    title: $t('app.stock.account.tradeday'),
    render: text => formatDate(text),
  }, {
    dataIndex: 'logtime',
    title: $t('app.stock.account.logtime'),
  }, {
    dataIndex: 'logmessage',
    title: $t('app.stock.account.logmessage'),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 100,
    render: (_, record) => record.changeStatus && (
      <ActionLinks links={[
        continueLink(toContinue, record),
      ]} />
    ),
  }];

  const [accountOptions, setAccountOptions] = useState([]);
  const [, setClusterList] = useState([]);
  const [serverMap, setServerMap] = useState({});

  const createSearchForm = useMemo(() => searchForm($t, accountOptions), [$t, accountOptions]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountOptions);

    // 获取集群、服务器信息
    getClusterList(Request.post, defaultParams, setClusterList, navigate, setServerMap);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUpdAccount), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map((respItem, index) => respItem.map((item, idx) => {
        const direct = item.amount < 0 ? 0 : 1;
        const newItem = {
          ...item,
          id: item.transferno + '_' + idx + 1,
          direct: direct,
        };

        // 判断是否继续划转：有出金且无失败，入金有失败
        if (direct && item.status === -1) {
          const outNum = respItem.filter(i => i.amount < 0);
          if (outNum.length > 0) {
            const outError = outNum.filter(i => i.status === -1);
            const inError = respItem.filter(i => i.amount > 0 && i.status === -1);

            if (outError.length === 0 && inError.length > 0) {
              newItem.changeStatus = 1;
            }
          }
        }

        // 第一项添加序号
        if (idx === 0) {
          newItem.idno = item.rspseqno;
          index > 0 && (newItem.rowClassName = 'ti-tr-top-border');
        }

        // 最后一项添加样式
        // if (idx === respItem.length - 1) {
        //   newItem.rowClassName = 'ti-tr-border';
        // }

        return newItem;
      }));

      const flatTableData = tableData.map(item => {
        const itemData = item[0];
        if (item.length > 1) {
          itemData.children = item.splice(1);
          return itemData;
        }

        return itemData;
      });

      // 设置默认展开
      const allKeys = expandAllKeys(flatTableData);
      setExpandedKeys(allKeys);

      setTotal(respData[0][0].rsptotnum);
      return flatTableData;
    } else {
      setExpandedKeys([]);
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [searchParams],
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onSearch = values => {
    run({
      ...searchParams,
      ...objFilter(values),
      status: values.status !== undefined ? values.status - 1 : 100,
      tradeday: values?.tradeday ? Number(dayjs(values.tradeday).format('YYYYMMDD')) : 0,
    });
  }
  const onReset = () => {
    run(searchParams);
  }

  function onExpand(expanded, record) {
    // console.log('expand:', expanded, record);
    if (expanded) {
      expandedKeys.push(record.id);
    } else {
      const idx = expandedKeys.indexOf(record.id);
      if (idx > -1) {
        expandedKeys.splice(idx, 1);
      }
    }
    setExpandedKeys([...expandedKeys]);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        // setDataSource([]);
      }

      setLimit(size);
    }

    run({
      ...searchParams,
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  const toContinue = record => {
    // console.log('Continue', record);
    const postData = {
      ...defaultParams,
      transferno: record.transferno,
      tradeday: record.tradeday,
    }

    fetchFunc(MESSAGE_TYPE.CntUpdAccount, postData, resp => {
      const respMsg = resp.map(item => {
        const dest = seatDict[item.destsvrid] || serverMap[item.destsvrid] || item.destsvrid;
        const result = TableTag(item.status + 1, transferStatusDict, transferStatusColor);

        return (<>
          <span>{dest + ' '}</span>
          {result}
        </>);
      });

      message.info(respMsg);
      refresh();
    });
  }

  return (
    <PageContent
      title={$t('app.menu.fund.translog', {
        defaultMsg: '划转记录',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
        initValues: {
          tradeday: dayjs(),
        }
      }}
      tableProps={{
        className: styles.doubleTable,
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        checkable: false,
        expandable: {
          expandedRowKeys: expandedKeys,
          onExpand: onExpand
        },
        onRefresh: refresh,
        page: page,
        total: total,
        pageChange: pageChange,
        // scroll: dataSource.length > 15 ? { x: 'max-content', y: 600 } : undefined,
      }}
    />
  );
}

const TransLog = props => (
  // <KeepAlive name={keepName} key={keepName}>
  <TransLogBase {...props} />
  // </KeepAlive>
);

export { TransLog };
