import { dicts, formOptions, tools } from '@titd/publics/utils';

const { transferStatusDict } = dicts;
const { selectOptions } = formOptions;
const { autoFilterOption } = tools;

export const searchForm = ($t, clientOptions) => [{
  valueType: 'autocomplete',
  name: 'clientid',
  label: $t('app.options.clientid'),
  rules: [{ required: true }],
  fieldProps: {
    options: clientOptions,
    filterOption: autoFilterOption,
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'picker',
  name: 'tradeday',
  label: $t('app.stock.account.tradeday'),
  rules: [{ required: true }],
}, {
  valueType: 'input',
  name: 'transferno',
  label: $t('app.stock.account.transferno'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.stock.account.status'),
  fieldProps: {
    options: selectOptions(transferStatusDict),
  }
}];
