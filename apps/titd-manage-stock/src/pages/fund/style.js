import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css, prefixCls }) => ({
  tiTableContent: css`
    margin-top: 16px;
    .${prefixCls}-table-pagination.${prefixCls}-pagination {
      margin-bottom: 0;
    }
  `,
  doubleTable: css`
    table {
      border-collapse: collapse;
    }

    .ti-tr-border:not(:last-child) {
      border-bottom: 3px double #ddd;
    }

    .ti-tr-top-border:not(:first-child) {
      border-top: 3px double #ddd;
    }
  `,
}));
