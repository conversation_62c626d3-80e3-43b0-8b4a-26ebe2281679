import { useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Card, Spin, App } from 'antd';
import { useRequest,useMount, useUnmount } from 'ahooks';

import {
  TableSimple,
  TableFields,
  ActionBtns,
  forms,
  btns,
} from '@titd/publics/components';
import { shareForm } from './data';

import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
  getList,
  errorResp,
} from '@titd/publics/utils';

import { useStyles } from '../style';

const { IndexColumn, TableTag } = TableFields;
const { SimpleForm } = forms;
const { shareBtn } = btns;
const { seatDict, exchangeDict, transferStatusDict, transferStatusColor } = dicts;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { getClusterList } = getList;

const Transfer = props => {
  const {
    params,
    record,
    custom,
    onFinish,
  } = props;

  const aForm = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const { styles } = useStyles();

  const [serverMap, setServerMap] = useState({});
  const [oldInstrumentid, setOldInstrumentid] = useState();
  const [respData, setRespData] = useState([]);

  const fetchFunc = async (type, params, func) => {
    try {
      const resp = await Request.post(SITE_URL.EXTEND(type), params);

      if (resp.status === 200) {
        func && func(resp.data);
      }
    } catch (error) {
      errorResp(error.response.data, navigate, message);
    }
  }

  const respColumns = [{
    dataIndex: 'transferno',
    title: $t('app.stock.account.transferno'),
    width: TABLE_WIDTH.ID + 10,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    dataIndex: 'sourceclusterid',
    title: $t('app.stock.share.source'),
    render: text => seatDict[text] || serverMap[text] || text,
  }, {
    dataIndex: 'destclusterid',
    title: $t('app.stock.share.dest'),
    render: text => seatDict[text] || serverMap[text] || text,
  }, {
  //   dataIndex: 'amount',
  //   title: $t('app.options.mem.account.amount'),
  //   align: 'right',
  //   render: text => NegaNumber(text),
  // }, {
    dataIndex: 'status',
    title: $t('app.stock.account.status'),
    render: text => TableTag(text + 1, transferStatusDict, transferStatusColor),
  }, {
    dataIndex: 'message',
    title: $t('app.stock.account.logmessage'),
  }];

  const [isModify, setIsModify] = useState(true);

  const createShareForm = useMemo(() => shareForm($t, isModify), [$t, isModify]);

  useMount(() => {
    getClusterList(Request.post, params, null, navigate, setServerMap);
  });
  useUnmount(() => cancel());

  useEffect(() => {
    if (record) {
      if (record.instrumentid !== oldInstrumentid) {
        setOldInstrumentid(record.instrumentid);
        setRespData([]);

        const newRecord = {
          ...record,
          exchangename: exchangeDict[record.exchangeid] || record.exchangeid,
          trans_flag: 1,
          position: undefined,
        }

        aForm.current.set(newRecord);
        setIsModify(true);
      }
    } else {
      aForm.current.reset();
    }
  }, [record]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (custom) {
      setIsModify(false);
    }
  },[custom]);

  const formValueChange = changedValues => {
    if ('trans_flag' in changedValues) {
      const value = changedValues.trans_flag;

      if (value) {
        aForm.current.set({
          instrumentid: record.instrumentid,
          // lpositionuse: record.lpositionuse,
        });
        setIsModify(true);
      } else {
        // aForm.current.set({
        //   lpositionuse: '-'
        // });
        setIsModify(false);
      }
    }
  }

  const shareSubmit = () => {
    aForm.current.submit(values => {
      // console.log(values);
      delete values.exchangename;
      // delete values.lpositionuse;

      const postData = {
        ...params,
        ...values,
        trans_flag: String(values.trans_flag + 1),
      }

      fetchFunc(MESSAGE_TYPE.InsSharePosition, postData, resp => {
        // message.success(CURD.StatusOkMsg);
        // onFinish();
        if (resp?.length > 0) {
          const tableData = resp.map((item, idx) => ({
            ...item,
            id: idx + 1,
            sourceclusterid: item.volume < 0 ? item.serverid : '',
            destclusterid: item.volume > 0 ? item.serverid : '',
          }));
          setRespData(tableData);
        } else {
          setRespData([]);
        }
        onFinish();
      });
    });
  }

  const { loading, run, cancel } = useRequest(shareSubmit, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  return (<>
    <Spin spinning={loading} tip="划转中……">
      <Card className={styles.tiTableContent}>
        <SimpleForm
          ref={aForm}
          formGroup
          formData={createShareForm}
          formStyle={{ maxWidth: '850px' }}
          onValuesChange={formValueChange}
          customBtn={
            <ActionBtns btns={[
              shareBtn(run),
            ]} />
          }
        />
      </Card>
    </Spin>

    {respData.length > 0 && (
      <Card className={styles.tiTableContent}>
        <TableSimple
          columns={respColumns}
          dataSource={respData}
          isLoading={loading}
        />
      </Card>
    )}
  </>);
}

export default Transfer;
