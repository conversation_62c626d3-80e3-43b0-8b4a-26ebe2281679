import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, Space, Switch, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount, useDebounceFn } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  getList,
  errorResp,
} from '@titd/publics/utils';

import Transfer from './Transfer';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableBadgeDot, TableTitle } = TableFields;
const { exchangeAllDict, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, MESSAGE_TYPE, SITE_URL, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { getAccountList, getClientList } = getList;

const ShareBase = () => {
  const navigate = useNavigate();
  const queryForm = useRef();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);
  const [selectKeys, setSelectKeys] = useState([1]);
  const [isCustom, setIsCustom] = useState(false);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
  }), [limit]);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
  //   title: $t('app.options.productid'),
  //   dataIndex: 'productid',
  //   // render: text => productGroup.find(i => i.value === text)?.label || text,
  // }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: TableTitle(
      $t('app.options.postion.position'),
      $t('app.options.postion.position.long')
    ),
    dataIndex: 'lposition',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: TableTitle(
      $t('app.options.postion.ydposition'),
      $t('app.options.postion.ydposition.long')
    ),
    dataIndex: 'lydposition',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: TableTitle(
      $t('app.options.postion.total'),
      $t('app.options.postion.tips')
    ),
    dataIndex: 'lpositionall',
    align: 'right',
    render: text => <Text strong>{NegaNumber(text)}</Text>
  }, {
    title: $t('app.options.postion.frozen'),
    dataIndex: 'lfrozen',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: $t('app.options.postion.buy'),
    dataIndex: 'lbuy',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: $t('app.options.postion.sell'),
    dataIndex: 'lsell',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: $t('app.options.postion.holdprice'),
    dataIndex: 'lholdprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4)
  }];

  const [accountOptions, setAccountOptions] = useState([]);
  const [clientGroup, setClientGroup] = useState([]);
  const [clientOptions, setClientOptions] = useState();

  const createSearchForm = useMemo(() => searchForm($t, accountOptions, clientOptions || clientGroup), [$t, accountOptions, clientGroup, clientOptions]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountOptions);
    getClientList(Request.post, defaultParams, setClientGroup);
  });
  useUnmount(() => cancel());

  // const getTableData = async params => {
  //   fetchFunc(MESSAGE_TYPE.QryAllPosition, params, resp => {

  //   }, () => {
  //     setDataSource([]);
  //     setTotal(0);
  //     setSelectKeys([]);
  //   });
  // };

  const getTableData = async params => {
    if (!params) {
      setTotal(0);
      setSelectKeys([]);
      return [];
    };

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAllPosition), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
        // 总持仓
        lpositionall: item.lposition + item.lydposition,
        // 可划拨持仓
        // lpositionuse: item.lydposition - item.lfrozen,
      }));

      setTotal(respData[0].rsptotnum);
      // setSelectKeys([1]);
      return tableData;
    } else {
      setTotal(0);
      setSelectKeys([]);
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const selectRecord = useMemo(() => {
    if (selectKeys.length > 0 && dataSource.length > 0) {
      return dataSource.find(i => i.id === selectKeys[0]);
    }

    return null;
  }, [selectKeys, dataSource]);

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      clientid: values.clientid,
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const { run: runClient } = useDebounceFn(async value => {
    if (value) {
      const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserClient), {
        ...defaultParams,
        userid: value,
      });

      const { data: clients } = resp;
      if (clients.length > 0) {
        const clientMap = clients.map(item => ({
          label: item.clientid,
          value: item.clientid,
        }));
        setClientOptions(clientMap);
      } else {
        setClientOptions([]);
      }
    } else {
      setClientOptions(clientGroup);
    }

    // 交易编码赋值
    queryForm.current?.set({
      'clientid': undefined,
    });
  }, { wait: 500 });

  const formValueChange = changedValues => {
    if ('accountid' in changedValues) {
      const value = changedValues.accountid;
      runClient(value);
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
        setSelectKeys([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.fund.share', {
        defaultMsg: '股份划拨',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
        onValuesChange: formValueChange
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        tableTitle: (<Space>
          <Switch
            checkedChildren="开启"
            unCheckedChildren="关闭"
            checked={isCustom}
            onClick={() => {
              setIsCustom(!isCustom);
              setSelectKeys([]);
            }}
          />
          自助划转
        </Space>),
        page: page,
        total: total,
        pageChange: pageChange,
        rowClickable: true,
        selectionType: 'radio',
        selectKeys: selectKeys,
        setSelectKeys: keys => {
          setSelectKeys(keys);
        },
      }}
    />

    {/* 资金划转 */}
    {(selectRecord || isCustom) && (
      <Transfer
        params={defaultParams}
        record={selectRecord}
        custom={isCustom}
        onFinish={onRefresh}
      />
    )}
  </>);
}

const Share = props => (
  // <KeepAlive name={keepName} key={keepName}>
  <ShareBase {...props} />
  // </KeepAlive>
);

export { Share };
