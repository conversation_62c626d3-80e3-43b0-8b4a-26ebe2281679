import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict, transferDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, accountOptions, clientOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions,
  }
}, {
  valueType: 'autocomplete',
  name: 'clientid',
  label: $t('app.options.clientid'),
  rules: [{ required: true }],
  fieldProps: {
    options: clientOptions
  }
}];

export const shareForm = ($t, isModify) => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  rules: [{ required: true }],
  fieldProps: {
    readOnly: isModify,
  }
}, {
//   valueType: 'plain',
//   name: 'lpositionuse',
//   label: $t('app.stock.account.positionuse'),
//   fieldProps: {
//     style: {
//       fontSize: '20px',
//       lineHeight: '20px',
//       color: '#0081cc',
//     }
//   }
// }, {
  valueType: 'select',
  name: 'trans_flag',
  label: $t('app.stock.account.flag'),
  rules: [{ required: true }],
  fieldProps: {
    options: selectOptions(transferDict),
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
  rules: [{ required: true }],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'number',
  name: 'position',
  label: $t('app.stock.account.volume'),
  rules: [{ required: true }],
}];
