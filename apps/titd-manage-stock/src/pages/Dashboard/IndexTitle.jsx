import { Typography } from 'antd';
import { FormattedMessage } from 'react-intl';

import { consts } from '@titd/publics/utils';

const { Title, Paragraph } = Typography;
const { SYSTEM_NAME } = consts;

const IndexTitle = () => (<>
  <Title level={3}>
    <FormattedMessage id={'app.dashboard.name'} />
    {`（${SYSTEM_NAME[1]}）`}
    <FormattedMessage id={'app.dashboard.version'}
      values={{ version: import.meta.env.VITE_APP_VERSION }} />
  </Title>
  <Paragraph><FormattedMessage id={'app.dashboard.intro'} /></Paragraph>
</>);

export default IndexTitle;
