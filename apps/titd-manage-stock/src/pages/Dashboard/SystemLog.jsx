import { useState } from 'react';
import { Link } from 'react-router';
import { Card, List, Typography, Divider, Grid } from 'antd';
import { useRequest } from 'ahooks';
import { useMount, useUnmount } from 'ahooks';

import { TableFields } from '@titd/publics/components';
import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dateFormat,
} from '@titd/publics/utils';

const { Text } = Typography;
const { useBreakpoint } = Grid;
const { TableLogType } = TableFields;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { formatNoDateString } = dateFormat;

const SystemLog = () => {
  const screens = useBreakpoint();

  const { $t } = useFormattedMessage();

  const isLogShow = (screens.lg === true);

  const defaultParams = {
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: Math.floor(DEFAULT_CONFIGS.LIMIT / 2),
  };

  const [menuMap, setMenuMap] = useState({});

  useMount(() => {
    getMenuTree();
  });
  useUnmount(() => cancel());

  const getLogData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QrySysLog), params);

    const { data: respData } = resp;
    return respData;
  };
  const getMenuTree = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryMenuSimple), {
      ...defaultParams,
      roleid: DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? 1 : 0,
    });

    const { data: respData } = resp;
    if (respData?.menu?.length > 0) {
      const menuData = {};
      const loop = data => {
        data.forEach(item => {
          menuData[item.menuid] = item.menuname;
          if (item.childmenu) {
            loop(item.childmenu);
          }
        });
      };
      loop(respData.menu);

      setMenuMap(menuData);
    }
  }

  const { data: logsData = [], cancel } = useRequest(getLogData, {
    defaultParams: [defaultParams],
    loadingDelay: 300,          // Loading Delay
    refreshOnWindowFocus: true, // 屏幕聚焦重新请求
  });

  const logItem = (item, idx) => (
    <List.Item key={idx}>
      <div style={{ paddingLeft: '16px', paddingRight: '16px' }}>
        <Text type="secondary">{formatNoDateString(item.logtime)}</Text>
        <Divider type="vertical" />
        <Text strong>{item.loginname}</Text>
        <Divider type="vertical" />
        <Text>{item.logip}</Text>
        <Divider type="vertical" />
        {TableLogType(item.logtype, menuMap)}
        {isLogShow && <>
          <Divider type="vertical" />
          <Text>{item.logdetail}</Text>
        </>}
      </div>
    </List.Item>
  );

  return logsData.length > 0 ? (
    <Card
      title={$t('app.dashboard.actionlog')}
      extra={<Link to="/sys/syslog">{$t('app.general.more')}</Link>}
      style={{ marginTop: '20px' }}
      styles={{
        body: {
          padding: 0,
        },
      }}
    >
      <List
        dataSource={logsData}
        renderItem={(item, idx) => logItem(item, idx)}
      />
    </Card>
  ) : null;
}

export default SystemLog;
