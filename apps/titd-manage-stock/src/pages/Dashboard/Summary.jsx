import { Link } from 'react-router';
import { Spin, Row, Col, Divider } from 'antd';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { StatisticCard } from '@titd/publics/components';
import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
} from '@titd/publics/utils';

import { BASE_STATUS, TRADE_STATUS, TRADE_SUM_STATUS } from './status';

const { SITE_URL, MESSAGE_TYPE } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;

const defaultSummary = {
  svrnum: 0,
  svrname: [],
  usernum: 0,
  normaluser: 0,
  logDate: '0000-00-00',
  statusArr: [],
};

const SummaryFooter = (label, value) => (
  <div className="field">
    {label && <span className="label">{label}</span>}
    <span className="number">{value || '-'}</span>
  </div>
);

const Summary = () => {
  const { $t } = useFormattedMessage();

  const defaultParams = {
    ...DEFAULT_SESSION_PARAMS(),
    svrid: undefined,
  };

  const SummaryContent = (labelid, group) => (<>
    {labelid && <Divider orientation="left" plain>{$t(labelid)}</Divider>}
    <Row gutter={[16, 16]}>
      {group.map((item, idx) => {
        return (
          <Col key={idx} xs={24} lg={12} xxl={8}>
            <StatisticCard
              title={$t(item.locale, {
                defaultMessage: item.title
              })}
              total={<span style={{ color: item.color }}>{summaryData.statusArr[item.id] || 0}</span>}
            />
          </Col>
        );
      })}
    </Row>
  </>);

  const getSummaryData = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.OwTdSvr), defaultParams);

    const { data: respData } = resp;
    const statusArr = [];
    if (respData.statusmsg?.length > 0) {
      respData.statusmsg.forEach(item => {
        statusArr[item.id] = item.vol;
      });

      // 在线用户数需计算
      const currentUser = statusArr[1] - statusArr[3] - statusArr[4];
      statusArr[0] = Math.max(currentUser, 0);
    }

    return {
      ...defaultSummary,
      ...respData,
      logDate: respData.confirtime && dayjs(respData.confirtime).format('YYYY-MM-DD'),
      statusArr: statusArr,
    };
  };

  const { data: summaryData = defaultSummary, loading } = useRequest(getSummaryData, {
    loadingDelay: 300,          // Loading Delay
    refreshOnWindowFocus: true, // 屏幕聚焦重新请求
  });

  return (
    <Spin spinning={loading} tip="加载中……">
      <Row gutter={[16, 16]}>
        {/* 服务器数量 */}
        <Col xs={24} lg={12} xxl={8}>
          <StatisticCard
            title={$t('app.dashboard.exchange')}
            desc={$t('app.dashboard.exchange.desc')}
            total={summaryData.svrnum}
            footer={SummaryFooter('', summaryData.svrname?.join(', '))}
          />
        </Col>

        {/* 用户数量 */}
        <Col xs={24} lg={12} xxl={8}>
          <StatisticCard
            title={$t('app.dashboard.user')}
            total={(
              <Link to="/db/userinfo">{summaryData.usernum}</Link>
            )}
            footer={SummaryFooter($t('app.dashboard.user.normal'), summaryData.normaluser)}
          />
        </Col>

        {/* 交易权限 */}
        {/*<Col xs={24} lg={12} xxl={6}>
          <StatisticCard
            title="交易权限"
            total={(
              <Link to="/db/traderight">{summaryData.right}</Link>
            )}
            footer={SummaryFooter('交易编码', summaryData.client)}
          />
        </Col>*/}

        {/* 上场确认 */}
        <Col xs={24} lg={12} xxl={8}>
          <StatisticCard
            title={$t('app.menu.db.importlog')}
            desc={$t('app.menu.db.importlog.desc')}
            total={(
              <Link to="/db/importlog">{summaryData.logDate}</Link>
            )}
            footer={SummaryFooter('', $t('app.options.db.log.lastdate'))}
          />
        </Col>
      </Row>

      {/* 基础信息 */}
      {SummaryContent('app.dashboard.base', BASE_STATUS)}
      {/* 现货信息 */}
      {SummaryContent('app.dashboard.trade.info', TRADE_STATUS)}
      {/* 成交汇总 */}
      <div style={{ marginTop: '15px' }}>{SummaryContent('', TRADE_SUM_STATUS)}</div>
    </Spin>
  );
}

export default Summary;
