import { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, TableTag, TableTitle, NegaNumber } from '@components/Table/fields';
import { searchForm } from '@pages/memory/UserInfo/data';

import { userStatusDict, tagColors } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserList } from '@utils/common';
import { formatMsToDateFull, formatDate } from '@utils/date-format';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistUserInfo = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  // const defaultParams = useMemo(() => ({
  //   ...DEFAULT_SESSION_PARAMS(),
  //   beginno: DEFAULT_CONFIGS.PAGE,
  //   endno: limit,
  // }), [limit]);
  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.userno}]`}
    </>
  }, {
  //   title: $t('app.options.db.userinfo.name'),
  //   dataIndex: 'username',
  // }, {
    title: $t('app.options.db.userinfo.status'),
    dataIndex: 'status',
    render: text => TableTag(text, userStatusDict, tagColors),
  }, {
    title: $t('app.options.mem.userinfo.loginsuccess'),
    dataIndex: 'loginsuccess',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.loginfailed'),
    dataIndex: 'loginfailed',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.timeout'),
    dataIndex: 'timeout',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.logout'),
    dataIndex: 'logout',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.orderinsert'),
    dataIndex: 'orderinsert',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.ordertierr'),
    dataIndex: 'ordertierr',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.orderexerr'),
    dataIndex: 'orderexerr',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.ordercancel'),
    dataIndex: 'ordercancel',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.canceltierr'),
    dataIndex: 'canceltierr',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.cancelexerr'),
    dataIndex: 'cancelexerr',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.db.userinfo.logincount'),
    dataIndex: 'logincount',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.db.userinfo.tradeflow'),
      $t('app.options.db.userinfo.tradeflow.long')
    ),
    dataIndex: 'tradeflow',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.mem.userinfo.maxloginsuccess'),
      $t('app.options.mem.userinfo.maxloginsuccess.long')
    ),
    dataIndex: 'maxloginsuccess',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.mem.userinfo.maxloginfailed'),
      $t('app.options.mem.userinfo.maxloginfailed.long')
    ),
    dataIndex: 'maxloginfailed',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.db.userinfo.lastloginip'),
    dataIndex: 'lastloginip'
  }, {
    title: $t('app.options.db.userinfo.lastlogintime'),
    dataIndex: 'lastlogintime',
    render: text => formatMsToDateFull(text),
  }, {
    title: $t('app.options.mem.userinfo.sequenceno'),
    dataIndex: 'sequenceno',
    align: 'right',
    render: text => NegaNumber(text),
  }].filter(i => i), [intl, checkDate]);

  const [userOptions, setUserOptions] = useState([]);
  const userFocus = useCallback(() => {
    getHistUserList(request.get, checkDate, setUserOptions);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userOptions, loading), [intl, userFocus, userOptions, loading]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('UserInfo', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistUserInfoAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistUserInfo {...props} />
  </KeepAlive>
);

export default HistUserInfoAlive;
