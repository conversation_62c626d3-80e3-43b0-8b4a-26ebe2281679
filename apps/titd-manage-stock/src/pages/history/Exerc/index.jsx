import { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import dayjs from 'dayjs';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useMount, useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, TableTag, NegaNumber, TableBadgeDot, TableTitle, ErrorMsg } from '@components/Table/fields';
import { searchForm } from '@pages/memory/Exerc/data';

import { ordStatusDict, exchangeDict, ownerTypeDict, ordActionTypeDict, ordStatusColor, tagNormalColors } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserClientList, getErrorCodeMsg } from '@utils/common';
import { formatDate, formatTime } from '@utils/date-format';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistExerc = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [errorCodeMsg, setErrorCodeMsg] = useState([]);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(null);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();
  const { get: eGet } = useFetch('');

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.userno}]`}
    </>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.order.cmdtype'),
    dataIndex: 'cmdtype',
    render: text => ordActionTypeDict[text] || text,
  }, {
    title: $t('app.options.order.ordtime'),
    dataIndex: 'ordtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno'
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
  }, {
    title: $t('app.options.order.ordresponsecode'),
    dataIndex: 'ordresponsecode',
    render: (text, record) => <ErrorMsg titdErrorCode={errorCodeMsg} errcode={text} exchangeid={record.exchangeid} />,
  }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.postion.legorderqty'),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.ownertype'),
    dataIndex: 'ownertype',
    render: text => ownerTypeDict[text] || text,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.order.frontno'),
      $t('app.options.order.frontno.long')
    ),
    dataIndex: 'frontno',
  }, {
    title: $t('app.options.mem.account.frozenmargin'),
    dataIndex: 'bfrozenmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'bfrozencommi',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.order.bfrozenvolume'),
    dataIndex: 'bfrozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.bthawvolume'),
    dataIndex: 'bthawvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }].filter(i => i), [intl, checkDate, errorCodeMsg]);

  const [userClientGroup, setUserClientGroup] = useState([]);
  const userFocus = useCallback(() => {
    getHistUserClientList(request.get, checkDate, setUserClientGroup);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userClientGroup, loading), [intl, userFocus, userClientGroup, loading]);

  useMount(() => {
    getErrorCodeMsg(eGet, setErrorCodeMsg);
  });
  useUnmount(() => abort());

  useEffect(() => {
    if (filterParams) {
      getTableData(checkDate, filterParams);
    }
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('Exerc', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    filterParams && setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      begtime: values?.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values?.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
        !searchParams.userrange && (searchParams.userid = userclient[1]);
      } else {
        searchParams.clientid = values.userclient;
      }

      delete searchParams.userclient;
      delete searchParams.userrange;
    }

    setFilterParams(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(null);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          userrange: 1,
          bssystemflag: 0,
        },
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistExercAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistExerc {...props} />
  </KeepAlive>
);

export default HistExercAlive;
