import { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, TableBadgeDot, TableTitle, NegaNumber } from '@components/Table/fields';
import { searchForm } from '@pages/memory/CommiMargin/data';

import { exchangeDict, tagNormalColors } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserClientList } from '@utils/common';
import { formatDate } from '@utils/date-format';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistCommiMargin = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.mem.commimargin.openbymoney') + $t('app.options.mem.commimargin.percent'),
    dataIndex: 'openbymoney',
    align: 'right',
    render: text => NegaNumber(text * 1000, 3),
  }, {
    title: $t('app.options.mem.commimargin.openbyvolume'),
    dataIndex: 'openbyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closebymoney') + $t('app.options.mem.commimargin.percent'),
    dataIndex: 'closebymoney',
    align: 'right',
    render: text => NegaNumber(text * 1000, 3),
  }, {
    title: $t('app.options.mem.commimargin.closebyvolume'),
    dataIndex: 'closebyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closetodaybymoney') + $t('app.options.mem.commimargin.percent'),
    dataIndex: 'closetodaybymoney',
    align: 'right',
    render: text => NegaNumber(text * 1000, 3),
  }, {
    title: $t('app.options.mem.commimargin.closetodaybyvolume'),
    dataIndex: 'closetodaybyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.openmax'),
    dataIndex: 'openmax',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.openmin'),
    dataIndex: 'openmin',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closemax'),
    dataIndex: 'closemax',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closemin'),
    dataIndex: 'closemin',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: $t('app.options.mem.commimargin.margin'),
    dataIndex: 'margin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.commimargin.marginratioparam') + '1',
    dataIndex: 'marginratioparam1',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.marginratioparam') + '2',
    dataIndex: 'marginratioparam2',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.mem.commimargin.marginrate'),
      $t('app.options.mem.commimargin.marginrate.long')
    ),
    dataIndex: 'marginrate',
    align: 'right',
  }, {
    title: $t('app.options.mem.commimargin.longbymoney'),
    dataIndex: 'longbymoney',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.longbyvolume'),
    dataIndex: 'longbyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.shortbymoney'),
    dataIndex: 'shortbymoney',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.shortbyvolume'),
    dataIndex: 'shortbyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }].filter(i => i), [intl, checkDate]);

  const [userClientGroup, setUserClientGroup] = useState([]);
  const userFocus = useCallback(() => {
    getHistUserClientList(request.get, checkDate, setUserClientGroup);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userClientGroup, loading, null, false), [intl, userFocus, userClientGroup, loading]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('CommiMargin', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    filterParams && setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    setFilterParams(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistCommiMarginAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistCommiMargin {...props} />
  </KeepAlive>
);

export default HistCommiMarginAlive;
