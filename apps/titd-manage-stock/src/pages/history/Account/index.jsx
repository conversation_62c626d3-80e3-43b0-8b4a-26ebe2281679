import { useState, useMemo, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, NegaNumber, TableTitle } from '@components/Table/fields';
import { searchForm } from '@pages/memory/Account/data';

import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { formatDate } from '@utils/date-format';
import { getFloat } from '@utils/currency';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistAccount = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState({
    ...defaultParams,
    showAuto: true,
  });

  const { get, response, loading: isLoading, abort } = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.mem.account.prebalance'),
    dataIndex: 'prebalance',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.distribfund'),
    dataIndex: 'distribfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.availusedfund'),
    dataIndex: 'availusedfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.dailyprofit'),
    dataIndex: 'dailyprofit',
    align: 'right',
    render: text => <Text strong>{NegaNumber(text, 2, 2)}</Text>,
  }, {
    title: $t('app.options.mem.account.commi'),
    dataIndex: 'commi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
  //   title: TableTitle(
  //     $t('app.options.mem.account.futmargin'),
  //     $t('app.options.mem.account.futmargin.long')
  //   ),
  //   dataIndex: 'futmargin',
  //   align: 'right',
  //   render: text => NegaNumber(text, 2, 2),
  // }, {
    title: TableTitle(
      $t('app.options.mem.account.optmargin'),
      $t('app.options.mem.account.optmargin.long')
    ),
    dataIndex: 'optmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.options.mem.account.combmargin'),
      $t('app.options.mem.account.combmargin.long')
    ),
    dataIndex: 'combmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.options.mem.account.recvmargin'),
      $t('app.options.mem.account.recvmargin.long')
    ),
    dataIndex: 'recvmargin',
    align: 'right',
    render: text => <Text strong>{NegaNumber(text, 2, 2)}</Text>,
  }, {
    title: $t('app.options.mem.account.closeprofit'),
    dataIndex: 'closeprofit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.posiprofit'),
    dataIndex: 'posiprofit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.premium'),
    dataIndex: 'premium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.deposit'),
    dataIndex: 'deposit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.withdraw'),
    dataIndex: 'withdraw',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozenmargin'),
    dataIndex: 'frozenmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozenpremium'),
    dataIndex: 'frozenpremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'frozencommi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.entryfees'),
    dataIndex: 'entryfees',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.options.mem.account.buypremium'),
      $t('app.options.mem.account.buypremium.long')
    ),
    dataIndex: 'buypremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }].filter(i => i), [intl, checkDate]);

  const [accountGroup, setAccountGroup] = useState([]);

  const createSearchForm = useMemo(() => searchForm(intl, accountGroup), [intl, accountGroup]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const caclProfit = item => {
    // 盈亏 = 结算准备金<可用资金> + 总保证金<当前期货保证金总额+当前期权保证金总额-当前保证金优惠总额> - 期权权利金收支<权利金收入-权利金支出> - (入金 - 出金) + 总冻结<冻结保证金+冻结权利金+冻结手续费> - 本系统分配资金
    // 可用资金 + 实收保证金 - (入金 - 出金) + 总冻结<冻结保证金+冻结权利金+冻结手续费> - 本系统分配资金

    return item.availusedfund + (item.optmargin - item.combmargin) - (item.deposit - item.withdraw) + (item.frozenmargin + item.frozenpremium + item.frozencommi) - item.distribfund;
  }

  const getTableData = async (date, {showAuto, ...params}) => {
    const resp = await get(SITE_URL.HISTORY_PATH('Account', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      const respList = resp.list;
      if (respList?.length > 0) {
        const tableData = respList.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
          // 实收保证金
          recvmargin: getFloat(item.optmargin - item.combmargin, 4),
          // 每日盈亏
          dailyprofit: caclProfit(item),
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }

      if (showAuto) {
        if (respList?.length > 0) {
          const accountIds = respList.map(item => ({
            label: item.accountid,
            value: item.accountid,
          }));
          setAccountGroup(accountIds);
        } else {
          setAccountGroup([]);
        }
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };
  const onRefresh = () => {
    setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistAccountAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistAccount {...props} />
  </KeepAlive>
);

export default HistAccountAlive;
