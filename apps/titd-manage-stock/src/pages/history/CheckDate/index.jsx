import { useRef, useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Card, Alert, Space, Button, Tag, Modal, App } from 'antd';
import { CloseOutlined, CheckOutlined } from '@ant-design/icons';
import useFetch from 'use-http';
import { FormattedMessage } from 'react-intl';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import { Calendar } from '@components/customAntd/Date';
import { HISTORY_DATE, TiLocalStorage } from '@utils/storage';
import { SITE_URL } from '@utils/consts';

import './index.less';

const CheckDate = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { message } = App.useApp();

  const selectRef = useRef(true);
  const [modal, contextHolder] = Modal.useModal();
  const [checkDate, setCheckDate] = useRecoilState(checkDateState);
  const [selectedValue, setSelectedValue] = useState([]);

  const from = location.state?.from?.pathname || '/hist/user/huserinfo';

  const { get, response, loading } = useFetch();

  useEffect(() => {
    if (checkDate) {
      const savedDate = checkDate.split(',').map(item => item);
      setSelectedValue(savedDate);
    }
  }, [checkDate]);

  // const onChange = date => {
  //   setSelectedValue(date);
  // }

  const findIdx = date => selectedValue.findIndex(item => item === date.format('YYYYMMDD'));
  const dateCellRender = date => {
    if (findIdx(date) !== -1) {
      return <div className="ti-calendar-selected"></div>
    }
  }

  const tagClose = idx => {
    selectedValue.splice(idx, 1);
    setSelectedValue([...selectedValue]);
  }

  const onSelect = date => {
    if (selectRef.current) {
      // console.log(selectedValue, date);
      const selectIdx = findIdx(date);
      if (selectIdx !== -1) {
        selectedValue.splice(selectIdx, 1);
      } else {
        selectedValue.push(date.format('YYYYMMDD'));
      }

      setSelectedValue([...selectedValue]);
    }

    selectRef.current = true;
  }

  const onSubmit = async () => {
    if (selectedValue.length > 0) {
      const resp = await get(SITE_URL.HISTORY_PATH('SqlPath', {
        histat: selectedValue.join(','),
      }));

      if (response.ok && resp.data?.length > 0) {
        const selectDate = resp.data.join(',');
        setCheckDate(selectDate);
        TiLocalStorage.set(HISTORY_DATE, selectDate);

        modal.info({
          title: '有数据日期',
          content: resp.data.map((item, idx) => (
            <Tag
              key={idx}
              color="orange"
            >{item}</Tag>
          )),
          okText: '确定',
          onOk: () => {
            navigate(from, { replace: true });
          },
        });
      } else {
        message.error('日期选择失败，请重新选择')
      }
    } else {
      message.error('请先选日期');
    }
  }

  const onCancel = () => {
    navigate(-1);
  }

  return (<>
    <Card>
      <div className="ti-calendar-content">
        <Alert
          className="ti-calendar-alert"
          message={<>
            {'已选日期: '}
            {selectedValue.map((item, idx) => (
              <Tag
                key={idx}
                color="orange"
                closable
                onClose={() => tagClose(idx)}
              >{item}</Tag>
            ))}
          </>}
        />
        <Calendar
          fullscreen={false}
          dateCellRender={dateCellRender}
          // defaultValue={savedDate}
          // onChange={onChange}
          onSelect={onSelect}
          // onPanelChange={(_, mode) => {
          onPanelChange={() => {
            // 避免 Cell 需要点击两次才能弹出详情
            selectRef.current = false;
          }}
        />
        <div className="ti-btn">
          <Space size="large">
            {location.state?.back && (<Button
              icon={<CloseOutlined />}
              onClick={onCancel}
            >
              <span><FormattedMessage id={'app.general.cancel'} /></span>
            </Button>)}
            <Button
              type="primary"
              loading={loading}
              icon={<CheckOutlined />}
              onClick={onSubmit}
            >
              <span><FormattedMessage id={'app.general.sure'} /></span>
            </Button>
          </Space>
        </div>
      </div>
    </Card>
    {contextHolder}
  </>);
}

export default CheckDate;
