import { useRef, useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import dayjs from 'dayjs';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useMount, useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, TableTag, NegaNumber, TableBadgeDot, TableTitle, ErrorMsg } from '@components/Table/fields';
import { DrawerForm } from '@components/form';
import { searchForm } from '@pages/memory/OrderInsert/data';
import ActionLinks from '@components/Table/ActionLinks';
import {
  rtnLink,
  infoLink,
} from '@components/actions/links';

import { exchangeDict, sideDict, ordStatusDict, priceTypeDict, timeInforceDict, ownerTypeDict, offsetFlagDict, clientTypeDict, coveredDict, volumeDict, trigDict, ordActionTypeDict, ordStatusColor, tagNormalColors, tagColors } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserClientList, getErrorCodeMsg } from '@utils/common';
import { formatDate, formatTime } from '@utils/date-format';
import { getFloat } from '@utils/currency';
import { objFilter } from '@utils/tools';

import OrderDetail from './OrderDetail';
import TradeDetail from './TradeDetail';

const { Text } = Typography;

const HistOrderInsert = () => {
  const drawerRtnForm = useRef(null);
  const drawerInfoForm = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [selectRecord, setSelectRecord] = useState({});
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [errorCodeMsg, setErrorCodeMsg] = useState([]);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();
  const { get: eGet } = useFetch('');

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.order.cmdtype'),
    dataIndex: 'cmdtype',
    render: text => ordActionTypeDict[text] || text,
  }, {
    title: $t('app.options.order.ordtime'),
    dataIndex: 'ordtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.order.exrequestid'),
    dataIndex: 'exrequestid',
    // render: (text, record) => <>
    //   <Text strong>{text}</Text>
    //   <Text>{` [${record.responsestr}]`}</Text>
    // </>,
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno',
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
  }, {
    title: $t('app.options.order.ordresponsecode'),
    dataIndex: 'ordresponsecode',
    render: (text, record) => <ErrorMsg titdErrorCode={errorCodeMsg} errcode={text} exchangeid={record.exchangeid} />,
  }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.rec2send'),
    dataIndex: 'rec2send',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.rec2exrsp'),
    dataIndex: 'rec2exrsp',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.cpuid'),
    dataIndex: 'cpuid',
  }, {
    title: $t('app.options.order.cancelvolume'),
    dataIndex: 'cancelvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.ssepartyid'),
    dataIndex: 'ssepartyid',
  }, {
    title: $t('app.options.mem.account.frozenmargin'),
    dataIndex: 'bfrozenmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'bfrozencommi',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.mem.account.frozenpremium'),
    dataIndex: 'bfrozenpremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.order.bfrozenvolume'),
    dataIndex: 'bfrozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.bthawvolume'),
    dataIndex: 'bthawvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.order.frontno'),
      $t('app.options.order.frontno.long')
    ),
    dataIndex: 'frontno',
  }, {
    title: TableTitle(
      $t('app.options.order.pricetype'),
      $t('app.options.order.pricetype.long')
    ),
    dataIndex: 'pricetype',
    render: text => priceTypeDict[text] || text,
  }, {
    title: $t('app.options.postion.side'),
    dataIndex: 'side',
    render: text => TableTag(text, sideDict, tagColors, true),
  }, {
    title: $t('app.options.order.offsetflag'),
    dataIndex: 'offsetflag',
    render: text => offsetFlagDict[text] || text,
  }, {
    title: $t('app.options.order.hedgeflag'),
    dataIndex: 'hedgeflag',
    render: text => clientTypeDict[text] || text,
  }, {
    title: $t('app.options.order.timeinforce'),
    dataIndex: 'timeinforce',
    render: text => timeInforceDict[text] || text,
  }, {
    title: $t('app.options.postion.covereduncovered'),
    dataIndex: 'coveredoruncovered',
    render: text => coveredDict[text] || text,
  }, {
    title: $t('app.options.order.volumecondition'),
    dataIndex: 'volumecondition',
    render: text => volumeDict[text] || text,
  }, {
    title: $t('app.options.order.trigcondition'),
    dataIndex: 'trigcondition',
    render: text => trigDict[text] || text,
  }, {
    title: $t('app.options.order.volume'),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.minvolume'),
    dataIndex: 'minvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.price'),
    dataIndex: 'price',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.order.stopprice'),
    dataIndex: 'stopprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.order.ownertype'),
    dataIndex: 'ownertype',
    render: text => ownerTypeDict[text] || text,
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 180,
    render: (_, record) => record.ordersysid && record.ordersysid !== ' ' ? (
      <ActionLinks links={[
        rtnLink(toRtnDetail, record),
        infoLink(toInfoDetail, record),
      ]} />
    ) : null
  }].filter(i => i), [intl, checkDate, errorCodeMsg]); // eslint-disable-line react-hooks/exhaustive-deps

  const [userClientGroup, setUserClientGroup] = useState([]);
  const userFocus = useCallback(() => {
    getHistUserClientList(request.get, checkDate, setUserClientGroup);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userClientGroup, loading, false), [intl, userFocus, userClientGroup, loading]);

  useMount(() => {
    getErrorCodeMsg(eGet, setErrorCodeMsg);
  });
  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('OrderInsert', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
          rec2send: getFloat(item.rec2send, 4),
          rec2exrsp: getFloat(item.rec2exrsp, 4),
          bfrozenmargin: getFloat(item.bfrozenmargin, 4),
          bfrozenpremium: getFloat(item.bfrozenpremium, 4),
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    filterParams && setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      begtime: values.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
        !searchParams.userrange && (searchParams.userid = userclient[1]);
      } else {
        searchParams.clientid = values.userclient;
      }

      delete searchParams.userclient;
      delete searchParams.userrange;
    }

    setFilterParams(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const toRtnDetail = record => {
    drawerRtnForm.current.show(`${$t('app.menu.mem.rtn.rtnorder')}: ${record.ordersysid}`);
    setSelectRecord(record);
  }

  const toInfoDetail = record => {
    drawerInfoForm.current.show(`${$t('app.menu.mem.rtn.rtntrade')}: ${record.ordersysid}`);
    setSelectRecord(record);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (<>
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          userrange: 1,
          bssystemflag: 0,
        },
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    {/* 委托回报 */}
    <DrawerForm
      ref={drawerRtnForm}
      formData={[]}
    >
      <OrderDetail
        order={selectRecord}
        params={defaultParams}
        checkDate={checkDate}
      />
    </DrawerForm>

    {/* 成交回报 */}
    <DrawerForm
      ref={drawerInfoForm}
      formData={[]}
    >
      <TradeDetail
        order={selectRecord}
        params={defaultParams}
        checkDate={checkDate}
      />
    </DrawerForm>
  </>);
}

const HistOrderInsertAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistOrderInsert {...props} />
  </KeepAlive>
);

export default HistOrderInsertAlive;
