import { useEffect, useState } from 'react';
import { Typography } from 'antd';
import useFetch from 'use-http';
import { useIntl } from 'react-intl';

import TableSimple from '@components/Table/Simple';
import { IndexColumn, NegaNumber, TableTitle } from '@components/Table/fields';

import { SITE_URL } from '@utils/consts';

import { formatTime } from '@utils/date-format';

import Descs from '@pages/memory/OrderInsert/Descs';
import { baseMap } from '@pages/memory/OrderInsert/map';

const { Text } = Typography;

const TradeDetail = props => {
  const {
    order,
    params,
    checkDate,
  } = props;

  const intl = useIntl();

  const [base, setBase] = useState([]);
  const [infoData, setInfoData] = useState([]);

  const { get, response, loading: isLoading, abort } = useFetch();

  const columns = [{
    title: $t('app.options.rtn.sequenceno'),
    dataIndex: 'sequenceno',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.trade.transtime'),
    dataIndex: 'transtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.trade.tradeprice'),
    dataIndex: 'tradeprice',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: $t('app.options.trade.tradevolume'),
    dataIndex: 'tradevolume',
    align: 'right',
  }, {
    title: TableTitle(
      $t('app.options.trade.leavesvolume'),
      $t('app.options.trade.leavesvolume.long')
    ),
    dataIndex: 'leavesvolume',
    align: 'right',
  }, {
    title: $t('app.options.trade.tradeid'),
    dataIndex: 'tradeid',
  }];

  const getGroupData = async record => {
    // 基础消息
    const baseData = baseMap(intl, record);
    setBase(baseData);

    const postData = {
      ...params,
      clientid: record.clientid,
      ordersysid: record.ordersysid,
      userid: record.userid,
      histat: checkDate,
    };

    // 成交
    const resp = await get(SITE_URL.HISTORY_PATH('RtnTrade', postData));
    if (response.ok && resp?.list.length > 0) {
      setInfoData(resp.list);
    }
  }

  useEffect(() => {
    getGroupData(order);

    return () => {
      abort();
      setBase([]);
      setInfoData([]);
    }
  }, [order]); // eslint-disable-line react-hooks/exhaustive-deps

  return (<>
    <Descs list={base} />
    <TableSimple
      customKey={'sequenceno'}
      columns={columns}
      dataSource={infoData}
      isLoading={isLoading}
    />
  </>);
}

export default TradeDetail;
