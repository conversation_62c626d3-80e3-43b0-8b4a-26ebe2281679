export const searchForm = ($t, accountGroup) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountGroup,
  }
}];

export const transferForm = ($t, digitUpper) => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.options.accountid'),
}, {
  valueType: 'plain',
  name: 'availused',
  label: $t('app.stock.cashaccount.availused'),
  fieldProps: {
    style: {
      fontSize: '20px',
      lineHeight: '20px',
      color: '#0081cc',
    }
  }
}, {
  valueType: 'plain',
  name: 'deposit',
  label: $t('app.stock.common.deposit'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#52c41a',
    }
  }
}, {
  valueType: 'plain',
  name: 'withdraw',
  label: $t('app.stock.common.withdraw'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#ff4d4f',
    }
  }
}, {
  valueType: 'number',
  name: 'transfer',
  label: $t('app.stock.common.account'),
  extra: digitUpper,
  rules: [{ required: true }],
}];
