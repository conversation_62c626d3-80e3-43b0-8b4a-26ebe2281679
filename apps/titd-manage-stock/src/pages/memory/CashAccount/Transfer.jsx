import { useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Card, Spin, Space, Button, message } from 'antd';
import {
  ImportOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { useRequest, useUnmount } from 'ahooks';

import {
  TableSimple,
  TableFields,
  forms,
} from '@titd/publics/components';
import { transferForm } from './data';

import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
  currency,
  errorResp,
} from '@titd/publics/utils';

const { TableTag } = TableFields;
const { SimpleForm } = forms;
const { loginStatusDict, tagColors } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;
const { digitUppercase, formatDot } = currency;

const Transfer = props => {
  const {
    params,
    record,
    onSuccess,
  } = props;

  const aForm = useRef();
  const navigate = useNavigate();
  const { $t } = useFormattedMessage();

  const [oldAccountid, setOldAccountid] = useState();
  const [respData, setRespData] = useState([]);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const respColumns = [{
    dataIndex: 'respstatus',
    title: $t('app.stock.account.status'),
    render: text => TableTag(text, loginStatusDict, tagColors),
  }, {
    dataIndex: 'responsestr',
    title: $t('app.stock.account.logmessage'),
  }];

  const amountType = useMemo(() => {
    return [
      ['withdraw', $t('app.stock.common.withdraw')],
      ['deposit', $t('app.stock.common.deposit')],
    ];
  }, [$t]);

  const [digitUpper, setDigitUpper] = useState('');
  const createTransferForm = useMemo(() => transferForm($t, digitUpper), [$t, digitUpper]);

  useUnmount(() => cancel());

  useEffect(() => {
    if (record) {
      if (record.accountid !== oldAccountid) {
        setOldAccountid(record.accountid);
        setRespData([]);
      }

      transferReset();

      const newRecord = {
        ...record,
        availused: formatDot(record.availused, 2),
        deposit: formatDot(record.deposit, 2),
        withdraw: formatDot(record.withdraw, 2),
      }

      aForm.current.set(newRecord);
    }
  }, [record]); // eslint-disable-line react-hooks/exhaustive-deps

  const transferReset = () => {
    aForm.current.reset();
    setDigitUpper('');
  }

  const formValueChange = changedValues => {
    if ('transfer' in changedValues) {
      const value = changedValues.transfer;
      if (value !== '') {
        setDigitUpper(digitUppercase(value));
      } else {
        setDigitUpper('');
      }
    }
  }

  const amountSubmit = type => {
    aForm.current.submit(values => {
      // console.log(values, type);
      const transfer = amountType[type];
      if (transfer) {
        const postData = {
          ...params,
          accountid: values.accountid,
          [transfer[0]]: values.transfer,
        };

        fetchFunc(MESSAGE_TYPE.MemUpdCashAccount, postData, resp => {
          if (resp.errcode) {
            message.error(resp.errmessage);
          } else {
            if (resp?.length > 0) {
              const tableData = resp.map((item, idx) => ({
                ...item,
                id: idx + 1,
                respstatus: item.responsecode < 0 ? 1 : 0,
              }));
              setRespData(tableData);
            } else {
              setRespData([]);
            }
            onSuccess && onSuccess();
          }
        });
      }
    });
  }

  const { loading, run, cancel } = useRequest(amountSubmit, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  return (<>
    <Spin spinning={loading} tip="划转中……">
      <Card className="ti-table-content">
        <SimpleForm
          ref={aForm}
          formData={createTransferForm}
          onValuesChange={formValueChange}
          formStyle={{ maxWidth: '600px' }}
          customBtn={<Space>
            <Button
              size="large"
              color="cyan"
              variant="solid"
              disabled={loading}
              icon={<ImportOutlined />}
              onClick={() => run(1)}
            >{$t('app.stock.common.deposit')}</Button>
            <Button
              size="large"
              color="orange"
              variant="solid"
              disabled={loading}
              icon={<ExportOutlined />}
              onClick={() => run(0)}
            >{$t('app.stock.common.withdraw')}</Button>
          </Space>}
        />
      </Card>
    </Spin>

    {respData.length > 0 && (
      <Card className="ti-table-content">
        <TableSimple
          columns={respColumns}
          dataSource={respData}
          isLoading={loading}
        />
      </Card>
    )}
  </>);
}

export default Transfer;
