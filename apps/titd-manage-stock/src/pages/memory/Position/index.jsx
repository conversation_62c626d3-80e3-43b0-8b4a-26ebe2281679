import { useRef, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
  exportDownModal,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

import TabForm from './TabForm';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableBadgeDot, TableTitle } = TableFields;
const { DrawerForm } = forms;
const { detailLink } = links;
const { exportAllBtn } = btns;
const { exchangeAllDict, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList, getProductByExchange } = getList;

const PositionBase = props => {
  const { paramId } = props;

  const navigate = useNavigate();
  const queryForm = useRef(null);
  const drawerForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectRecord, setSelectRecord] = useState({});
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = exchangeList && exchangeList[paramId];

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const financeColumns = [{
    title: $t('app.stock.account.respstr1'),
    dataIndex: 'smargin',
    align: 'right',
    render: text => NegaNumber(text, 2),
    className: 'ti-group-border',
  }, {
    title: $t('app.stock.position.checksingle'),
    className: 'ti-group-border',
    children: [{
      title: $t('app.stock.position.check.cents'),
      dataIndex: 'sholdprice',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }, {
      title: $t('app.stock.position.check.limits'),
      dataIndex: 'sbuy',
      align: 'center',
      render: (text, record) => formatLimits(record.sfrozen, text),
    }, {
      title: $t('app.stock.position.check.ratiomaxs'),
      dataIndex: 'ssell',
      align: 'right',
      className: 'ti-group-border',
      render: text => NegaNumber(text),
    }]
  }, {
    title: $t('app.stock.position.checkgroup'),
    children: [{
      title: $t('app.stock.position.check.cents'),
      dataIndex: 'choldprice',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }, {
      title: $t('app.stock.position.check.limits'),
      dataIndex: 'cbuy',
      align: 'center',
      render: (text, record) => formatLimits(record.cfrozen, text),
    }, {
      title: $t('app.stock.position.check.ratiomaxs'),
      dataIndex: 'csell',
      align: 'right',
      render: text => NegaNumber(text),
    }]
  }];

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
    render: text => productGroup.find(i => i.value === text)?.label || text,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: TableTitle(
      $t('app.options.postion.position'),
      $t('app.options.postion.position.long')
    ),
    dataIndex: 'lposition',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.postion.ydposition'),
      $t('app.options.postion.ydposition.long')
    ),
    dataIndex: 'lydposition',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.postion.total'),
      $t('app.options.postion.tips')
    ),
    dataIndex: 'lpositionall',
    align: 'right',
    render: text => <Text strong>{NegaNumber(text)}</Text>
  }, {
    title: $t('app.options.postion.frozen'),
    dataIndex: 'lfrozen',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.postion.buy'),
    dataIndex: 'lbuy',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.postion.sell'),
    dataIndex: 'lsell',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.postion.holdprice'),
    dataIndex: 'lholdprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
    // }, {
    //   title: TableTitle('持仓保证金', '期权会随着行情的变化而变化, 期货的保证金买入后不变'),
    //   dataIndex: 'lmargin',
    //   align: 'right',
    //   className: 'ti-group-border',
    //   render: text => NegaNumber(text)
  }, ...(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? financeColumns : []), {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ID + 10,
    render: (_, record) => (
      <ActionLinks links={[
        detailLink(toDetail, record),
      ]} />
    )
  }].filter(i => i);

  const formatLimits = (min, max) => {
    if (min === 1000000) return $t('app.stock.position.check.nodebt');
    if (min === 0 && max === 0) return '-';
    min = min && `${min}%`;
    if (max === 0 || max === 1000000) return `${min} ≤ R`;
    return `${min} ≤ R < ${max}%`;
  }

  const [accountGroup, setAccountGroup] = useState([]);
  const [productGroup, setProductGroup] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, accountGroup, exchanges), [$t, accountGroup, exchanges]);
  const createAddForm = useMemo(() => addForm($t), [$t]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountGroup);

    // 展示用
    getProductByExchange(Request, defaultParams, setProductGroup);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryPosition, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => {
        const itemData = {
          ...item,
          id: item.rspseqno,
        }

        if (DEFAULT_CONFIGS.SHOW_MARGINFINANCING) {
          return ({
            ...itemData,
            // 9999 / 10001 -> '> 1,000,000%'
            smargin: item.smargin >= 9999 ? '> 1,000,000%' : item.smargin * 100,
            sholdprice: item.sholdprice * 100,
            choldprice: item.choldprice * 100,
            lpositionall: item.lposition + item.lydposition,
          })
        }

        return itemData;
      });

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const toDetail = (record) => {
    // console.log('Detail', record);
    drawerForm.current.show(`${record.clientid} - ${record.instrumentid}`, record);
    setSelectRecord(record);
  }

  const afterSubmit = () => {
    drawerForm.current.close(onRefresh);
  }

  const toExport = () => {
    fetchFunc(MESSAGE_TYPE.DownloadPosition, defaultParams, resp => {
      // console.log(resp);
      if (resp.errmessage) {
        exportDownModal(resp.errmessage);
      } else {
        message.error('导出失败');
      }
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.pos.position', {
        defaultMsg: '持仓汇总',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        actionBtns: [
          exportAllBtn(toExport),
        ],
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    <DrawerForm
      ref={drawerForm}
      formData={createAddForm}
    >
      <TabForm
        record={selectRecord}
        params={defaultParams}
        afterSubmit={afterSubmit}
      />
    </DrawerForm>
  </>);
}

const Position = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <PositionBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Position };
