import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict } = dicts;
const { selectOptions, selectExchange } = formOptions;

export const searchForm = ($t, accountGroup, exchanges) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountGroup,
  }
}, {
  valueType: 'input',
  name: 'productid',
  label: $t('app.options.productid'),
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}];

export const addForm = $t => [{
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    readOnly: true,
    variant: 'borderless',
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    open: false,
    variant: 'borderless',
    allowClear: false,
    suffixIcon: null,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
  fieldProps: {
    readOnly: true,
    variant: 'borderless',
  }
}];

export const updateForm = $t => [{
  valueType: 'number',
  name: 'position',
  label: $t('app.options.postion.position'),
}, {
  valueType: 'number',
  name: 'ydposition',
  label: $t('app.options.postion.ydposition'),
}, {
  valueType: 'number',
  name: 'frozen',
  label: $t('app.options.postion.frozen'),
}, {
  valueType: 'number',
  name: 'buy',
  label: $t('app.options.postion.buy'),
}, {
  valueType: 'number',
  name: 'sell',
  label: $t('app.options.postion.sell'),
}, {
  valueType: 'number',
  name: 'holdprice',
  label: $t('app.options.postion.holdprice'),
}, {
  valueType: 'input',
  name: 'positionall',
  label: $t('app.options.postion.total'),
  fieldProps: {
    readOnly: true,
    variant: 'borderless',
  }
}];
