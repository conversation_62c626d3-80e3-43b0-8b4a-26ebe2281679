import { useRef, useEffect, useMemo } from 'react';
import { Card, Button, App } from 'antd';

import {
  forms,
} from '@titd/publics/components';
import {
  consts,
  useFormattedMessage,
  Request,
} from '@titd/publics/utils';

import { updateForm } from './data';

const { NormalForm } = forms;
const { SITE_URL, MESSAGE_TYPE, CURD } = consts;

const TabForm = props => {
  const {
    record,
    params,
    afterSubmit,
  } = props;

  const lForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const CardItem = ({ title, refForm, formData, func }) => {
    return (
      <Card
        title={title}
        size="small"
        variant="borderless"
        extra={(
          <Button
            color="primary"
            variant="solid"
            onClick={func}
          >{$t('app.general.update')}</Button>
        )}
      >
        <NormalForm
          ref={refForm}
          formData={formData}
        />
      </Card>
    );
  }

  useEffect(() => {
    if (record) {
      lForm.current?.set({
        position: record.lposition,
        ydposition: record.lydposition,
        frozen: record.lfrozen,
        buy: record.lbuy,
        sell: record.lsell,
        holdprice: record.lholdprice,
        positionall: record.lpositionall,
      });
    }

    // return () => {
    //   abort();
    // }
  }, [record]);

  const createUpdateForm = useMemo(() => updateForm($t), [$t]);

  const formSubmit = async (form, type) => {
    const postData = {
      ...params,
      ...form,
      exchangeid: record.exchangeid,
      clientid: record.clientid,
      instrumentid: record.instrumentid,
      postype: type,
    }

    delete postData.positionall;

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemUpdPosition, params.svrid), postData);

    const { data: respData } = resp;
    if (respData.length > 0 && respData[0].responsecode < 1) {
      message.success(CURD.Update + CURD.StatusOkMsg);
      afterSubmit();
    } else {
      message.error(respData[0]?.responsestr || CURD.ServerError);
    }
  }

  return (<>
    <CardItem
      title={$t('')}
      refForm={lForm}
      formData={createUpdateForm}
      func={() => lForm.current?.submit(form => formSubmit(form, 1))}
    />
  </>);
}

export default TabForm;
