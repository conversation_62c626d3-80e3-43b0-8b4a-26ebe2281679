import { TableFields } from '@titd/publics/components';
import { consts } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { DEFAULT_CONFIGS } = consts;

export const searchForm = ($t, accountGroup, isRequired = true) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  rules: [{ required: isRequired }],
  fieldProps: {
    options: accountGroup,
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? {
  valueType: 'input',
  name: 'tradeid',
  label: $t('app.options.postion.tradeid'),
} : null].filter(i => i);

export const calcForm = $t => [{
  label: '充抵保证金 = （总数量 - 两融数量）* 最新价 * 折算率',
  children: [{
    valueType: 'text',
    name: 'volume',
    label: TableTitle(
      '( ' + $t('app.stock.postion.allvolume'),
      $t('app.stock.postion.allvolume.long'),
    ),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'lrvolume',
    label: TableTitle(
      '- ' + $t('app.stock.postion.lrvolume') + ' )',
      $t('app.stock.postion.lrvolume.long'),
    ),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'lastprice',
    label: '* ' + $t('app.options.instrument.lastprice'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'coll_disc',
    label: TableTitle(
      '* ' + $t('app.stock.instrument.collateralconvrate'),
      $t('app.stock.instrument.collateralconvrate.long'),
    ),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'checkKcdzqsz',
    label: '= ' + $t('app.stock.account.calc') + $t('app.stock.position.kcdzqsz'),
    fieldProps: {
      style: { textAlign: 'right', fontWeight: 'bold' }
    }
  }, {
    valueType: 'text',
    name: 'kcdzqsz',
    label: TableTitle(
      $t('app.stock.position.kcdzqsz'),
      $t('app.stock.position.kcdzqsz.long')
    ),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }]
}, {
  label: '两融保证金 = 未还本金 * 融资比例',
  children: [{
    valueType: 'text',
    name: 'toreturnprice',
    label: $t('app.stock.position.toreturnprice'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'fi_margin',
    label: TableTitle(
      '* ' + $t('app.stock.instrument.fimargin'),
      $t('app.stock.instrument.fimargin.long'),
    ),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'checkRzbzj',
    label: '= ' + $t('app.stock.account.calc') + $t('app.stock.position.rzbzj'),
    fieldProps: {
      style: { textAlign: 'right', fontWeight: 'bold' }
    }
  }, {
    valueType: 'text',
    name: 'rzbzj',
    label: $t('app.stock.position.rzbzj'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }]
}, {
  label: '融资浮盈 = (两融数量 * 最新价 - 未还本金) * 折算率',
  children: [{
    valueType: 'text',
    name: 'lrvolume',
    label: TableTitle(
      '( ' + $t('app.stock.postion.lrvolume'),
      $t('app.stock.postion.lrvolume.long'),
    ),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'lastprice',
    label: '* ' + $t('app.options.instrument.lastprice'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'toreturnprice',
    label: '- ' + $t('app.stock.position.toreturnprice') + ' )',
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'collDisc',
    label: '* ' + $t('app.stock.instrument.collateralconvrate'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'checkRzyl',
    label: '= ' + $t('app.stock.account.calc') + $t('app.stock.position.rzyl'),
    fieldProps: {
      style: { textAlign: 'right', fontWeight: 'bold' }
    }
  }, {
    valueType: 'text',
    name: 'rzyl',
    label: $t('app.stock.position.rzyl'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }]
}, {
  label: '可用保证金 = 充抵保证金 + 融资盈亏 - 两融保证金 - 未还手续费 - 未还利息',
  children: [{
    valueType: 'text',
    name: 'checkKcdzqsz',
    label: $t('app.stock.position.kcdzqsz'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'checkRzyl',
    label: '+ ' + $t('app.stock.position.rzyl'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'checkRzbzj',
    label: '- ' + $t('app.stock.position.rzbzj'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'returncommi',
    label: '- ' + $t('app.stock.position.returncommi'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'interest',
    label: '- ' + $t('app.stock.cdposition.pendrepayint'),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }, {
    valueType: 'text',
    name: 'checkAllmargin',
    label: '= ' + $t('app.stock.account.calc') + $t('app.stock.account.allmargin'),
    fieldProps: {
      style: { textAlign: 'right', fontWeight: 'bold' }
    }
  }, {
    valueType: 'text',
    name: 'allmargin',
    label: TableTitle(
      $t('app.stock.account.allmargin'),
      $t('app.stock.position.allmargin.long'),
    ),
    fieldProps: {
      style: { textAlign: 'right' }
    }
  }]
}];
