import { useRef, useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { searchForm, calcForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
  currency,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTag, NegaNumber, TableBadgeDot, TableTitle } = TableFields;
const { DrawerForm } = forms;
const { showCalcLink } = links;
const { exchangeAllDict, sideDict, productDict, tagNormalColors, tagColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList } = getList;
const { formatDot } = currency;

const PositionDtlBase = props => {
  const { paramId } = props;

  const queryForm = useRef(null);
  const drawerForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  const financeColumns = [{
    title: TableTitle(
      $t('app.stock.account.allmargin'),
      $t('app.stock.position.allmargin.long'),
    ),
    dataIndex: 'allmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.position.kcdzqsz'),
      $t('app.stock.position.kcdzqsz.long')
    ),
    dataIndex: 'kcdzqsz',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.position.rzbzj'),
    dataIndex: 'rzbzj',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.position.rzyl'),
    dataIndex: 'rzyl',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.fimargin'),
      $t('app.stock.instrument.fimargin.long'),
    ),
    dataIndex: 'fi_margin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.slmargin'),
      $t('app.stock.instrument.slmargin.long'),
    ),
    dataIndex: 'sl_margin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.collateralconvrate'),
      $t('app.stock.instrument.collateralconvrate.long'),
    ),
    dataIndex: 'coll_disc',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.pendrepayint'),
    dataIndex: 'interest',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.position.toreturnprice'),
    dataIndex: 'toreturnprice',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.position.returncommi'),
    dataIndex: 'returncommi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.repaidamt'),
    dataIndex: 'repaidamt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.repaidqty'),
    dataIndex: 'repaidqty',
    align: 'right',
    render: text => NegaNumber(text, 2),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 120,
    render: (_, record) => (
      <ActionLinks links={[
        showCalcLink(toShowCalc, record),
      ]} />
    )
  }];

  const volumeGroup = DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? [{
    title: $t('app.stock.cdposition.orderid'),
    dataIndex: 'tradeid',
  }, {
    title: $t('app.stock.postion.instrvolume'),
    dataIndex: 'instrvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.stock.postion.ydvolume'),
      $t('app.stock.postion.ydvolume.long'),
    ),
    dataIndex: 'ydvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.stock.postion.allvolume'),
      $t('app.stock.postion.allvolume.long'),
    ),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.stock.postion.lrvolume'),
      $t('app.stock.postion.lrvolume.long'),
    ),
    dataIndex: 'lrvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }] : [{
    title: $t('app.stock.postion.allvolume'),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }];

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.postion.side'),
    dataIndex: 'side',
    render: text => TableTag(text, sideDict, tagColors, true),
  }, ...volumeGroup, {
    title: $t('app.options.postion.price'),
    dataIndex: 'price',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.instrument.lastprice'),
    dataIndex: 'lastprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.producttype'),
    dataIndex: 'producttype',
    render: text => productDict[String.fromCharCode(text)] || text,
  }, ...(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? financeColumns : [])].filter(i => i);

  const [accountGroup, setAccountGroup] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, accountGroup), [$t, accountGroup]);
  const createCalcForm = useMemo(() => calcForm($t), [$t]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountGroup);
  });
  useUnmount(() => cancel());

  // 普通持仓：可用保证金=充低保证金
  // 融资持仓：可用保证金=充低保证金+两融浮盈-两融保证金-待偿利息-待偿手续费
  // 融券持仓：可用保证金=充低保证金+两融浮盈-两融保证金-待偿利息-待偿手续费-融资卖出金额
  const getTableData = async ({ noDataShow, ...params }) => {
    if (!params) {
      setTotal(0);
      return [];
    };

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryPositionDtl, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => {
        const itemData = {
          ...item,
          id: item.rspseqno,
        }

        if (DEFAULT_CONFIGS.SHOW_MARGINFINANCING && item.responsestr) {
          const respstr = item.responsestr.split('|').map(i => Number(i));

          return {
            ...itemData,
            coll_disc: respstr[0],
            fi_margin: respstr[1],
            sl_margin: respstr[2],
            kcdzqsz: respstr[3],
            rzyl: respstr[4],
            rzbzj: respstr[5],
            // rqyl: respstr[6],
            // rqfz: respstr[7],
            // rqzj: respstr[8],
            interest: respstr[6],
            lastprice: respstr[7],
            ydvolume: respstr[8],
            // returnvolume: respstr[12],
            toreturnprice: respstr[9],
            repaidamt: item.side === 3 ? respstr[10] : '-',
            repaidqty: item.side === 4 ? respstr[10] : '-',
            returncommi: respstr[11],
            allmargin: respstr[12],
            lrvolume: respstr[13],
            instrvolume: respstr[14],
          }
        }
        return itemData;
      });

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const toShowCalc = record => {
    // console.log('Calc', record);
    // 充抵保证金 = （总数量 - 两融数量）* 最新价 * 折算率
    const checkKcdzqsz = (record.volume - record.lrvolume) * record.lastprice * record.coll_disc;

    // 两融保证金 = 未还本金 * (融资比例 / 融券比例)
    // 融资持仓 -> 融资比例，融券持仓 -> 融券比例
    const checkRzbzj = record.toreturnprice * record.fi_margin;

    // 融资浮盈 = (两融数量 * 最新价 - 未还本金) * 折算率
    // 括号里是正数，就去取titdsvr给你的折算率，括号是负数，折算率为1
    const checkNum = record.lrvolume * record.lastprice - record.toreturnprice;
    const collDisc = checkNum > 0 ? record.coll_disc : 1
    const checkRzyl = checkNum * collDisc;

    // 可用保证金 = 充抵保证金 + 融资盈亏 - 两融保证金 - 未还手续费 - 未还利息
    const checkAllmargin = checkKcdzqsz + checkRzyl - checkRzbzj - record.returncommi - record.interest;

    const newRecord = {
      ...record,
      volume: formatDot(record.volume, 2, 2),
      lrvolume: formatDot(record.lrvolume, 2, 2),
      lastprice: formatDot(record.lastprice, 4, 4),
      coll_disc: formatDot(record.coll_disc, 2, 2),
      toreturnprice: formatDot(record.toreturnprice, 2, 2),
      fi_margin: formatDot(record.fi_margin, 2, 2),
      sl_margin: formatDot(record.sl_margin, 2, 2),
      kcdzqsz: formatDot(record.kcdzqsz, 2, 2),
      rzbzj: formatDot(record.rzbzj, 2, 2),
      rzyl: formatDot(record.rzyl, 2, 2),
      returncommi: formatDot(record.returncommi, 2, 2),
      interest: formatDot(record.interest, 2, 2),
      allmargin: formatDot(record.allmargin, 2, 2),
      checkKcdzqsz: formatDot(checkKcdzqsz, 2, 2),
      collDisc: formatDot(collDisc, 2, 2),
      checkRzbzj: formatDot(checkRzbzj, 2, 2),
      checkRzyl: formatDot(checkRzyl, 2, 2),
      checkAllmargin: formatDot(checkAllmargin, 2, 2),
    }

    drawerForm.current.show('保证金公式明细查询', newRecord);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.pos.positiondtl', {
        defaultMsg: '持仓详情',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        onSearch: onSearch,
        onReset: onReset,
        isLoading: isLoading,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    {/* 保证金计算公式 */}
    <DrawerForm
      isGroup
      ref={drawerForm}
      formData={createCalcForm}
    />
  </>);
}

const PositionDtl = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <PositionDtlBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { PositionDtl };
