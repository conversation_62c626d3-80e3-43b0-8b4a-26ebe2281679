import { useRef, useState, useMemo } from 'react';
import { useParams } from 'react-router';
import { Card, Typography } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount, useDebounceFn } from 'ahooks';

import {
  TiTable,
  TableFields,
  forms,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  Request,
  dicts,
  tools,
  getList,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot } = TableFields;
const { QueryFilter } = forms;
const { accountTypeDict, clientTypeDict, exchangeAllDict, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, MESSAGE_TYPE, SITE_URL, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getMemUserList, getUserClientList } = getList;

const ClientAccountBase = props => {
  const { paramId } = props;

  const queryForm = useRef(null);

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request] = useFetch(SITE_URL.BASE);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: '资金账户',
    dataIndex: 'accountid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: '资金账户类型',
    dataIndex: 'accounttype',
    render: text => TableBadgeDot(text, accountTypeDict, tagNormalColors, true),
  }, {
    title: '交易编码',
    dataIndex: 'clientid',
  }, {
    title: '交易编码类型',
    dataIndex: 'clienttype',
    render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: '交易所',
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }];

  const [userOptions, setUserOptions] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);

  const createSearchForm = useMemo(() => searchForm(userOptions, clientOptions), [userOptions, clientOptions]);

  useMount(() => {
    getMemUserList(Request.post, defaultParams, setUserOptions, paramId);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryClientAccount, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: params.beginno + item.rspseqno - 1,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: defaultParams,
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    // 去除用户
    delete values.userid;

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const { run: clientRun } = useDebounceFn(async (value) => {
    let clientDefault = null;
    if (value) {
      clientDefault = await getUserClientList(Request.post, defaultParams, setClientOptions, value);
    } else {
      setClientOptions([]);
    }

    // 交易编码赋值
    queryForm.current?.set({
      'clientid': clientDefault,
    });
  }, { wait: 500 });
  const formValueChange = changedValues => {
    if ('userid' in changedValues) {
      const value = changedValues.userid;
      clientRun(value);
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <div className="ti-table-search">
      <QueryFilter
        ref={queryForm}
        formData={createSearchForm}
        onSearch={onSearch}
        onReset={onReset}
        isLoading={loading}
        onValuesChange={formValueChange}
      />
    </div>
    <Card>
      <TiTable
        columns={columns}
        dataSource={dataSource}
        isLoading={loading}
        onRefresh={onRefresh}
        checkable={false}
        page={page}
        total={total}
        pageChange={pageChange}
      />
    </Card>
  </>);
}

const ClientAccount = props => {
  const param = useParams();

  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <ClientAccountBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { ClientAccount };
