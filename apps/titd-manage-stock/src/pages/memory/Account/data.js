import { consts } from '@titd/publics/utils';

const { DEFAULT_CONFIGS } = consts;

export const searchForm = ($t, accountGroup) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountGroup,
  }
}];

const marginForm = $t => [{
  valueType: 'text',
  name: 'futmargin',
  label: $t('app.stock.account.availusedmargin'),
}, {
  valueType: 'text',
  name: 'allmargin',
  label: $t('app.stock.account.allmargin'),
}];

const financeForm = $t => [{
  valueType: 'text',
  name: 'optmargin',
  label: $t('app.stock.trade.premium'),
}, {
  valueType: 'text',
  name: 'combmargin',
  label: $t('app.stock.position.kcdzqsz'),
}, {
  valueType: 'text',
  name: 'frozencombmargin',
  label: $t('app.stock.account.frozencombmargin'),
}, {
  valueType: 'text',
  name: 'entryfees',
  label: $t('app.stock.position.rzbzj'),
}, {
  valueType: 'text',
  name: 'frozenmargin1',
  label: $t('app.stock.account.forzenenrzbzj'),
}, {
  valueType: 'text',
  name: 'posiprofit',
  label: $t('app.stock.position.rzyl'),
}, {
  valueType: 'text',
  name: 'frozenmargin',
  label: $t('app.stock.position.rqfz'),
}, {
  valueType: 'text',
  name: 'interest',
  label: $t('app.stock.account.interest'),
}, {
  valueType: 'text',
  name: 'xycommi',
  label: $t('app.stock.account.xycommi'),
}, {
  valueType: 'text',
  name: 'forzenxycommi',
  label: $t('app.stock.account.forzenxycommi'),
}, {
  valueType: 'text',
  name: 'respstr1',
  label: $t('app.stock.account.respstr1'),
}, {
  valueType: 'text',
  name: 'respstr2',
  label: $t('app.stock.account.respstr2'),
}, {
  valueType: 'text',
  name: 'respstr3',
  label: $t('app.stock.account.respstr3'),
}, {
  valueType: 'text',
  name: 'respstr4',
  label: $t('app.stock.account.respstr4'),
}, {
  valueType: 'hidden',
  name: 'xycommi',
  noStyle: true,
}];

export const addForm = ($t, isModify) => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.options.accountid'),
}, {
  valueType: 'text',
  name: 'prebalance',
  label: $t('app.options.mem.account.prebalance'),
}, {
  valueType: 'text',
  name: 'distribfund',
  label: $t('app.options.mem.account.distribfund'),
}, {
  valueType: 'text',
  name: 'availusedfund',
  label: $t('app.options.mem.account.availusedfund'),
}, ...(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? marginForm($t) : [{
  valueType: 'text',
  name: 'premium',
  label: $t('app.stock.trade.premium'),
}]), {
  valueType: 'number',
  name: 'commi',
  label: $t('app.options.mem.account.commi'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify
  }
}, {
  valueType: 'number',
  name: 'closeprofit',
  label: $t('app.options.mem.account.closeprofit'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify,
    min: Number.MIN_SAFE_INTEGER
  }
}, {
  valueType: 'text',
  name: 'deposit',
  label: $t('app.options.mem.account.deposit'),
}, {
  valueType: 'text',
  name: 'withdraw',
  label: $t('app.options.mem.account.withdraw'),
}, {
  valueType: 'number',
  name: 'frozenpremium',
  label: $t('app.stock.trade.frozenpremium'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify,
    min: Number.MIN_SAFE_INTEGER
  }
}, {
  valueType: 'number',
  name: 'frozencommi',
  label: $t('app.options.mem.account.frozencommi'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify,
    min: Number.MIN_SAFE_INTEGER
  }
}, ...(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? financeForm($t) : [])].filter(i => i);

export const amountForm = ($t, digitUpper) => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.options.accountid'),
}, {
  valueType: 'plain',
  name: 'availusedfund',
  label: $t('app.options.mem.account.availusedfund'),
  fieldProps: {
    style: {
      fontSize: '20px',
      lineHeight: '20px',
      color: '#0081cc',
    }
  }
}, {
  valueType: 'plain',
  name: 'deposit',
  label: $t('app.options.mem.account.deposited'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#52c41a',
    }
  }
}, {
  valueType: 'plain',
  name: 'withdraw',
  label: $t('app.options.mem.account.withdrawed'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#ff4d4f',
    }
  }
}, {
  valueType: 'number',
  name: 'amount',
  label: $t('app.options.mem.account.amount'),
  extra: digitUpper,
  rules: [{ required: true }],
}];

export const calcForm = $t => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.options.accountid'),
}, {
  valueType: 'text',
  name: 'availusedfund',
  label: $t('app.options.mem.account.availusedfund'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'combmargin',
  label: '+ ' + $t('app.stock.position.kcdzqsz'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'frozencombmargin',
  label: '+ ' + $t('app.stock.account.frozencombmargin'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'posiprofit',
  label: '+ ' + $t('app.stock.position.rzyl'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'rqyl',
  label: '+ ' + $t('app.stock.position.rqyl'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'frozenmargin',
  label: '- ' + $t('app.stock.position.rqfz'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'entryfees',
  label: '- ' + $t('app.stock.position.rzbzj'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'frozenmargin1',
  label: '- ' + $t('app.stock.account.forzenenrzbzj'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'interest',
  label: '- ' + $t('app.stock.account.interest'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'xycommi',
  label: '- ' + $t('app.stock.account.xycommi'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'forzenxycommi',
  label: '- ' + $t('app.stock.account.forzenxycommi'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}, {
  valueType: 'text',
  name: 'calcmargin',
  label: '= ' + $t('app.stock.account.calc') + ' ' + $t('app.stock.account.allmargin'),
  fieldProps: {
    style: { textAlign: 'right', fontWeight: 'bold' }
  }
}, {
  valueType: 'text',
  name: 'allmargin',
  label: $t('app.stock.account.allmargin'),
  fieldProps: {
    style: { textAlign: 'right' }
  }
}];
