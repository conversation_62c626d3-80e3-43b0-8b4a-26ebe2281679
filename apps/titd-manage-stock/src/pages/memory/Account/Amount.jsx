import { useRef, useState, useEffect, useMemo } from 'react';
import { Card, Spin, Space, Button, App } from 'antd';
import {
  ImportOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  TableSimple,
  TableFields,
  forms,
} from '@titd/publics/components';
import { amountForm } from './data';

import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
  currency,
  getList,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn, TableTag } = TableFields;
const { SimpleForm } = forms;
const { transferStatusDict, transferStatusColor } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;
const { digitUppercase, formatDot } = currency;
const { getClusterList } = getList;

const Amount = props => {
  const {
    params,
    record,
    onSuccess,
  } = props;

  const aForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [serverMap, setServerMap] = useState({});
  const [oldAccountid, setOldAccountid] = useState();
  const [respData, setRespData] = useState([]);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const respColumns = [{
    dataIndex: 'transferno',
    title: $t('app.stock.account.transferno'),
    width: 80,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    dataIndex: 'sourceclusterid',
    title: $t('app.stock.account.source'),
    render: text => serverMap[text] || text,
  }, {
    dataIndex: 'destclusterid',
    title: $t('app.stock.account.dest'),
    render: text => serverMap[text] || text,
  }, {
  //   dataIndex: 'amount',
  //   title: $t('app.options.mem.account.amount'),
  //   align: 'right',
  //   render: text => NegaNumber(text),
  // }, {
    dataIndex: 'status',
    title: $t('app.stock.account.status'),
    render: text => text ? TableTag(text + 1, transferStatusDict, transferStatusColor) : '',
  }, {
    dataIndex: 'responsestr',
    title: $t('app.stock.account.logmessage'),
  }];

  const amountType = useMemo(() => {
    return [
      ['withdraw', $t('app.general.withdraw')],
      ['deposit', $t('app.general.deposit')],
    ];
  }, [$t]);

  const [digitUpper, setDigitUpper] = useState('');
  const createAmountForm = useMemo(() => amountForm($t, digitUpper), [$t, digitUpper]);

  useMount(() => {
    getClusterList(Request.post, params, null, navigate, setServerMap);
  });
  useUnmount(() => cancel());

  useEffect(() => {
    if (record) {
      if (record.accountid !== oldAccountid) {
        setOldAccountid(record.accountid);
        setRespData([]);
      }

      transferReset();

      const newRecord = {
        ...record,
        availusedfund: formatDot(record.availusedfund, 2, 2),
        deposit: formatDot(record.deposit, 2),
        withdraw: formatDot(record.withdraw, 2),
      }

      aForm.current.set(newRecord);
    }
  }, [record]); // eslint-disable-line react-hooks/exhaustive-deps

  const transferReset = () => {
    aForm.current.reset();
    setDigitUpper('');
  }

  const formValueChange = changedValues => {
    if ('amount' in changedValues) {
      const value = changedValues.amount;
      if (value !== '') {
        setDigitUpper(digitUppercase(value));
      } else {
        setDigitUpper('');
      }
    }
  }

  const amountSubmit = type => {
    aForm.current.submit(async values => {
      // console.log(values, type);
      const amount = amountType[type];
      if (amount) {
        const postData = {
          ...params,
          accountid: values.accountid,
          [amount[0]]: values.amount,
        };

        fetchFunc(MESSAGE_TYPE.DepositWithdraw, postData, resp => {
          if (resp.errcode) {
            message.error(resp.errmessage);
          } else {
            if (resp?.length > 0) {
              const tableData = resp.map((item, idx) => ({
                ...item,
                id: idx + 1,
              }));
              setRespData(tableData);
            } else {
              setRespData([]);
            }
            onSuccess && onSuccess();
          }
        });

        // const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.DepositWithdraw, params?.svrid), postData);

        // // 特别错误处理
        // if (resp[0]?.responsecode && resp[0]?.responsecode !== 0) {
        //   message.error(resp[0].responsestr);
        //   return;
        // }

        // message.success(resp[0]?.responsestr || amount[1] + CURD.StatusOkMsg);
        // onSuccess && onSuccess();
      }
    });
  }

  const { loading, run, cancel } = useRequest(amountSubmit, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    }
  });
  return (<>
    <Spin spinning={loading} tip="划转中……">
      <Card className="ti-table-content">
        <SimpleForm
          ref={aForm}
          formData={createAmountForm}
          onValuesChange={formValueChange}
          formStyle={{ maxWidth: '600px' }}
          customBtn={<Space>
            <Button
              size="large"
              color="cyan"
              variant="solid"
              disabled={loading}
              icon={<ImportOutlined />}
              onClick={() => run(1)}
            >
              <span>{$t('app.general.deposit')}</span>
            </Button>

            <Button
              size="large"
              color="orange"
              variant="solid"
              disabled={loading}
              icon={<ExportOutlined />}
              onClick={() => run(0)}
            >
              <span>{$t('app.general.withdraw')}</span>
            </Button>
          </Space>}
        />
      </Card>
    </Spin>

    {respData.length > 0 && (
      <Card className="ti-table-content">
        <TableSimple
          columns={respColumns}
          dataSource={respData}
          isLoading={loading}
        />
      </Card>
    )}
  </>);
}

export default Amount;
