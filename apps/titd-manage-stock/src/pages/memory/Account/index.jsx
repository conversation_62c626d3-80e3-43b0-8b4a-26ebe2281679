import { useRef, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, Space, Button, App } from 'antd';
import {
  EditOutlined,
  CloseOutlined,
  CheckOutlined,
} from '@ant-design/icons';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
  exportDownModal,
} from '@titd/publics/components';
import { searchForm, addForm, calcForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  currency,
  errorResp,
  getList,
} from '@titd/publics/utils';

import Amount from './Amount';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableTitle } = TableFields;
const { DrawerForm, ModalForm } = forms;
const { detailLink, showCalcLink } = links;
const { exportAllBtn } = btns;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList } = getList;
const { getFloat, formatDot } = currency;

const AccountBase = props => {
  const { paramId } = props;

  const drawerForm = useRef(null);
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([1]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [oriDataSource, setOriDataSource] = useState([]);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const marginColumns = [{
    title: TableTitle(
      $t('app.stock.account.allprice'),
      $t('app.stock.account.allprice.long'),
    ),
    dataIndex: 'allprice',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.availusedfund'),
    dataIndex: 'availusedfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.account.allmargin'),
      $t('app.stock.account.allmargin.long'),
    ),
    dataIndex: 'allmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.account.allamt'),
    dataIndex: 'allamt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.account.respstr1'),
    dataIndex: 'respstr1',
    align: 'right',
    // render: text => NegaNumber(text, 2, 2),
    render: text => NegaNumber(text, 6, 6),
  }, {
    title: $t('app.stock.account.respstr2'),
    dataIndex: 'respstr2',
    align: 'right',
    render: text => NegaNumber(text, 3, 3),
  }, {
    title: TableTitle(
      $t('app.stock.account.availusedmargin'),
      $t('app.stock.account.availusedmargin.long'),
    ),
    dataIndex: 'futmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }];

  const financeColumns = [{
  //   title: intl.formatMessage({ id: 'app.options.mem.account.frozenmargin' }),
  //   dataIndex: 'frozenmargin1',
  //   align: 'right',
  //   render: text => NegaNumber(text, 2, 2),
  // }, {
    title: $t('app.stock.trade.premium'),
    dataIndex: 'optmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.position.kcdzqsz'),
      $t('app.stock.account.kcdzqsz.long'),
    ),
    dataIndex: 'combmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.account.frozencombmargin'),
    dataIndex: 'frozencombmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.position.rzbzj'),
      $t('app.stock.account.rzbzj.long'),
    ),
    dataIndex: 'entryfees',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.account.forzenenrzbzj'),
    dataIndex: 'frozenmargin1',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.position.rzyl'),
    dataIndex: 'posiprofit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.position.rqyl'),
    dataIndex: 'rqyl',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
  //   title: TableTitle(
  //     intl.formatMessage({ id: 'app.stock.position.rqzj' }),
  //     intl.formatMessage({ id: 'app.stock.position.rqzj.long' })
  //   ),
  //   dataIndex: 'buypremium',
  //   align: 'right',
  //   render: text => NegaNumber(text, 2, 2),
  // }, {
  //   title: TableTitle(
  //     intl.formatMessage({ id: 'app.stock.position.rqyl' }),
  //     intl.formatMessage({ id: 'app.stock.position.rqyl.long' }),
  //   ),
  //   dataIndex: 'premium',
  //   align: 'right',
  //   render: text => NegaNumber(text, 2, 2),
  // }, {
    title: $t('app.stock.position.rqfz'),
    align: 'right',
    dataIndex: 'frozenmargin',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.account.interest'),
      $t('app.stock.cdposition.pendrepayint'),
    ),
    dataIndex: 'interest',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.account.xycommi'),
    dataIndex: 'xycommi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.account.forzenxycommi'),
    dataIndex: 'forzenxycommi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.account.respstr3'),
      $t('app.stock.account.respstr3.long'),
    ),
    dataIndex: 'respstr3',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.account.respstr4'),
    dataIndex: 'respstr4',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.account.contremainamt'),
      $t('app.stock.account.contremainamt.long'),
    ),
    dataIndex: 'contRemainAmt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 190,
    render: (_, record) => (
      <ActionLinks links={[
        detailLink(toDetail, record),
        showCalcLink(toShowCalc, record),
      ]} />
    )
  }];

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.mem.account.prebalance'),
    dataIndex: 'prebalance',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.distribfund'),
    dataIndex: 'distribfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, ...(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? marginColumns : [{
    title: $t('app.options.mem.account.availusedfund'),
    dataIndex: 'availusedfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.trade.premium'),
    dataIndex: 'premium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }]), {
    title: $t('app.options.mem.account.commi'),
    dataIndex: 'commi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.closeprofit'),
    dataIndex: 'closeprofit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.deposit'),
    dataIndex: 'deposit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.withdraw'),
    dataIndex: 'withdraw',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.trade.frozenpremium'),
    dataIndex: 'frozenpremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'frozencommi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, ...(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? financeColumns : [{
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ID,
    render: (_, record) => (
      <ActionLinks links={[
        detailLink(toDetail, record),
      ]} />
    )
  }])].filter(i => i);

  const [accountGroup, setAccountGroup] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const createSearchForm = useMemo(() => searchForm($t, accountGroup), [$t, accountGroup]);
  const createAddForm = useMemo(() => addForm($t, isModify), [$t, isModify]);
  const createCalcForm = useMemo(() => calcForm($t), [$t]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountGroup);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryAccount, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => {
        const itemData = {
          ...item,
          id: item.rspseqno,
          prebalance: getFloat(item.prebalance, 4),
          distribfund: getFloat(item.distribfund, 4),
          availusedfund: getFloat(item.availusedfund, 4),
          commi: getFloat(item.commi, 4),
          closeprofit: getFloat(item.closeprofit, 4),
          deposit: getFloat(item.deposit, 4),
          withdraw: getFloat(item.withdraw, 4),
          frozenpremium: getFloat(item.frozenpremium, 4),
          frozencommi: getFloat(item.frozencommi, 4),
        }

        if (DEFAULT_CONFIGS.SHOW_MARGINFINANCING && item.responsestr) {
          const respstr = item.responsestr.split('|').map(i => Number(i));

          return {
            ...itemData,
            futmargin: getFloat(item.futmargin, 4),
            posiprofit: getFloat(item.posiprofit, 4),
            entryfees: getFloat(item.entryfees, 4),
            combmargin: getFloat(item.combmargin, 4),
            // 可用保证金
            allmargin: getFloat(item.availusedfund + item.futmargin, 4),
            interest: respstr[0],
            frozenmargin1: respstr[1],
            xycommi: respstr[2],
            forzenxycommi: respstr[3],
            // 9999 / 10001 -> '> 1,000,000%'
            respstr1: respstr[4] >= 9999 ? '> 1,000,000%' : respstr[4] * 100,
            respstr2: respstr[5],
            respstr3: respstr[6],
            respstr4: respstr[7],
            contRemainAmt: respstr[8],
            rqyl: respstr[9],
            frozencombmargin: respstr[10],
            allamt: respstr[8] + respstr[0] + respstr[2],
            allprice: item.availusedfund + respstr[5] + item.frozenpremium + item.frozencommi,
          }
        }
        return itemData;
      });

      setTotal(respData[0].rsptotnum);
      // setSelectKeys([1]);
      setOriDataSource(respData);
      return tableData;
    } else {
      setTotal(0);
      setSelectKeys([]);
      setOriDataSource([]);
      return [];
    }
  }

  const {
    data: dataSource = [],
    params: prevParams,
    loading,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    }
  });

  const selectRecord = useMemo(() => {
    if (selectKeys.length > 0 && dataSource.length > 0) {
      return dataSource.find(i => i.id === selectKeys[0]);
    }

    return null;
  }, [selectKeys, dataSource]);

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const toDetail = async record => {
    // console.log('Detail', record);
    drawerForm.current.show(record.accountid, record);
  }
  const toCancel = () => {
    drawerForm.current.set(selectRecord);
    setIsModify(false);
  }
  const toSure = () => {
    drawerForm.current.submit(addSubmit);
    setIsModify(false);
  }
  const toShowCalc = record => {
    // console.log('Calc', record);
    // 计算可用保证金，用于验证
    const calcMargin = record.availusedfund + record.combmargin + record.frozencombmargin + record.posiprofit + record.rqyl - record.frozenmargin - record.entryfees - record.frozenmargin1 - record.interest - record.xycommi - record.forzenxycommi;

    const newRecord = {
      ...record,
      availusedfund: formatDot(record.availusedfund, 2, 2),
      combmargin: formatDot(record.combmargin, 2, 2),
      frozencombmargin: formatDot(record.frozencombmargin, 2, 2),
      posiprofit: formatDot(record.posiprofit, 2, 2),
      rqyl: formatDot(record.rqyl, 2, 2),
      frozenmargin: formatDot(record.frozenmargin, 2, 2),
      entryfees: formatDot(record.entryfees, 2, 2),
      frozenmargin1: formatDot(record.frozenmargin1, 2, 2),
      interest: formatDot(record.interest, 2, 2),
      xycommi: formatDot(record.xycommi, 2, 2),
      forzenxycommi: formatDot(record.forzenxycommi, 2, 2),
      allmargin: formatDot(record.allmargin, 2, 2),
      calcmargin: formatDot(calcMargin, 2, 2),
    }

    modalForm.current.show('保证金公式明细查询', newRecord);
  }

  const toExport = () => {
    fetchFunc(MESSAGE_TYPE.DownloadAccount, defaultParams, resp => {
      // console.log(resp);
      if (resp.errmessage) {
        exportDownModal(resp.errmessage);
      } else {
        message.error('导出失败');
      }
    });
  }

  const addSubmit = async form => {
    const findItem = oriDataSource.find(i => i.accountid === form.accountid);
    if (findItem) {
      const postData = {
        ...defaultParams,
        ...findItem,
        commi: form.commi,
        closeprofit: form.closeprofit,
        frozenpremium: form.frozenpremium,
        frozencommi: form.frozencommi,
      }

      delete postData.rsptotnum;
      delete postData.rspseqno;
      delete postData.islast;
      delete postData.msgtype;

      // 两融个别字段特殊处理
      if (DEFAULT_CONFIGS.SHOW_MARGINFINANCING) {
        postData.entryfees = form.interest;
        postData.combmargin = form.xycommi;
        delete postData.responsestr;
      }

      fetchFunc(MESSAGE_TYPE.MemUpdAccount, postData, () => {
        message.success(CURD.Update + CURD.StatusOkMsg);
        onRefresh();
      });
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.user.account', {
        defaultMsg: '资金信息',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          exportAllBtn(toExport),
        ],
        onRefresh: onRefresh,
        page: page,
        total: total,
        pageChange: pageChange,
        rowClickable: true,
        selectionType: 'radio',
        selectKeys: selectKeys,
        setSelectKeys: (keys) => {
          setSelectKeys(keys);
        },
      }}
    />

    {/* 出入金 */}
    {selectRecord && (
      <Amount
        params={defaultParams}
        record={selectRecord}
        onSuccess={onRefresh}
      />
    )}

    {/* 资金信息修改 */}
    <DrawerForm
      ref={drawerForm}
      formData={createAddForm}
      onClose={() => setIsModify(false)}
    >
      <div style={{
        borderTop: '1px solid #eee',
        paddingTop: '20px',
        textAlign: 'center'
      }}>
        {isModify ? (
          <Space>
            <Button danger onClick={toCancel}><CloseOutlined /> {$t('app.general.cancel')}</Button>
            <Button type="primary" onClick={toSure}><CheckOutlined /> {$t('app.general.sure')}</Button>
          </Space>
        ) : (
          <Button type="primary" ghost onClick={() => setIsModify(true)}><EditOutlined /> {$t('app.general.update')}</Button>
        )}
      </div>
    </DrawerForm>

    {/* 保证金计算公式 */}
    <ModalForm
      ref={modalForm}
      width={480}
      formData={createCalcForm}
    />
  </>);
}

const Account = props => {
  const param = useParams();

  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <AccountBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Account };
