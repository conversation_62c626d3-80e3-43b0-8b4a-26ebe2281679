import { useRef, useEffect, useState, useMemo } from 'react';
import { Row, Col, Divider, Form, Button, message } from 'antd';
import {
  ImportOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import useFetch from 'use-http';
import { useIntl } from 'react-intl';

import { MESSAGE_TYPE, SITE_URL, CURD } from '@utils/consts';
import { SimpleForm } from '@components/form';
import { amountForm } from './data';
import { digitUppercase } from '@utils/currency';

const InnerForm = props => {
  const {
    params,
    record,
    onSuccess,
  } = props;

  const depositForm = useRef();
  const withdrawForm = useRef();
  const intl = useIntl();

  const { post, loading, abort } = useFetch(SITE_URL.BASE);

  const amountType = useMemo(() => {
    return [
      ['withdraw', intl.formatMessage({ id: 'app.general.withdraw' })],
      ['deposit', intl.formatMessage({ id: 'app.general.deposit' })],
    ];
  }, [intl]);

  const [depositUpper, setDepositUpper] = useState('');
  const [withdrawUpper, setWithdrawUpper] = useState('');
  const createDepositForm = useMemo(() => amountForm(intl, depositUpper, intl.formatMessage({ id: 'app.general.deposit' })), [intl, depositUpper]);
  const createWithdrawForm = useMemo(() => amountForm(intl, withdrawUpper, intl.formatMessage({ id: 'app.general.withdraw' })), [intl, withdrawUpper]);

  useEffect(() => {
    return () => {
      abort();
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const depositFormChange = (changedValues, values) => {
    if ('amount' in changedValues) {
      const value = changedValues.amount;
      if (value) {
        setDepositUpper(digitUppercase(value));
      } else {
        setDepositUpper('');
      }
    }
  };
  const withdrawFormChange = (changedValues, values) => {
    if ('amount' in changedValues) {
      const value = changedValues.amount;
      if (value !== '') {
        setWithdrawUpper(digitUppercase(value));
      } else {
        setWithdrawUpper('');
      }
    }
  };

  const amountSubmit = async (form, type) => {
    const amount = amountType[type];
    if (amount) {
      const postData = {
        ...params,
        accountid: record?.accountid,
        [amount[0]]: form.amount,
      };
      // console.log(postData, type);

      const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.DepositWithdraw, params?.svrid), postData);
      // 特别错误处理
      if (resp[0]?.responsecode && resp[0]?.responsecode !== 0) {
        message.error(resp[0].responsestr);
        return;
      }

      message.success(resp[0]?.responsestr || amount[1] + CURD.StatusOkMsg);
      onSuccess && onSuccess();

      // fetchFunc(MESSAGE_TYPE.DepositWithdraw, postData, (resp) => {
      //   // 特别错误处理
      //   if (resp[0]?.responsecode && resp[0]?.responsecode !== 0) {
      //     message.error(resp[0].responsestr);
      //     return;
      //   }

      //   message.success(resp[0]?.responsestr || amount[1] + CURD.StatusOkMsg);
      // });
    }
  }

  return (<>
    <Divider orientation="left" plain>
      {intl.formatMessage({ id: 'app.options.mem.account.depositwithdraw' })}
    </Divider>

    <Row gutter={[16, 24]}>
      <Col xs={24}>
        {/* 入金 */}
        <SimpleForm
          ref={depositForm}
          formData={createDepositForm}
          formLayout="inline"
          onValuesChange={depositFormChange}
          onFinish={form => amountSubmit(form, 1)}
        >
          <Form.Item>
            <Button
              className="ti-btn-info"
              htmlType="submit"
              loading={loading}
              icon={<ImportOutlined />}
            >{intl.formatMessage({ id: 'app.general.deposit' })}</Button>
          </Form.Item>
        </SimpleForm>
      </Col>
      <Col xs={24}>
        {/* 出金 */}
        <SimpleForm
          ref={withdrawForm}
          formData={createWithdrawForm}
          formLayout="inline"
          onValuesChange={withdrawFormChange}
          onFinish={form => amountSubmit(form, 0)}
        >
          <Button
            className="ti-btn-warning"
            htmlType="submit"
            loading={loading}
            icon={<ExportOutlined />}
          >{intl.formatMessage({ id: 'app.general.withdraw' })}</Button>
        </SimpleForm>
      </Col>
    </Row>
  </>);
}

export default InnerForm;
