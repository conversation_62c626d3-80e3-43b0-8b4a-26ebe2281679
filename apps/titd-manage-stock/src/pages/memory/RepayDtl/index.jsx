import { useRef, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
  dateFormat,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, NegaNumber } = TableFields;
const { exchangeAllDict, repayTypeDict, debtsTypeDict, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList } = getList;
const { formatDate, formatTime } = dateFormat;

const RepayDtlBase = props => {
  const { paramId } = props;

  const queryForm = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.stock.repay.orderid'),
    dataIndex: 'orderId',
  }, {
    title: $t('app.stock.repay.type'),
    dataIndex: 'repayType',
    render: text => repayTypeDict[String.fromCharCode(text)] || text,
  }, {
    title: $t('app.stock.repay.contrddate'),
    dataIndex: 'conTrdDate',
    render: text => formatDate(text),
  }, {
    title: $t('app.stock.repay.orderno'),
    dataIndex: 'conOrderId',
  }, {
    title: $t('app.stock.repay.firstmatched'),
    dataIndex: 'firstMatched',
    render: text => formatTime(text) || '-',
  }, {
    title: $t('app.stock.repay.rltrepay'),
    dataIndex: 'rltrepay',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.repay.debtstype'),
    dataIndex: 'debtstype',
    render: text => debtsTypeDict[String.fromCharCode(text)] || text,
  }, {
    title: $t('app.stock.repay.time'),
    dataIndex: 'repaytime',
  }];

  const [accountGroup, setAccountGroup] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, accountGroup), [$t, accountGroup]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountGroup);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    if (!params) {
      return [];
    };

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryRepayDetail, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
        rltrepay: item.rltRepayAmt || item.rltRepayInt,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.cdpos.repaydtl', {
        defaultMsg: '还款明细',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        onSearch: onSearch,
        onReset: onReset,
        isLoading: isLoading,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const RepayDtl = props => {
  const param = useParams();
  const paramId = param?.id || '';
  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <RepayDtlBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { RepayDtl };
