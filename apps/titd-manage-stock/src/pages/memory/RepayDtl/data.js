import { dicts, formOptions } from '@titd/publics/utils';

const { repayTypeDict, debtsTypeDict } = dicts;
const { selectOptions, selectObjOptions } = formOptions;

export const searchForm = ($t, accountGroup, isRequired = true) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  rules: [{ required: isRequired }],
  fieldProps: {
    options: accountGroup,
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'select',
  name: 'repayType',
  label: $t('app.stock.repay.type'),
  fieldProps: {
    options: selectOptions(repayTypeDict, { isCode: true }),
  }
}, {
  valueType: 'select',
  name: 'debtstype',
  label: $t('app.stock.repay.debtstype'),
  fieldProps: {
    options: selectObjOptions(debtsTypeDict, { isCode: true }),
  }
}, {
  valueType: 'input',
  name: 'orderId',
  label: $t('app.stock.repay.orderid'),
}, {
  valueType: 'input',
  name: 'conOrderId',
  label: $t('app.stock.repay.orderno'),
}];
