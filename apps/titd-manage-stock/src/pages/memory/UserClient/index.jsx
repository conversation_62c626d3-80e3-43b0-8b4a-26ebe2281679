import { useRef, useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount, useDebounceFn } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { searchForm, chgStatusForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  optDiff,
  optDiffTwo,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTag, TableBadgeDot } = TableFields;
const { ModalForm } = forms;
const { chgStatusLink } = links;
const { accountTypeDict, exchangeAllDict, clientStatusDict, clientRightDict, isLimitDict, yesNoDict, tagNormalColors, tagColors, yesNoColor } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientGroup, getClientByUser } = getList;

const MemUserClientBase = props => {
  const { paramId } = props;

  const navigate = useNavigate();
  const modalForm = useRef(null);
  const queryForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = useMemo(() => exchangeList && exchangeList[paramId], [exchangeList, paramId]);
  const defaultExchange = exchanges?.length === 1 ? Number(exchanges[0]) : undefined;

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid'
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.clientno}]`}
    </>,
  }, {
    title: $t('app.options.accounttype'),
    dataIndex: 'accounttype',
    render: text => TableBadgeDot(text, accountTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.bizpbu'),
    dataIndex: 'bizpbu',
  }, {
    title: $t('app.options.salesnumb'),
    dataIndex: 'salesnumb',
  }, {
    title: $t('app.stock.userclient.ordernum'),
    dataIndex: 'ordernum',
    align: 'right',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.mem.userclient.status'),
    dataIndex: 'status',
    render: text => TableTag(text, clientStatusDict, tagColors),
  }, {
    title: $t('app.options.rightid'),
    dataIndex: 'rights',
    render: text => rightFunc(text)
  }, {
    title: $t('app.stock.userclient.islimit'),
    dataIndex: 'isLimit',
    render: text => TableTag(text, isLimitDict, yesNoColor)
  }, DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? {
    title: $t('app.stock.userclient.return'),
    dataIndex: 'right6',
    render: text => TableTag(text, yesNoDict, yesNoColor)
  } : null, {
  //   title: '检查自成交',
  //   dataIndex: 'right1',
  //   render: text => TableTag(text, yesNoDict, yesNoColor)
  // }, {
  //   title: '行权日不能开义务仓',
  //   dataIndex: 'right2',
  //   render: text => TableTag(text, yesNoDict, yesNoColor)
  // }, {
  //   title: '禁止行权',
  //   dataIndex: 'right3',
  //   render: text => TableTag(text, yesNoDict, yesNoColor)
  // }, {
  //   title: '禁止组合行权',
  //   dataIndex: 'right4',
  //   render: text => TableTag(text, yesNoDict, yesNoColor)
  // }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 100,
    render: (_, record) => record.status !== 3 && (
      <ActionLinks links={[
        chgStatusLink(toChgStatus, record),
      ]} />
    )
  }].filter(i => i);

  const rightFunc = text => {
    return text && text?.map((item, idx) => {
      // return (<Tag key={idx}>{clientRightDict[item] || item}</Tag>);
      if (item === 1) {
        return (
          <span key={idx}>
            {TableTag(idx + 1, clientRightDict, tagNormalColors, true)}
          </span>
        );
      } else {
        return null;
      }
    }).filter(i => i);
  }

  const [userOptions, setUserOptions] = useState([]);
  const [userClientGroup, setUserClientGroup] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientGroup(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userOptions, false, clientOptions, exchanges), [$t, userFocus, userOptions, clientOptions, exchanges]);
  const createChgStatusForm = useMemo(() => chgStatusForm($t), [$t]);

  useMount(() => {
    // 默认先载入一次
    getUserClientGroup(Request.post, defaultParams, setUserClientGroup);
  });
  useUnmount(() => cancel());

  useEffect(() => {
    if (userClientGroup.length > 0) {
      const users = optDiffTwo(userClientGroup);
      setUserOptions(users);

      const clients = optDiff(userClientGroup, 'clientid');
      setClientOptions(clients);
    } else {
      setUserOptions([]);
      setClientOptions([]);
    }
  }, [userClientGroup]);

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryUserClient, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => {
        const arr = item.bizpbu.split(',');

        return {
          ...item,
          id: item.rspseqno,
          bizpbu: arr[0],
          ordernum: arr[1] || 0,
          isLimit: arr[2] || 0,
          rights: [item.right1, item.right2, item.right3, item.right4],
          right6: arr[3] || 0,
        };
      });

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      exchangeid: defaultExchange,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }
    // 重置股东代码
    clientRun('');

    run(defaultParams);
  }

  let oldRecord = null;
  const toChgStatus = record => {
    // console.log(record);
    oldRecord = record;
    modalForm.current.show('更改交易状态', record);
  }

  const chgSubmit = form => {
    // console.log(form, oldRecord);
    // 业务PBU，营业部代码无更改不需要提交
    if (form.bizpbu === oldRecord.bizpbu) {
      delete form.bizpbu;
    }
    if (form.salesnumb === oldRecord.salesnumb) {
      delete form.salesnumb;
    }
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(MESSAGE_TYPE.MemUpdClientStatus, postData, resp => {
      const respData = resp[0];
      if (respData.responsecode && respData.responsecode === -1) {
        message.error(respData.responsestr);
      } else {
        message.success(CURD.Update + CURD.StatusOkMsg);
        onRefresh();
      }
    });
  }

  const { run: clientRun } = useDebounceFn(async value => {
    if (value) {
      const clientGroup = getClientByUser(userClientGroup, value);

      setClientOptions(clientGroup);
    } else {
      const clients = optDiff(userClientGroup, 'clientid');
      setClientOptions(clients);
    }

    // 交易编码赋值
    queryForm.current?.set({
      'clientid': undefined,
    });
  }, { wait: 500 });
  const formValueChange = changedValues => {
    if ('userid' in changedValues) {
      const value = changedValues.userid;
      clientRun(value);
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.user.muserclient', {
        defaultMsg: '用户交易编码',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          exchangeid: defaultExchange,
        },
        onValuesChange: formValueChange,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={chgSubmit}
      formData={createChgStatusForm}
    />
  </>);
}

const MemUserClient = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <MemUserClientBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { MemUserClient };
