// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { exchangeDict } = dicts;
const { selectExchange } = formOptions;

export const searchForm = ($t, userFocus, userClientOptions, loading, exchanges) => [{
  valueType: 'autocomplete',
  name: 'userclient',
  label: TableTitle(
    $t('app.options.userclient'),
    $t('app.options.userclient.tip'),
  ),
  fieldProps: {
    onFocus: userFocus,
    options: userClientOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}];
