import { dicts, formOptions } from '@titd/publics/utils';

import { editIcon, userIcon } from './data-dom';

const { exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, frontOptions) => [{
  valueType: 'input',
  name: 'gatewayid',
  label: $t('app.options.db.gateway.id'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'select',
  name: 'gatewaytype',
  label: $t('app.options.db.gateway.front'),
  fieldProps: {
    options: selectOptions(frontOptions),
  }
}];

export const updateForm = $t => [{
  valueType: 'plain',
  name: 'gatewayid',
  label: $t('app.options.db.gateway.id'),
}, {
  valueType: 'hidden',
  name: 'exchangeid',
  noStyle: true,
}, {
  valueType: 'hidden',
  name: 'gatewaytype',
  noStyle: true,
}, {
  valueType: 'input',
  name: 'gatewayaddr',
  rules: [
    { required: true },
    { pattern: new RegExp(/^((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$/, 'g'), message: $t('app.options.db.gateway.address.msg') },
  ],
  label: $t('app.options.db.gateway.address'),
}];

export const assignForm = $t => [{
  valueType: 'plain',
  name: 'gatewayid',
  label: $t('app.options.db.gateway.id'),
}, {
  valueType: 'hidden',
  name: 'exchangeid',
  noStyle: true,
}, {
  valueType: 'hidden',
  name: 'gatewaytype',
  noStyle: true,
// }, {
//   valueType: 'select',
//   name: 'userid',
//   label: $t('app.options.userid'),
//   rules: [{ required: true }],
//   fieldProps: {
//     mode: 'tags',
//     options: userOptions
//   }
}];

export const updAddrLink = (funcs, record) => ({
  id: 'app.general.updateaddr',
  name: '修改地址',
  icon: editIcon,
  onClick: () => funcs(record)
});
export const assignUserLink = (funcs, record) => ({
  id: 'app.general.assignuser',
  name: '分配用户',
  icon: userIcon,
  onClick: () => funcs(record)
});
