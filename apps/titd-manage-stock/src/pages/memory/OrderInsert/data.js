// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { ordStatusDict, userRangeDict, sysRangeDict } = dicts;
const { selectOptions, selectObjOptions } = formOptions;

export const searchForm = ($t, userFocus, userClientOptions, loading, isRequired = true) => [{
  valueType: 'autocomplete',
  name: 'userclient',
  label: TableTitle(
    $t('app.options.userclient'),
    $t('app.options.userclient.tip'),
  ),
  rules: [{
    required: isRequired,
    message: $t('app.general.please') + $t('app.options.userclient'),
  }],
  fieldProps: {
    onFocus: userFocus,
    options: userClientOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'input',
  name: 'ordersysid',
  label: TableTitle(
    $t('app.options.order.ordersysid'),
    $t('app.options.order.ordersysid.long')
  ),
}, {
  valueType: 'picker',
  name: 'begtime',
  label: $t('app.options.order.begtime'),
  fieldProps: {
    type: 'time',
  }
}, {
  valueType: 'picker',
  name: 'endtime',
  label: $t('app.options.order.endtime'),
  fieldProps: {
    type: 'time'
  }
}, {
  valueType: 'select',
  name: 'ordstatus',
  label: $t('app.options.order.ordstatus'),
  fieldProps: {
    options: selectObjOptions(ordStatusDict, { isNum: true })
  }
}, {
  valueType: 'select',
  name: 'userrange',
  label: TableTitle(
    $t('app.options.order.userrange'),
    $t('app.options.order.userrange.long')
  ),
  fieldProps: {
    allowClear: false,
    options: selectOptions(userRangeDict)
  }
}, {
  valueType: 'select',
  name: 'bssystemflag',
  label: $t('app.options.order.bssystemflag'),
  fieldProps: {
    allowClear: false,
    options: selectOptions(sysRangeDict)
  }
}];
