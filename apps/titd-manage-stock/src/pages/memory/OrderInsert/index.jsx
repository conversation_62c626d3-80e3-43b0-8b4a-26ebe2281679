import { useRef, useState, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, Button, App } from 'antd';
import dayjs from 'dayjs';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
  exportDownModal,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  currency,
  getList,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

import OrderDetail from './OrderDetail';
import TradeDetail from './TradeDetail';

const { Text } = Typography;
const { IndexColumn, TableTag, NegaNumber, TableBadgeDot, TableTitle, ErrorMsg, TableSwitch } = TableFields;
const { DrawerForm } = forms;
const { rtnLink, infoLink } = links;
const { exchangeAllDict, sideDict, ordStatusDict, priceTypeDict, timeInforceDict, ownerTypeDict, yesNoDict, ordStatusColor, tagNormalColors, tagColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList, getErrorCodeMsg } = getList;
const { getFloat } = currency;
const { formatTime } = dateFormat;

const OrderInsertBase = props => {
  const { paramId } = props;
  const navigate = useNavigate();
  const queryForm = useRef(null);
  const drawerRtnForm = useRef(null);
  const drawerInfoForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectRecord, setSelectRecord] = useState({});
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [errorCodeMsg, setErrorCodeMsg] = useState([]);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.order.ordtime'),
    dataIndex: 'ordtime',
    fixed: 'left',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
    fixed: 'left',
  }, {
    title: $t('app.options.order.exrequestid'),
    dataIndex: 'responsestr',
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
  }, {
    title: $t('app.stock.order.exexecutno'),
    dataIndex: 'exexecutno',
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.ordresponsecode'),
    dataIndex: 'ordresponsecode',
    render: (text, record) => <ErrorMsg titdErrorCode={errorCodeMsg} errcode={text} exchangeid={record.exchangeid} />,
  }, {
    title: $t('app.options.postion.side'),
    dataIndex: 'side',
    render: text => TableTag(text, sideDict, tagColors, true),
  }, {
    title: $t('app.options.order.volume'),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.price'),
    dataIndex: 'price',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.order.bfrozenvolume'),
    dataIndex: 'bfrozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.bthawvolume'),
    dataIndex: 'bthawvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.cancelvolume'),
    dataIndex: 'cancelvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.trade.frozenpremium'),
    dataIndex: 'bfrozenpremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.order.frozen'),
      $t('app.stock.order.price.tip')
    ),
    dataIndex: 'sfrozenvolume',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'bfrozencommi',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: TableTitle(
      $t('app.options.order.pricetype'),
      $t('app.options.order.pricetype.long')
    ),
    dataIndex: 'pricetype',
    render: text => priceTypeDict[text] || text,
  }, {
    title: $t('app.options.order.timeinforce'),
    dataIndex: 'timeinforce',
    render: text => timeInforceDict[text] || text,
  }, {
    title: $t('app.options.order.ownertype'),
    dataIndex: 'ownertype',
    render: text => ownerTypeDict[text] || text,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.order.frontno'),
      $t('app.options.order.frontno.long')
    ),
    dataIndex: 'frontno',
  }, {
    title: $t('app.options.bizpbu'),
    dataIndex: 'bizpbu',
  }, {
    title: $t('app.options.order.cpuid'),
    dataIndex: 'cpuid',
  }, {
    title: $t('app.options.order.rec2send'),
    dataIndex: 'rec2send',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.rec2exrsp'),
    dataIndex: 'rec2exrsp',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.gateway'),
    dataIndex: 'gateway',
  }, {
    title: $t('app.stock.order.exrequestno'),
    dataIndex: 'exrequestid',
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno',
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
  }, {
    title: $t('app.options.order.unaction'),
    dataIndex: 'sthawvolume',
    render: (text, record) => record.ordersysid && record.ordersysid !== ' ' ? TableSwitch(text, record, 'ordersysid', toChgStatus, yesNoDict, 1, {
      className: 'ti-switch-danger',
      disabled: !text,
    }) : null,
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 200,
    render: (_, record) => record.ordersysid && record.ordersysid !== ' ' ? (
      <ActionLinks links={[
        rtnLink(toRtnDetail, record),
        infoLink(toInfoDetail, record),
      ]} />
    ) : null
  }];

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false), [$t, userFocus, userClientGroup]);

  useMount(() => {
    getErrorCodeMsg(Request.get, setErrorCodeMsg);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryStkInsert, params.svrid), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => {
        const arrGroup = item.ssepartyid.split(',');

        let itemData = {
          ...item,
          id: params.beginno + item.rspseqno - 1,
          rec2send: getFloat(item.rec2send, 4),
          rec2exrsp: getFloat(item.rec2exrsp, 4),
          bfrozenmargin: getFloat(item.bfrozenmargin, 4),
          bfrozenpremium: getFloat(item.bfrozenpremium, 4),
          exexecutno: arrGroup[1] || '',
        }

        const sseGroup = arrGroup[0]?.split('|') || [];
        if (sseGroup.length > 0) {
          itemData = {
            ...itemData,
            bizpbu: sseGroup[0] || '',
            gateway: sseGroup[1] || '',
          }
        } else {
          // 兼容旧版
          itemData = {
            ...itemData,
            ssepartyid: arrGroup[0] || '',
          }
        }

        return itemData;
      });

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0)
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getSearchParams = values => {
    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
      // instrumentid: values.instrumentid?.trim(),
      begtime: values.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
        !searchParams.userrange && (searchParams.userid = userclient[1]);
      } else {
        searchParams.clientid = values.userclient;
      }

      delete searchParams.userclient;
      delete searchParams.userrange;
    }

    return searchParams;
  }

  const onRefresh = () => {
    refresh();
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = getSearchParams(values);

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }
  const toChgStatus = record => {
    const postData = {
      ...defaultParams,
      intvolume1: 15, // 保单信息,
      str1: record.ordersysid,
    }

    fetchFunc(MESSAGE_TYPE.MemUpdOrderStatus, postData, resp => {
      // console.log(resp);
      if (resp.errmessage) {
        message.error(resp.errmessage);
      } else {
        message.success(CURD.Update + CURD.StatusOkMsg);
        onRefresh();
      }
    });
  }

  const toRtnDetail = record => {
    drawerRtnForm.current.show(`${$t('app.menu.mem.rtn.rtnorder')}: ${record.ordersysid}`);
    setSelectRecord(record);
  }

  const toInfoDetail = record => {
    drawerInfoForm.current.show(`${$t('app.menu.mem.rtn.rtntrade')}: ${record.ordersysid}`);
    setSelectRecord(record);
  }

  const toExport = values => {
    const exportParams = getSearchParams(values);

    fetchFunc(MESSAGE_TYPE.ExpOrderInsert, exportParams, resp => {
      // console.log(resp);
      if (resp.errmessage) {
        exportDownModal(resp.errmessage);
      } else {
        message.error('导出失败');
      }
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.ord.orderinsert', {
        defaultMsg: '期权报单',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        initValues: {
          userrange: 1,
          bssystemflag: 0,
        },
        onSearch: onSearch,
        onReset: onReset,
        otherBtns: (
          <Button
            color="orange"
            variant="outlined"
            onClick={() => queryForm.current?.submit(toExport)}
          >{$t('app.general.export')}</Button>
        ),
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    {/* 委托回报 */}
    <DrawerForm
      ref={drawerRtnForm}
      formData={[]}
    >
      <OrderDetail order={selectRecord} params={defaultParams} />
    </DrawerForm>

    {/* 成交回报 */}
    <DrawerForm
      ref={drawerInfoForm}
      formData={[]}
    >
      <TradeDetail order={selectRecord} params={defaultParams} />
    </DrawerForm>
  </>);
}

const OrderInsert = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <OrderInsertBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { OrderInsert };
