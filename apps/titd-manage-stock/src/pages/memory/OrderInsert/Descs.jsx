import { Descriptions } from 'antd';

const { Item } = Descriptions;

const Descs = ({ title, list }) => {
  return (
    <Descriptions
      title={title}
      bordered
      size="middle"
      column={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2, xxl: 2 }}
      style={{ marginBottom: '25px' }}
    >
      {list && list.map((item, idx) => <Item label={item.label} key={idx}>{item.value}</Item>)}
    </Descriptions>
  );
}

export default Descs;
