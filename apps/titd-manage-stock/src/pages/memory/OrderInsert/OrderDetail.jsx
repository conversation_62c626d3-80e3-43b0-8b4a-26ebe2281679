import { useEffect, useState } from 'react';
import { Typography } from 'antd';

import {
  TableSimple,
  TableFields,
} from '@titd/publics/components';
import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
  dateFormat,
  tools,
} from '@titd/publics/utils';

import Descs from './Descs';
import { baseMap } from './map';

const { Text } = Typography;
const { TableTag, IndexColumn, NegaNumber } = TableFields;
const { ordStatusDict, ordStatusColor } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;
const { formatTime } = dateFormat;
const { hideColumns } = tools;

const OrderDetail = props => {
  const {
    order,
    params,
  } = props;

  const { $t } = useFormattedMessage();

  const [base, setBase] = useState([]);
  const [rtnData, setRtnData] = useState([]);

  // const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);

  const columns = [{
    title: $t('app.options.rtn.sequenceno'),
    dataIndex: 'sequenceno',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.rtn.transtime'),
    dataIndex: 'transtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.price'),
    dataIndex: 'price',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.order.volume'),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.rtn.leavesvolume'),
    dataIndex: 'leavesvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.cancelvolume'),
    dataIndex: 'cancelvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }];

  const getGroupData = async record => {
    // 基础消息
    const baseData = baseMap($t, record);
    setBase(baseData);

    const postData = {
      ...params,
      clientid: record.clientid,
      ordersysid: record.ordersysid,
      userid: record.userid
    };

    // 委托
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryRtnOrderStk, params.svrid), postData);
    if (resp.status === 200) {
      setRtnData([...resp.data]);
    }
  }

  useEffect(() => {
    getGroupData(order);

    return () => {
      setBase([]);
      setRtnData([]);
    }
  }, [order]); // eslint-disable-line react-hooks/exhaustive-deps

  return (<>
    <Descs list={base} />
    <TableSimple
      customKey={'sequenceno'}
      columns={hideColumns(columns)}
      dataSource={rtnData}
      // isLoading={isLoading}
    />
  </>);
}

export default OrderDetail;
