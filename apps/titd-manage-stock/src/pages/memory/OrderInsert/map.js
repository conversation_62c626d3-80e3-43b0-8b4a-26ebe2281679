import { TableFields } from '@titd/publics/components';
import { dicts, tools } from '@titd/publics/utils';

const { TableTitle, TableBadgeDot, TableTag } = TableFields;
const { exchangeDict, sideDict, priceTypeDict, timeInforceDict, ownerTypeDict, tagColors, tagNormalColors } = dicts;
const { hideColumns } = tools;

const value2Text = (dict, text) => dict[text] || text;

export const baseMap = ($t, record) => {
  const bmap = [{
    label: $t('app.options.clientid'),
    value: `${record.clientid} [${record.clientno}]`
  }, {
    label: $t('app.options.instrumentid'),
    value: `${record.instrumentid} [${record.instrumentno}]`
  }, {
    label: $t('app.options.exchangeid'),
    value: TableBadgeDot(record.exchangeid, exchangeDict, tagNormalColors, true),
  }, {
    label: $t('app.options.order.ordersysid.long'),
    value: record.ordersysid
  }, {
    label: TableTitle(
      $t('app.options.order.pricetype'),
      $t('app.options.order.pricetype.long')
    ),
    value: value2Text(priceTypeDict, record.pricetype)
  }, {
    label: $t('app.options.postion.side'),
    value: TableTag(record.side, sideDict, tagColors, true),
  }, {
    label: $t('app.options.order.timeinforce'),
    value: value2Text(timeInforceDict, record.timeinforce)
  }, {
    label: $t('app.options.order.ownertype'),
    value: value2Text(ownerTypeDict, record.ownertype)
  }, {
    label: TableTitle(
      $t('app.options.userid'),
      $t('app.options.rtn.userid.long')
    ),
    value: record.userid
  }];

  return hideColumns(bmap);
}
