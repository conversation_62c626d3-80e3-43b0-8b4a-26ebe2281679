import { useEffect, useState } from 'react';
import { Typography } from 'antd';

import {
  TableSimple,
  TableFields,
} from '@titd/publics/components';
import {
  consts,
  useFormattedMessage,
  Request,
  dateFormat,
} from '@titd/publics/utils';

import Descs from './Descs';
import { baseMap } from './map';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableTitle } = TableFields;
const { SITE_URL, MESSAGE_TYPE } = consts;
const { formatTime } = dateFormat;

const TradeDetail = props => {
  const {
    order,
    params,
  } = props;

  const { $t } = useFormattedMessage();

  const [base, setBase] = useState([]);
  const [infoData, setInfoData] = useState([]);

  // const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);

  const columns = [{
    title: $t('app.options.rtn.sequenceno'),
    dataIndex: 'sequenceno',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.trade.transtime'),
    dataIndex: 'transtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.trade.tradeprice'),
    dataIndex: 'tradeprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.trade.tradevolume'),
    dataIndex: 'tradevolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.trade.leavesvolume'),
      $t('app.options.trade.leavesvolume.long')
    ),
    dataIndex: 'leavesvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.trade.tradeid'),
    dataIndex: 'tradeid',
  }];

  const getGroupData = async record => {
    // 基础消息
    const baseData = baseMap($t, record);
    setBase(baseData);

    const postData = {
      ...params,
      clientid: record.clientid,
      ordersysid: record.ordersysid,
      userid: record.userid
    };

    // 成交
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryRtnTradeStk, params.svrid), postData);
    if (resp.status === 200) {
      setInfoData([...resp.data]);
    }
  }

  useEffect(() => {
    getGroupData(order);

    return () => {
      // abort();
      setBase([]);
      setInfoData([]);
    }
  }, [order]); // eslint-disable-line react-hooks/exhaustive-deps

  return (<>
    <Descs list={base} />
    <TableSimple
      customKey={'sequenceno'}
      columns={columns}
      dataSource={infoData}
      // isLoading={isLoading}
    />
  </>);
}

export default TradeDetail;
