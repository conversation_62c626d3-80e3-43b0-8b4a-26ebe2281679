// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { exchangeDict } = dicts;
const { selectOptions, selectExchange } = formOptions;

export const searchForm = ($t, userFocus, userClientOptions, loading, exchanges) => [{
  valueType: 'autocomplete',
  name: 'userclient',
  label: TableTitle(
    $t('app.options.userclient'),
    $t('app.options.userclient.tip'),
  ),
  fieldProps: {
    onFocus: userFocus,
    options: userClientOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}];

export const transferForm = ($t, isModify, digitUpper, userFocus, userClientOptions) => {
  const showForm = isModify ? [{
    valueType: 'plain',
    name: 'availused',
    label: $t('app.stock.cashstock.availused'),
    fieldProps: {
      style: {
        fontSize: '20px',
        lineHeight: '20px',
        color: '#0081cc',
      }
    }
  }, {
    valueType: 'plain',
    name: 'deposit',
    label: $t('app.stock.common.deposit'),
    fieldProps: {
      style: {
        fontSize: '18px',
        lineHeight: '18px',
        color: '#52c41a',
      }
    }
  }, {
    valueType: 'plain',
    name: 'withdraw',
    label: $t('app.stock.common.withdraw'),
    fieldProps: {
      style: {
        fontSize: '18px',
        lineHeight: '18px',
        color: '#ff4d4f',
      }
    }
  }] : [];

  return [{
    valueType: 'autocomplete',
    name: 'userclient',
    label: $t('app.options.clientid'),
    rules: [{ required: true }],
    fieldProps: {
      readOnly: isModify,
      onFocus: userFocus,
      options: userClientOptions,
    }
  }, {
    valueType: 'select',
    name: 'exchangeid',
    label: $t('app.options.exchangeid'),
    rules: [{ required: true }],
    fieldProps: {
      disabled: isModify,
      options: selectOptions(exchangeDict),
    }
  }, {
    valueType: 'input',
    name: 'instrumentid',
    label: $t('app.options.instrumentid'),
    rules: [{ required: true }],
    fieldProps: {
      readOnly: isModify,
    }
  }, ...showForm, {
    valueType: 'number',
    name: 'transfer',
    label: $t('app.stock.common.number'),
    extra: digitUpper,
    rules: [{ required: true }],
  }];
}
