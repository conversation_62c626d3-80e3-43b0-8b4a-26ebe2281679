import { useRef, useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Card, Spin, Space, Button, message } from 'antd';
import {
  ImportOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { useRequest, useUnmount } from 'ahooks';

import {
  TableSimple,
  TableFields,
  forms,
} from '@titd/publics/components';
import { transferForm } from './data';

import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
  currency,
  getList,
  errorResp,
} from '@titd/publics/utils';

const { TableTag } = TableFields;
const { SimpleForm } = forms;
const { loginStatusDict, exchangeDict, tagColors } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;
const { getUserClientList } = getList;
const { digitUppercase, formatDot } = currency;

const Transfer = props => {
  const {
    params,
    record,
    custom,
    onSuccess,
  } = props;

  const aForm = useRef();
  const navigate = useNavigate();
  const { $t } = useFormattedMessage();

  const [oldAccountid, setOldAccountid] = useState();
  const [respData, setRespData] = useState([]);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const respColumns = [{
    dataIndex: 'respstatus',
    title: $t('app.stock.account.status'),
    render: text => TableTag(text, loginStatusDict, tagColors),
  }, {
    dataIndex: 'responsestr',
    title: $t('app.stock.account.logmessage'),
  }];

  const amountType = useMemo(() => {
    return [
      ['withdraw', $t('app.stock.common.withdraw')],
      ['deposit', $t('app.stock.common.deposit')],
    ];
  }, [$t]);

  const [isModify, setIsModify] = useState(true);
  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, params, setUserClientGroup);
  }, [params]);

  const [digitUpper, setDigitUpper] = useState('');
  const createTransferForm = useMemo(() => transferForm($t, isModify, digitUpper, userFocus, userClientGroup), [$t, isModify, digitUpper, userFocus, userClientGroup]);

  useUnmount(() => cancel());

  useEffect(() => {
    if (record) {
      if (record.accountid !== oldAccountid) {
        setOldAccountid(record.accountid);
        setRespData([]);
      }

      transferReset();

      const newRecord = {
        ...record,
        exchangename: exchangeDict[record.exchangeid] || record.exchangeid,
        availused: formatDot(record.availused, 2),
        deposit: formatDot(record.deposit, 2),
        withdraw: formatDot(record.withdraw, 2),
      }

      aForm.current.set(newRecord);
      setIsModify(true);
    }
  }, [record]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (custom) {
      setIsModify(false);
    }
  },[custom]);

  const transferReset = () => {
    aForm.current.reset();
    setDigitUpper('');
  }

  const formValueChange = changedValues => {
    if ('transfer' in changedValues) {
      const value = changedValues.transfer;
      if (value !== '') {
        setDigitUpper(digitUppercase(value));
      } else {
        setDigitUpper('');
      }
    }
  }

  const amountSubmit = type => {
    aForm.current.submit(values => {
      // console.log(values, type);
      const transfer = amountType[type];
      if (transfer) {
        // 用户代码、交易编码
        if (values?.userclient) {
          const userclient = values.userclient?.split(':');
          if (userclient.length > 0) {
            values.clientid = userclient[0];
          } else {
            values.clientid = values.userclient;
          }
        }

        const postData = {
          ...params,
          clientid: values.clientid,
          exchangeid: values.exchangeid,
          instrumentid: values.instrumentid,
          [transfer[0]]: values.transfer,
        };

        fetchFunc(MESSAGE_TYPE.MemUpdCashStock, postData, resp => {
          if (resp.errcode) {
            message.error(resp.errmessage);
          } else {
            if (resp?.length > 0) {
              const tableData = resp.map((item, idx) => ({
                ...item,
                id: idx + 1,
                respstatus: item.responsecode < 0 ? 1 : 0,
              }));
              setRespData(tableData);
            } else {
              setRespData([]);
            }
            onSuccess && onSuccess();
          }
        });
      }
    });
  }

  const { loading, run, cancel } = useRequest(amountSubmit, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  return (<>
    <Spin spinning={loading} tip="划转中……">
      <Card className="ti-table-content">
        <SimpleForm
          ref={aForm}
          formData={createTransferForm}
          onValuesChange={formValueChange}
          formStyle={{ maxWidth: '600px' }}
          customBtn={<Space>
            <Button
              size="large"
              color="cyan"
              variant="solid"
              disabled={loading}
              icon={<ImportOutlined />}
              onClick={() => run(1)}
            >{$t('app.stock.common.deposit')}</Button>
            <Button
              size="large"
              color="orange"
              variant="solid"
              disabled={loading}
              icon={<ExportOutlined />}
              onClick={() => run(0)}
            >{$t('app.stock.common.withdraw')}</Button>
          </Space>}
        />
      </Card>
    </Spin>

    {respData.length > 0 && (
      <Card className="ti-table-content">
        <TableSimple
          columns={respColumns}
          dataSource={respData}
          isLoading={loading}
        />
      </Card>
    )}
  </>);
}

export default Transfer;
