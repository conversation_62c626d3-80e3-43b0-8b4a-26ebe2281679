import { useState, useMemo, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, Space, Switch, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  dicts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

import Transfer from './Transfer';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableBadgeDot } = TableFields;
const { exchangeAllDict, cashTypeDict, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList } = getList;

const CashStockBase = props => {
  const { paramId } = props;

  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);
  const [selectKeys, setSelectKeys] = useState([1]);
  const [isCustom, setIsCustom] = useState(false);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

   // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = exchangeList && exchangeList[paramId];

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: text => text ? (<Text strong>{text}</Text>) : (<Text type="secondary">
      {$t('app.general.all') + $t('app.options.clientid')}
    </Text>),
  }, {
    title: $t('app.stock.cashaccount.type'),
    dataIndex: 'cashtype',
    render: text => cashTypeDict[text] || text
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
  }, {
    title: $t('app.stock.cashstock.preposition'),
    dataIndex: 'preposition',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.cashstock.availused'),
    dataIndex: 'availused',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.cashstock.position'),
    dataIndex: 'position',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.cashstock.frozenvolume'),
    dataIndex: 'frozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.common.deposit') + $t('app.stock.common.number'),
    dataIndex: 'deposit',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.common.withdraw') + $t('app.stock.common.number'),
    dataIndex: 'withdraw',
    align: 'right',
    render: text => NegaNumber(text),
  }];

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false, exchanges), [$t, userFocus, userClientGroup, exchanges]);

  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryCashStock, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
        cashtype: item.clientid ? 1 : 0,
      }));

      setTotal(respData[0].rsptotnum);
      // setSelectKeys([1]);
      return tableData;
    } else {
      setTotal(0);
      setSelectKeys([]);
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const selectRecord = useMemo(() => {
    if (selectKeys.length > 0 && dataSource.length > 0) {
      return dataSource.find(i => i.id === selectKeys[0]);
    }

    return null;
  }, [selectKeys, dataSource]);

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    }

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }

      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.cdcash.cashstock', {
        defaultMsg: '融券头寸',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        tableTitle: (<Space>
          <Switch
            checkedChildren="开启"
            unCheckedChildren="关闭"
            checked={isCustom}
            onClick={() => {
              setIsCustom(!isCustom);
              setSelectKeys([]);
            }}
          />
          自助划转
        </Space>),
        page: page,
        total: total,
        pageChange: pageChange,
        rowClickable: true,
        selectionType: 'radio',
        selectKeys: selectKeys,
        setSelectKeys: keys => setSelectKeys(keys),
      }}
    />

    {/* 转移数量 */}
    {(selectRecord || isCustom) && (
      <Transfer
        params={defaultParams}
        record={selectRecord}
        custom={isCustom}
        onSuccess={onRefresh}
      />
    )}
  </>);
}

const CashStock = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <CashStockBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { CashStock };
