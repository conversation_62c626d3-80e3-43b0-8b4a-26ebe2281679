import { useRef, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
  dateFormat,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, NegaNumber, TableTag, TableTitle } = TableFields;
const { exchangeAllDict, sideDict, tagNormalColors, tagColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList } = getList;
const { formatDate } = dateFormat;

const CreditPositionBase = props => {
  const { paramId } = props;

  const queryForm = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.stock.cdposition.openingdate'),
    dataIndex: 'openingDate',
    render: text => formatDate(text),
  }, {
  //   title: intl.formatMessage({ id: 'app.stock.cdposition.closingdate' }),
  //   dataIndex: 'closingDate',
  //   render: text => formatDate(text),
  // }, {
    title: $t('app.stock.cdposition.expdate'),
    dataIndex: 'expDate',
    render: text => formatDate(text),
  }, {
    title: $t('app.stock.cdposition.orderno'),
    dataIndex: 'orderno',
  }, {
    title: $t('app.stock.repay.orderno'),
    dataIndex: 'orderId',
  }, {
    title: $t('app.stock.cdposition.side'),
    dataIndex: 'side',
    render: text => TableTag(text, sideDict, tagColors, true),
  }, {
    title: $t('app.stock.cdposition.contqty'),
    dataIndex: 'contQty',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.cdposition.contamt'),
    dataIndex: 'contAmt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.contfee'),
    dataIndex: 'contFee',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
  //   title: intl.formatMessage({ id: 'app.stock.cdposition.contintrate' }),
  //   dataIndex: 'contIntRate',
  //   align: 'right',
  //   render: text => NegaNumber(text),
  // }, {
  //   title: intl.formatMessage({ id: 'app.stock.cdposition.contintblnaccu' }),
  //   dataIndex: 'contIntBlnAccu',
  //   align: 'right',
  //   render: text => NegaNumber(text),
  // }, {
    title: $t('app.stock.cdposition.contint'),
    dataIndex: 'contInt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.repaidamt'),
    dataIndex: 'repaidAmt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.rltrepaidamt'),
    dataIndex: 'rltRepaidAmt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.repaidqty'),
    dataIndex: 'repaidQty',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.cdposition.rltrepaidqty'),
    dataIndex: 'rltRepaidQty',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.cdposition.rltrepaidcommi'),
    dataIndex: 'debtAmt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.repaidint'),
    dataIndex: 'repaidInt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.rltrepaidint'),
    dataIndex: 'rltrepaidint',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.stock.cdposition.contremainamt'),
      $t('app.stock.position.toreturnprice') + ' + ' + $t('app.stock.position.returncommi')
    ),
    dataIndex: 'contRemainAmt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.contremainqty'),
    dataIndex: 'contRemainQty',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.cdposition.pendrepayint'),
    dataIndex: 'pendRepayInt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.allamt'),
    dataIndex: 'allamt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.stock.cdposition.marginratio'),
    dataIndex: 'marginRatio',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.cdposition.marginamt'),
    dataIndex: 'marginAmt',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  // }, {
  //   title: intl.formatMessage({ id: 'app.stock.cdposition.freeint' }),
  //   dataIndex: 'freeInt',
  //   align: 'right',
  //   render: text => NegaNumber(text),
  // }, {
  //   title: intl.formatMessage({ id: 'app.stock.cdposition.debtamt' }),
  //   dataIndex: 'debtAmt',
  //   align: 'right',
  //   render: text => NegaNumber(text),
  // }, {
  //   title: intl.formatMessage({ id: 'app.stock.cdposition.tmpstr' }),
  //   dataIndex: 'tmpstr',
  }];

  const [accountGroup, setAccountGroup] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, accountGroup), [$t, accountGroup]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountGroup);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryCreditPosition, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => {
        const respstr = item.responsestr.split('|').map(i => Number(i));

        return {
          ...item,
          id: item.rspseqno,
          rltrepaidint: respstr[0],
          orderno: respstr[1],
          allamt: item.contRemainAmt + item.pendRepayInt,
        };
      });

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.cdpos.cdposition', {
        defaultMsg: '融资合约',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        onSearch: onSearch,
        onReset: onReset,
        isLoading: isLoading,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const CreditPosition = props => {
  const param = useParams();
  const paramId = param?.id || '';
  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <CreditPositionBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { CreditPosition };
