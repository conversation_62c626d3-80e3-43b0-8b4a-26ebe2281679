import { useRef, useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { searchForm, chgStatusForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { TableTag, IndexColumn, NegaNumber, TableBadgeDot, TableTitle } = TableFields;
const { ModalForm } = forms;
const { updateLink, chgStatusLink } = links;
const { exchangeAllDict, clientStatusDict, yesNoDict, tagNormalColors, tagColors, yesNoColor } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { formatDate } = dateFormat;

const InstrumentBase = props => {
  const { paramId } = props;
  const modalForm = useRef(null);
  const addModalForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = useMemo(() => exchangeList && exchangeList[paramId], [exchangeList, paramId]);
  const defaultExchange = exchanges?.length === 1 ? Number(exchanges[0]) : undefined;

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);
  const searchParams = {
    ...defaultParams,
    exchangeid: defaultExchange,
  };

  // const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const financeColumns = [{
    title: $t('app.stock.instrument.allowedascollateral'),
    dataIndex: 'allowdelivpersonopen',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.collateralconvrate'),
      $t('app.stock.instrument.collateralconvrate.long'),
    ),
    dataIndex: 'marginprice',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.instrument.allowedfi'),
    dataIndex: 'tradedays',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.fimargin'),
      $t('app.stock.instrument.fimargin.long'),
    ),
    dataIndex: 'stockprice',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.instrument.allowedsl'),
    dataIndex: 'advancemonth',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.slmargin'),
      $t('app.stock.instrument.slmargin.long'),
    ),
    dataIndex: 'undermarginprice',
    align: 'right',
    render: text => NegaNumber(text),
  }];

  const columns = [{
    dataIndex: 'id',
    fixed: 'left',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    fixed: 'left',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.instrumentno}]`}
    </>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.instrument.instrumentname'),
    dataIndex: 'instrumentname',
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
    render: text => productOptions.find(i => i.value === text)?.label || text,
  }, {
    title: $t('app.options.instrument.producttype'),
    dataIndex: 'producttype',
    render: text => productTypeOptions.find(i => i.value === text)?.label || text,
  }, {
    title: $t('app.options.instrument.lastprice'),
    dataIndex: 'lastprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.settlementprice'),
    dataIndex: 'settlementprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.upperlimitprice'),
    dataIndex: 'upperlimitprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.lowerlimitprice'),
    dataIndex: 'lowerlimitprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.volumemultiple'),
    dataIndex: 'volumemultiple',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.createdate'),
    dataIndex: 'createdate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.opendate'),
    dataIndex: 'opendate',
    render: text => formatDate(text),
  }, {
    title: $t('app.stock.instrument.buyordertick'),
    dataIndex: 'startdelivdate',
    align: 'right',
    render: text => formatDate(text),
  }, {
    title: $t('app.stock.instrument.sellordertick'),
    dataIndex: 'enddelivdate',
    align: 'right',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.basisprice'),
    dataIndex: 'basisprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.maxmarketordervolume'),
    dataIndex: 'maxmarketordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.minmarketordervolume'),
    dataIndex: 'minmarketordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.maxlimitordervolume'),
    dataIndex: 'maxlimitordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.minlimitordervolume'),
    dataIndex: 'minlimitordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.pricetick'),
    dataIndex: 'pricetick',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.tplus1'),
    dataIndex: 'tplus1',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: $t('app.options.mem.userclient.status'),
    dataIndex: 'tradingright',
    render: text => TableTag(text, clientStatusDict, tagColors),
  }, ...(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? financeColumns : []), {
    title: $t('app.stock.instrument.remark'),
    dataIndex: 'expiredate'
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? 180 : 100,
    render: (_, record) => (
      <ActionLinks links={[
        DEFAULT_CONFIGS.SHOW_MARGINFINANCING && updateLink(toUpdate, record),
        record.tradingright !== 3 && chgStatusLink(toChgStatus, record),
      ]} />
    )
  }].filter(i => i);

  const [productOptions, setProductOptions] = useState([]);
  const [productTypeOptions, setProductTypeOptions] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, exchanges, productOptions, productTypeOptions), [$t, exchanges, productOptions, productTypeOptions]);
  const createChgStatusForm = useMemo(() => chgStatusForm($t), [$t]);
  const createAddForm = useMemo(() => addForm($t), [$t])

  useMount(() => {
    getProduct();
  });
  useUnmount(() => cancel());

  const getProduct = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryInstrumentType), defaultParams);

    const { data: productRsp } = resp;
    if (productRsp.length > 0) {
      const products = [];
      const types = [];
      productRsp.forEach(item => {
        // 品种
        const idx = products.findIndex(i => i.value === item.productid);
        if (idx === -1) {
          products.push({
            label: `${item.productname} [${item.productid}]`,
            value: item.productid,
          });
        }

        // 类型
        const typeIdx = types.findIndex(i => i.value === item.producttype);
        if (typeIdx === -1) {
          types.push({
            // label: `${item.producttypename} [${item.producttype}]`,
            label: item.producttypename,
            value: item.producttype,
          });
        }
      });

      setProductOptions(products);
      setProductTypeOptions(types);
    }
  }

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryInstrument, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [searchParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }

  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...searchParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(searchParams);
  }

  const toUpdate = record => {
    // console.log('Update', record);
    const newRecord = {
      ...record,
      dbpflag: record.allowdelivpersonopen,
      zsl: record.marginprice,
      rzflag: record.tradedays,
      rqbl: record.stockprice,
      rqflag: record.advancemonth,
      rzbl: record.undermarginprice,
    }
    addModalForm.current.show(1, newRecord);
  }
  const toChgStatus = record => {
    // console.log('ChangeStatus', record);
    const newRecord = {
      ...record,
      productid: record.instrumentid,
      status: record.tradingright,
    }
    modalForm.current.show('更改交易状态', newRecord);
  }

  const chgSubmit = form => {
    // console.log(form);
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(MESSAGE_TYPE.UpdClientStatus, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const addSubmit = form => {
    const postData = {
      ...defaultParams,
      ...form,
      dbpflag: form.dbpflag ? 1 : 0,
      rzflag: form.rzflag ? 1 : 0,
      rqflag: form.rqflag ? 1 : 0,
    }

    fetchFunc(MESSAGE_TYPE.MemUpdInstrument, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.instrument', {
        defaultMsg: '合约信息',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        initValues: {
          exchangeid: defaultExchange,
        },
        resetSearch: false,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: refresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    {/* 更新状态 */}
    <ModalForm
      ref={modalForm}
      onOk={chgSubmit}
      formData={createChgStatusForm}
    />

    {/* 修改 */}
    <ModalForm
      ref={addModalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />
  </>);
}

const Instrument = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <InstrumentBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Instrument };
