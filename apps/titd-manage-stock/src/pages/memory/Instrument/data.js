import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { exchangeDict, clientChgStatusDict } = dicts;
const { selectOptions, selectExchange } = formOptions;

export const searchForm = ($t, exchanges, productOptions, productTypeOptions) => [{
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}, {
  valueType: 'select',
  name: 'productid',
  label: $t('app.options.productid'),
  fieldProps: {
    showSearch: true,
    options: productOptions,
  }
}, {
  valueType: 'select',
  name: 'producttype',
  label: $t('app.options.instrument.producttype'),
  fieldProps: {
    options: productTypeOptions,
  }
}];

export const chgStatusForm = $t => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    open: false,
    bordered: false,
    allowClear: false,
    showArrow: false,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'text',
  name: 'productid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'radio',
  name: 'status',
  label: $t('app.options.mem.userclient.status'),
  rules: [{ required: true }],
  fieldProps: {
    optionType: 'button',
    buttonStyle: 'solid',
    options: selectOptions(clientChgStatusDict)
  }
}];

export const addForm = $t => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    open: false,
    bordered: false,
    allowClear: false,
    showArrow: false,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'text',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'switch',
  name: 'dbpflag', // allowdelivpersonopen
  label: $t('app.stock.instrument.allowedascollateral'),
  otherProps: {
    valuePropName: 'checked',
  }
}, {
  valueType: 'number',
  name: 'zsl', // marginprice
  label: TableTitle(
    $t('app.stock.instrument.collateralconvrate'),
    $t('app.stock.instrument.collateralconvrate.long'),
  ),
  fieldProps: {
    precision: 2,
  }
}, {
  valueType: 'switch',
  name: 'rzflag', // tradedays
  label: $t('app.stock.instrument.allowedfi'),
  otherProps: {
    valuePropName: 'checked',
  }
}, {
  valueType: 'number',
  name: 'rqbl', // stockprice
  label: TableTitle(
    $t('app.stock.instrument.fimargin'),
    $t('app.stock.instrument.fimargin.long'),
  ),
  fieldProps: {
    precision: 2,
  }
}, {
  valueType: 'switch',
  name: 'rqflag', // advancemonth
  label: $t('app.stock.instrument.allowedsl'),
  otherProps: {
    valuePropName: 'checked',
  }
}, {
  valueType: 'number',
  name: 'rzbl', // undermarginprice
  label: TableTitle(
    $t('app.stock.instrument.slmargin'),
    $t('app.stock.instrument.slmargin.long'),
  ),
  fieldProps: {
    precision: 2,
  }
}];
