// import { Spin } from 'antd';
import { dicts, formOptions } from '@titd/publics/utils';

const { isTimeoutDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, userFocus, userOptions) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.userid'),
  fieldProps: {
    onFocus: userFocus,
    options: userOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'select',
  name: 'istimeout',
  label: $t('app.options.mem.sessionno.istimeout'),
  fieldProps: {
    allowClear: false,
    options: selectOptions(isTimeoutDict)
  }
}, {
  valueType: 'number',
  name: 'reqsessionno',
  label: $t('app.options.sessionno'),
}];
