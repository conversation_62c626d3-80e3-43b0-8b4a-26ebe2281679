import { useState, useMemo, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

const { Text, Title } = Typography;
const { IndexColumn, TableTag, TableBadgeDot } = TableFields;
const { Confirm } = forms;
const { offlineLink } = links;
const { isTimeoutDict, yesNoDict, tagColors, yesNoColor } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getMemUserList } = getList;
const { formatDateFull } = dateFormat;

const SessionBase = props => {
  const { paramId } = props;

  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.mem.sessionno.istimeout'),
    dataIndex: 'istimeout',
    render: text => TableBadgeDot(text, isTimeoutDict, tagColors)
  }, {
    title: $t('app.options.mem.sessionno.reqipaddr'),
    dataIndex: 'reqipaddr'
  }, {
    title: $t('app.options.mem.sessionno.reqport'),
    dataIndex: 'reqport'
  }, {
    title: $t('app.options.mem.sessionno.rspipaddr'),
    dataIndex: 'rspipaddr'
  }, {
    title: $t('app.options.mem.sessionno.rspport'),
    dataIndex: 'rspport'
  }, {
    title: $t('app.options.mem.sessionno.lastrecmsg'),
    dataIndex: 'lastrecmsg',
    render: text => formatDateFull(text),
  }, {
    title: $t('app.menu.db.appid'),
    dataIndex: 'appid'
  }, {
    title: $t('app.options.mem.sessionno.apiversion'),
    dataIndex: 'apiversion',
  }, {
    title: $t('app.options.mem.sessionno.prvsub'),
    dataIndex: 'prvsub',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: $t('app.options.mem.sessionno.pubsub'),
    dataIndex: 'pubsub',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 90,
    render: (_, record) => !record.istimeout ? (
      <ActionLinks links={[
        offlineLink(toOffline, record),
      ]} />
    ) : null
  }];

  const [userOptions, setUserOptions] = useState([]);

  const userFocus = useCallback(() => {
    getMemUserList(Request.post, defaultParams, setUserOptions);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userOptions, false), [$t, userFocus, userOptions]);

  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQrySession, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    }
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const deleteFunc = async (record, showMsg) => {
    const deleteData = {
      ...defaultParams,
      usersessionno: record.sessionno + '',
    }

    fetchFunc(MESSAGE_TYPE.MemUpdSession, deleteData, () => {
      showMsg && message.success(CURD.Offline + CURD.StatusOkMsg);
      onRefresh();
    });
  }
  const toOffline = record => {
    // console.log('Delete: ', record);
    if (record.istimeout !== 1) {
      Confirm({
        modal,
        title: $t('app.options.mem.sessionno.offline'),
        content: (
          <div>
            <Title level={4}>{record.userid + ':' + record.sessionno}</Title>
            <Text type="secondary">{$t('app.options.mem.sessionno.offline.tip')}</Text>
          </div>
        ),
        onOk: () => {
          // console.log('ok', record);
          deleteFunc(record, true);
        }
      });
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.session', {
        defaultMsg: 'SESSION信息',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          istimeout: 0,
        },
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const Session = props => {
  const param = useParams();

  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <SessionBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Session };
