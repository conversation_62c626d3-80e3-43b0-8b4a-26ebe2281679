import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, NegaNumber, TableTag, TableTitle } = TableFields;
const { exchangeAllDict, clientStatusDict, yesNoDict,tagNormalColors, tagColors, yesNoColor } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList } = getList;

const CommiMarginBase = props => {
  const { paramId } = props;

  const navigate = useNavigate();
  const queryForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = exchangeList && exchangeList[paramId];

  const financeColumns = [{
    title: $t('app.stock.instrument.allowedascollateral'),
    dataIndex: 'shortbymoney',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.collateralconvrate'),
      $t('app.stock.instrument.collateralconvrate.long'),
    ),
    dataIndex: 'marginratioparam1',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.instrument.allowedfi'),
    dataIndex: 'longbymoney',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.fimargin'),
      $t('app.stock.instrument.fimargin.long'),
    ),
    dataIndex: 'marginratioparam2',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.stock.instrument.allowedsl'),
    dataIndex: 'longbyvolume',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: TableTitle(
      $t('app.stock.instrument.slmargin'),
      $t('app.stock.instrument.slmargin.long'),
    ),
    dataIndex: 'margin',
    align: 'right',
    render: text => NegaNumber(text),
  }];

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.mem.userclient.status'),
    dataIndex: 'responsestr',
    render: text => TableTag(text, clientStatusDict, tagColors),
  }, {
    title: $t('app.options.mem.commimargin.openbymoney') + $t('app.stock.mem.commimargin.percent'),
    dataIndex: 'openbymoney',
    align: 'right',
    render: text => NegaNumber(text * 10000, 4),
  }, {
    title: $t('app.options.mem.commimargin.openbyvolume'),
    dataIndex: 'openbyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closebymoney') + $t('app.stock.mem.commimargin.percent'),
    dataIndex: 'closebymoney',
    align: 'right',
    render: text => NegaNumber(text * 10000, 4),
  }, {
    title: $t('app.options.mem.commimargin.closebyvolume'),
    dataIndex: 'closebyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closetodaybymoney') + $t('app.stock.mem.commimargin.percent'),
    dataIndex: 'closetodaybymoney',
    align: 'right',
    render: text => NegaNumber(text * 10000, 4),
  }, {
    title: $t('app.options.mem.commimargin.closetodaybyvolume'),
    dataIndex: 'closetodaybyvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.openmax'),
    dataIndex: 'openmax',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.openmin'),
    dataIndex: 'openmin',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closemax'),
    dataIndex: 'closemax',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.commimargin.closemin'),
    dataIndex: 'closemin',
    align: 'right',
    render: text => NegaNumber(text)
  }, ...(DEFAULT_CONFIGS.SHOW_MARGINFINANCING ? financeColumns : [])].filter(i => i);

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false, exchanges), [$t, userFocus, userClientGroup, exchanges]);

  useUnmount(() => cancel());

  // double类型有可能是长小数，转0或1
  const changeTo01 = num => num > 0.5 ? 1 : 0;

  const getTableData = async ({ noDataShow, ...params }) => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryCommiMargin, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
        shortbymoney: changeTo01(item.shortbymoney),
        longbymoney: changeTo01(item.longbymoney),
        longbyvolume: changeTo01(item.longbyvolume),
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0)
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    mutate: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    }
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...defaultParams,
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.user.commimargin', {
        defaultMsg: '手续费保证金',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          exchangeid: exchanges?.length === 1 ? Number(exchanges[0]) : null,
        },
        // onValuesChange: formValueChange,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const CommiMargin = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <CommiMarginBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { CommiMargin };
