import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { searchForm, addForm, resetPwdForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  dateFormat,
  updateRowData,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTag, TableTitle, NegaNumber } = TableFields;
const { ModalForm } = forms;
const { updateLink, resetPwdLink } = links;
const { userStatusDict, tagColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getMemUserList } = getList;
const { formatMsToDateFull } = dateFormat;

const MemUserInfoBase = props => {
  const { paramId } = props;

  const modalForm = useRef(null);
  const resetForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.db.userinfo.name'),
    dataIndex: 'username',
  }, {
    title: $t('app.options.db.userinfo.status'),
    dataIndex: 'status',
    render: text => TableTag(text, userStatusDict, tagColors),
  }, {
    title: $t('app.options.mem.userinfo.loginsuccess'),
    dataIndex: 'loginsuccess',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.loginfailed'),
    dataIndex: 'loginfailed',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.timeout'),
    dataIndex: 'timeout',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.logout'),
    dataIndex: 'logout',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.orderinsert'),
    dataIndex: 'orderinsert',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.ordertierr'),
    dataIndex: 'ordertierr',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.orderexerr'),
    dataIndex: 'orderexerr',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.ordercancel'),
    dataIndex: 'ordercancel',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.canceltierr'),
    dataIndex: 'canceltierr',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.cancelexerr'),
    dataIndex: 'cancelexerr',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.mem.userinfo.cancelmax'),
    dataIndex: 'password',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.db.userinfo.logincount'),
    dataIndex: 'logincount',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.db.userinfo.tradeflow'),
      $t('app.options.db.userinfo.tradeflow.long')
    ),
    dataIndex: 'tradeflow',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.mem.userinfo.maxloginsuccess'),
      $t('app.options.mem.userinfo.maxloginsuccess.long')
    ),
    dataIndex: 'maxloginsuccess',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.mem.userinfo.maxloginfailed'),
      $t('app.options.mem.userinfo.maxloginfailed.long')
    ),
    dataIndex: 'maxloginfailed',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.db.userinfo.lastloginip'),
    dataIndex: 'lastloginip'
  }, {
    title: $t('app.options.db.userinfo.lastlogintime'),
    dataIndex: 'lastlogintime',
    render: text => formatMsToDateFull(text),
  }, {
    title: $t('app.options.mem.userinfo.sequenceno'),
    dataIndex: 'sequenceno',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 170,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        resetPwdLink(toResetPwd, record),
      ]} />
    )
  }];

  const [userGroup, setUserGroup] = useState([]);
  const userFocus = useCallback(() => {
    getMemUserList(Request.post, defaultParams, setUserGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userGroup, false), [$t, userFocus, userGroup]);
  const createAddForm = useMemo(() => addForm($t), [$t]);
  const createResetPwdForm = useMemo(() => resetPwdForm($t), [$t]);

  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryUserInfo, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryUserInfo), {
      ...defaultParams,
      userid: record.userid,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }

    return null;
  }

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = (values) => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      modalForm.current.show(1, {
        ...newRecord,
        endno: newRecord.password,
      });

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
      });
      mutate(newData);
    } else {
      message.error($t('app.general.deleted'));
    }
  }
  const toResetPwd = record => {
    // console.log('ResetPwd', record);
    resetForm.current.show(2, record);
  }
  const addSubmit = async (form) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(MESSAGE_TYPE.MemUpdUserInfo, postData, () => {
        message.success((CURD.Update) + CURD.StatusOkMsg);
        onRefresh();
      });
  }
  const resetPwdSubmit = (form) => {
    const postData = {
      ...defaultParams,
      userid: form.userid,
      newpassword: form.newpassword,
    }

    fetchFunc(MESSAGE_TYPE.UpdUserPassword, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.user.muserinfo', {
        defaultMsg: '登录账户',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    {/* 修改 */}
    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />

    {/* 重置密码 */}
    <ModalForm
      ref={resetForm}
      onOk={resetPwdSubmit}
      formData={createResetPwdForm}
    />
  </>);
}

const MemUserInfo = props => {
  const param = useParams();

  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <MemUserInfoBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { MemUserInfo };
