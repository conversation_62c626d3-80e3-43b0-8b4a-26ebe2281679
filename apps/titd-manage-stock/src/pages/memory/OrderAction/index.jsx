import { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, App } from 'antd';
import dayjs from 'dayjs';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  currency,
  dateFormat,
  optDiffTwo,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTag, TableTitle, ErrorMsg } = TableFields;
const { ordActionTypeDict, ordStatusDict, ordStatusColor } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientGroup, getErrorCodeMsg } = getList;
const { getFloat } = currency;
const { formatTime } = dateFormat;

const OrderActionBase = props => {
  const { paramId } = props;
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [errorCodeMsg, setErrorCodeMsg] = useState([]);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);
  // const { get } = useFetch('');

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.userno}]`}
    </>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.order.cancel.cmdtype'),
    dataIndex: 'cmdtype',
    render: text => ordActionTypeDict[text] || text,
  }, {
    title: $t('app.options.order.cancel.ordtime'),
    dataIndex: 'ordtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
  //   title: '合约代码',
  //   dataIndex: 'instrumentid',
  //   render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  // }, {
    title: $t('app.options.order.exrequestid'),
    dataIndex: 'responsestr',
  }, {
    title: $t('app.options.order.exrequestno'),
    dataIndex: 'exrequestid',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {record.responsestr && <Text>{` [${record.responsestr}]`}</Text>}
    </>,
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno',
  }, {
    title: $t('app.options.order.cancel.ordresponsecode'),
    dataIndex: 'ordresponsecode',
    render: text => <ErrorMsg titdErrorCode={errorCodeMsg} errcode={text} />,
  }, {
    title: $t('app.options.order.cancel.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.orilocalorderno'),
    dataIndex: 'orilocalorderno',
  }, {
    title: $t('app.options.order.oriexrequestid.long'),
    dataIndex: 'oriexrequestid',
  }, {
    title: TableTitle(
      $t('app.options.order.orisessionno'),
      $t('app.options.order.orisessionno.long'),
    ),
    dataIndex: 'orisessionno',
  }, {
    title: $t('app.options.order.oriordersysid'),
    dataIndex: 'oriordersysid',
  // }, {
  //   title: '交易所报单编号',
  //   dataIndex: 'ordersysid',
  // }, {
  //   title: '交易编码类型',
  //   dataIndex: 'clienttype',
  //   render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.order.rec2send'),
    dataIndex: 'rec2send',
    render: text => getFloat(text, 4),
  // }, {
  //   title: '交易所耗时(毫秒)',
  //   dataIndex: 'rec2exrsp',
  //   render: text => getFloat(text, 4),
  // }, {
  //   title: '',
  //   dataIndex: 'ordstatus1',
  // }, {
  //   title: '',
  //   dataIndex: 'sseexreqnum',
  }, {
    title: $t('app.options.order.cpuid'),
    dataIndex: 'cpuid',
  // }, {
  //   title: '订单撤销数量',
  //   dataIndex: 'cancelvolume',
  // }, {
  //   title: '交易所席位',
  //   dataIndex: 'ssepartyid',
  // }, {
  //   title: '买方冻结',
  //   children: [{
  //     title: '冻结保证金',
  //     dataIndex: 'bfrozenmargin',
  //   }, {
  //     title: '冻结手续费',
  //     dataIndex: 'bfrozencommi',
  //   }, {
  //     title: '冻结权利金',
  //     dataIndex: 'bfrozenpremium',
  //   }, {
  //     title: '冻结数量',
  //     dataIndex: 'bfrozenvolume',
  //   }, {
  //     title: '已解冻数量',
  //     dataIndex: 'bthawvolume',
  //   }]
  // }, {
  //   title: '卖方冻结',
  //   children: [{
  //     title: '冻结保证金',
  //     dataIndex: 'sfrozenmargin',
  //   }, {
  //     title: '冻结手续费',
  //     dataIndex: 'sfrozencommi',
  //   }, {
  //     title: '冻结权利金',
  //     dataIndex: 'sfrozenpremium',
  //   }, {
  //     title: '冻结数量',
  //     dataIndex: 'sfrozenvolume',
  //   }, {
  //     title: '已解冻数量',
  //     dataIndex: 'sthawvolume',
  //   }]
  }];

  const [userOptions, setUserOptions] = useState([]);
  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientGroup(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userOptions, false), [$t, userFocus, userOptions]);

  useMount(() => {
    getErrorCodeMsg(Request.get, setErrorCodeMsg);
  });
  useUnmount(() => cancel());

  useEffect(() => {
    const users = optDiffTwo(userClientGroup);

    setUserOptions(users);
  }, [userClientGroup]);

  const getTableData = async params => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryOrderAction, params.svrid), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: params.beginno + item.rspseqno - 1,
        rec2send: getFloat(item.rec2send, 4),
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    refresh();
  }
  const onSearch = (values) => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
      begtime: values.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.ord.orderaction', {
        defaultMsg: '撤单信息',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        labelWidth: 145,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const OrderAction = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <OrderActionBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { OrderAction };
