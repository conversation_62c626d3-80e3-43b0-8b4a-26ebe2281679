import { useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

import Transfer from './Transfer';

const { Text } = Typography;
const { IndexColumn, NegaNumber } = TableFields;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getAccountList } = getList;

const CreditBase = props => {
  const { paramId } = props;

  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([1]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>,
    className: 'ti-group-border',
  }, {
    title: $t('app.stock.credit.rz'),
    className: 'ti-group-border',
    children: [{
      title: $t('app.stock.credit.precredit'),
      dataIndex: 'precreditRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.availcredit'),
      dataIndex: 'availcreditRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.usedcredit'),
      dataIndex: 'usedcreditRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.frzcredit'),
      dataIndex: 'frzcreditRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.common.deposit') + $t('app.stock.common.account'),
      dataIndex: 'depositRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.common.withdraw') + $t('app.stock.common.account'),
      dataIndex: 'withdrawRz',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
      className: 'ti-group-border',
    }]
  }, {
    title: $t('app.stock.credit.rq'),
    children: [{
      title: $t('app.stock.credit.precredit'),
      dataIndex: 'precreditRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.availcredit'),
      dataIndex: 'availcreditRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.usedcredit'),
      dataIndex: 'usedcreditRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.credit.frzcredit'),
      dataIndex: 'frzcreditRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.common.deposit') + $t('app.stock.common.account'),
      dataIndex: 'depositRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }, {
      title: $t('app.stock.common.withdraw') + $t('app.stock.common.account'),
      dataIndex: 'withdrawRq',
      align: 'right',
      render: text => NegaNumber(text, 2, 2),
    }]
  }];

  const [accountGroup, setAccountGroup] = useState([]);

  const createSearchForm = useMemo(() => searchForm($t, accountGroup), [$t, accountGroup]);

  useMount(() => {
    // 获取账户列表
    getAccountList(Request.post, defaultParams, setAccountGroup);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryCredit, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
      }));

      setTotal(respData[0].rsptotnum);
      // setSelectKeys([1]);
      return tableData;
    } else {
      setTotal(0);
      setSelectKeys([]);
      return [];
    }
  };

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const selectRecord = useMemo(() => {
    if (selectKeys.length > 0 && dataSource.length > 0) {
      return dataSource.find(i => i.id === selectKeys[0]);
    }

    return null;
  }, [selectKeys, dataSource]);

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.cdmanage.credit', {
        defaultMsg: '信用额度',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        page: page,
        total: total,
        pageChange: pageChange,
        rowClickable: true,
        selectionType: 'radio',
        selectKeys: selectKeys,
        setSelectKeys: keys => {
          setSelectKeys(keys);
        },
      }}
    />

    {/* 转移金额 */}
    {selectRecord && (
      <Transfer
        params={defaultParams}
        record={selectRecord}
        onSuccess={onRefresh}
      />
    )}
  </>);
}

const Credit = props => {
  const param = useParams();
  const paramId = param?.id || '';
  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <CreditBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Credit };
