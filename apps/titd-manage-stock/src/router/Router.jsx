import { Suspense } from 'react';
import { RouterProvider } from 'react-router/dom';
import { createHashRouter, Navigate } from 'react-router';

import { AdminLayout, AuthLayout } from '@titd/publics/layouts';
import {
  pubmemory,
  pubsystem,
  RequireAuth,
  Login,
  PageLoading,
  NoAuthPage,
  NotFoundPage,
  ErrorPage,
} from '@titd/publics/pages';
import {
  Dashboard,
  database,
  fund,
  memory,
} from '@/pages';

const {
  UserInfo,
  UserClient,
  AppId,
  TradeTpl,
  TradeRight,
  AssignFunds,
  FrozenFunds,
  ImportLog,
  UserRisk,
  StkCommiTpl,
  StkCommiTplUser,
  LimitAmount,
  LimitOrder,
  BlackImport,
  BlackList,
  TradeSum,
  SalesNumb,
  Gateway,
  Concentrate,
  ConcentraInstr,
  ExConctParam,
  AssignCreditCash,
  FairPrice,
} = database;
const {
  MemUserInfo,
  MemUserClient,
  Session,
  Account,
  CommiMargin,
  Risk,
  MemGateway,
  Instrument,
  Position,
  PositionDtl,
  OrderInsert,
  OrderAction,
  BusInsert,
  OrderTimeOut,
  TradeInfo,
  RtnOrder,
  RtnTrade,
  RtnOrderBus,
  RtnTradeBus,
  MemBlackList,
  Credit,
  CashAccount,
  CashStock,
  CreditPosition,
  RepayDtl,
} = memory;
const { RtnWithDraw, SelfTrade } = pubmemory;
const { SysLog, AdminUser, Roles } = pubsystem;
const {
  Amount,
  TransLog,
  Share,
  ShareTransLog,
  CreditTrans,
  CreditLog,
  CashTrans,
  CashLog,
} = fund;

const routers = [{
  path: '/',
  element: (
    <RequireAuth>
      <AdminLayout />
    </RequireAuth>
  ),
  children: [{
    index: true,
    element: <Navigate replace to="/dashboard" />
  }, {
    path: 'dashboard',
    element: <Dashboard />
  // }, {
  //   path: 'settings',
  //   element: <Settings keepName="settings" />
  }, { // 场下管理
    path: 'db',
    children: [{
      index: true,
      element: <Navigate replace to="/db/userinfo" />
    }, {
      path: 'userinfo',
      element: <UserInfo keepName="userinfo" />
    }, {
      path: 'userclient',
      element: <UserClient keepName="userclient" />
    }, {
      path: 'appid',
      element: <AppId keepName="appid" />
    }, {
      path: 'tradetpl',
      element: <TradeTpl keepName="tradetpl" />
    }, {
      path: 'traderight',
      element: <TradeRight keepName="traderight" />
    }, {
      path: 'assignfunds',
      element: <AssignFunds keepName="assignfunds" />
    }, {
      path: 'frozenfunds',
      element: <FrozenFunds keepName="frozenfunds" />
    }, {
      path: 'importlog',
      element: <ImportLog keepName="importlog" />
    }, {
      path: 'userrisk',
      element: <UserRisk keepName="userrisk" />
    }, {
      path: 'stkcommitpl',
      element: <StkCommiTpl keepName="stkcommitpl" />
    }, {
      path: 'stkcommitpluser',
      element: <StkCommiTplUser keepName="stkcommitpluser" />
    }, {
      path: 'limit',
      children: [{
        path: 'limitamount',
        element: <LimitAmount keepName="limitamount" />
      }, {
        path: 'limitorder',
        element: <LimitOrder keepName="limitorder" />
      }]
    }, {
      path: 'black',
      children: [{
        path: 'blackimport',
        element: <BlackImport keepName="blackimport" />
      }, {
        path: 'blacklist',
        element: <BlackList keepName="blacklist" />
      }]
    }, {
      path: 'tradesum',
      element: <TradeSum keepName="tradesum" />
    }, {
      path: 'salesnumb',
      element: <SalesNumb keepName="salesnumb" />
    }, {
      path: 'gateway',
      element: <Gateway keepName="gateway" />
    }, {
      path: 'martrade',
      children: [{
        path: 'concentrate',
        element: <Concentrate keepName="concentrate" />
      }, {
        path: 'concentrainstr',
        element: <ConcentraInstr keepName="concentrainstr" />
      }, {
        path: 'exconctparam',
        element: <ExConctParam keepName="exconctparam" />
      }, {
        path: 'assigncreditcash',
        element: <AssignCreditCash keepName="assigncreditcash" />
      }, {
        path: 'fairprice',
        element: <FairPrice keepName="fairprice" />
      }]
    }]
  }, { // 场上管理
    path: 'mem',
    children: [{
      path: ':id',
      children: [{
        path: 'user',
        children: [{
          path: 'muserinfo',
          element: <MemUserInfo keepName="muserinfo" />
        }, {
          path: 'muserclient',
          element: <MemUserClient keepName="muserclient" />
        }, {
          path: 'session',
          element: <Session keepName="session" />
        }, {
          path: 'account',
          element: <Account keepName="account" />
        }, {
          path: 'commimargin',
          element: <CommiMargin keepName="commimargin" />
        }, {
          path: 'risk',
          element: <Risk keepName="risk" />
        }, {
          path: 'mgateway',
          element: <MemGateway keepName="mgateway" />
        }]
      }, {
        path: 'instrument',
        element: <Instrument keepName="instrument" />
      }, {
        path: 'pos',
        children: [{
          path: 'position',
          element: <Position keepName="position" />
        }, {
          path: 'positiondtl',
          element: <PositionDtl keepName="positiondtl" />
        }]
      }, {
        path: 'ord',
        children: [{
          path: 'orderinsert',
          element: <OrderInsert keepName="orderinsert" />
        }, {
          path: 'orderaction',
          element: <OrderAction keepName="orderaction" />
        }, {
          path: 'businsert',
          element: <BusInsert keepName="businsert" />
        }, {
          path: 'ordertimeout',
          element: <OrderTimeOut keepName="ordertimeout" />
         }]
      }, {
        path: 'tradeinfo',
        element: <TradeInfo keepName="tradeinfo" />
      }, {
        path: 'rtn',
        children: [{
          path: 'rtnorder',
          element: <RtnOrder keepName="rtnorder" />
        }, {
          path: 'rtntrade',
          element: <RtnTrade keepName="rtntrade" />
        }, {
          path: 'rtnorderbus',
          element: <RtnOrderBus keepName="rtnorderbus" />
        }, {
          path: 'rtntradebus',
          element: <RtnTradeBus keepName="rtntradebus" />
        }, {
          path: 'rtnwithdraw',
          element: <RtnWithDraw keepName="rtnwithdraw" />
        // }, {
        //   path: 'rtnshare',
        //   element: <RtnShare keepName="rtnshare" />
        }]
      }, {
        path: 'err',
        children: [{
          path: 'selftrade',
          element: <SelfTrade keepName="selftrade" />
        }, {
          path: 'memblacklist',
          element: <MemBlackList keepName="memblacklist" />
        }]
      }, {
        path: 'cdmanage',
        children: [{
          path: 'credit',
          element: <Credit keepName="credit" />
        }, {
          path: 'instrument',
          element: <Instrument keepName="instrument" />
        }]
      }, {
        path: 'cdcash',
        children: [{
          path: 'cashaccount',
          element: <CashAccount keepName="cashaccount" />
        }, {
          path: 'cashstock',
          element: <CashStock keepName="cashstock" />
        }]
      }, {
        path: 'cdpos',
        children: [{
          path: 'cdposition',
          element: <CreditPosition keepName="cdposition" />
        }, {
          path: 'repaydtl',
          element: <RepayDtl keepName="repaydtl" />
        }]
      }]
    }]
  }, { // 资金管理
    path: 'fund',
    children: [{
      index: true,
      element: <Navigate replace to="/fund/amount" />
    }, {
      path: 'amount',
      element: <Amount keepName="amount" />
    }, {
      path: 'translog',
      element: <TransLog keepName="translog" />
    }, {
      path: 'share',
      element: <Share keepName="share" />
    }, {
      path: 'sharetranslog',
      element: <ShareTransLog keepName="sharetranslog" />
    }, {
      path: 'credittrans',
      element: <CreditTrans keepName="credittrans" />
    }, {
      path: 'creditlog',
      element: <CreditLog keepName="creditlog" />
    }, {
      path: 'cashtrans',
      element: <CashTrans keepName="cashtrans" />
    }, {
      path: 'cashlog',
      element: <CashLog keepName="cashlog" />
    }]
  }, { // 系统管理
    path: 'sys',
    children: [{
      index: true,
      element: <Navigate replace to="/sys/syslog" />
    }, {
      path: 'syslog',
      element: <SysLog keepName="syslog" />
    }, {
      path: 'adminuser',
      element: <AdminUser keepName="adminuser" />
    }, {
      path: 'roles',
      element: <Roles keepName="roles" />
    }]
//   }, ...routeHistPart, {
  }, {
    path: '*',
    element: <Navigate replace to="/exceptions/404" />
  }]
}, { // 登录相关
  path: 'auth',
  element: <AuthLayout />,
  children: [{
    index: true,
    element: <Navigate replace to="/auth/login" />
  }, {
    path: 'login',
    element: <Login />
  // }, {
  //   path: 'updatepwd',
  //   element: <UpdatePwd />
  // }, {
  //   path: 'unlock',
  //   element: <Unlock />
  }]
}, { // 页面错误相关
  path: 'exceptions',
  children: [{
    path: '403',
    element: <NoAuthPage />
  }, {
    path: '404',
    element: <NotFoundPage />
  }, {
    path: '500',
    element: <ErrorPage />
  }]
}, {
  path: '*',
  element: <Navigate replace to="/exceptions/404" />
}];

export const Router = () => (
  <Suspense fallback={<PageLoading />}>
    <RouterProvider router={createHashRouter(routers)} />
  </Suspense>
);
