#!/bin/bash
BUILD_PATH=./dist
RELEASE_NAME=titd-admin-options
RELEASE_PATH=./release/

ERRORCODE=../../errcodes/titderrorcode-stkopt.json
ERRORCODE_NEW=./public/data/titderrorcode.json

# 使用说明，用来提示输入参数
usage() {
  echo "Usage sh run.sh [dev|build]"
  exit 1
}

# 拷贝errocdemsg文件到目录
copy_errcode() {
  cp $ERRORCODE $ERRORCODE_NEW
}

# 自动设置版本号
auto_version() {
  pnpm run version
}

# 打包生成的文件
zip_build() {
  local name="$1"
  local current_date=$(date +'%Y-%m-%d')

  if [ -n "$name" ]; then
    zip_name="${RELEASE_NAME}_${name}_${current_date}.zip"
  else
    zip_name="${RELEASE_NAME}_${current_date}.zip"
  fi
  output_zip="${RELEASE_PATH}${zip_name}"

  if [ -f "$output_zip" ]; then
    rm "$output_zip"
  fi
  cd $BUILD_PATH && zip -r ".$output_zip" * && cd ../
}

# 测试环境打包
dev() {
  TITD_SYSTEMID=0 pnpm run build:dev
}

# 发布版本打包
build() {
  copy_errcode
  auto_version

  build_normal
  build_sysid
}

build_normal() {
  TITD_SYSTEMID=0 pnpm run build

  zip_build
}

build_sysid() {
  pnpm run build

  zip_build "systemid"
}

# 根据输入参数，选择执行对应方法，不输入则执行使用说明
case "$1" in
  "dev")
    dev
    ;;
  "build")
    build
    ;;
  *)
    usage
    ;;
esac
