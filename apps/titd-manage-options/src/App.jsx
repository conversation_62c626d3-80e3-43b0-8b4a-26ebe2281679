import { RecoilRoot } from 'recoil';
import { IntlProvider } from 'react-intl';
import { ConfigProvider } from 'antd';
import { StyleProvider } from '@ant-design/cssinjs';
import dayjs from 'dayjs';

import { loadLocale } from '@titd/publics/assets';
import { Router } from './router/Router';

// import './App.less';

const App = () => {
  const { locale, message, antLocale, dayjsLocale } = loadLocale('zh-CN');
  // 日期控件中文
  dayjs.locale(dayjsLocale);

  return (
    <RecoilRoot>
      <IntlProvider locale={locale} messages={message}>
        <ConfigProvider locale={antLocale}>
          <StyleProvider hashPriority="high">
            <Router />
          </StyleProvider>
        </ConfigProvider>
      </IntlProvider>
    </RecoilRoot>
  );
};

export default App;