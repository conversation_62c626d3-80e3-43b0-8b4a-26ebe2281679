import { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, TableTag, TableBadgeDot } from '@components/Table/fields';
import { searchForm } from '@pages/memory/Session/data';

import { isTimeoutDict, yesNoDict, tagColors, yesNoColor } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserList } from '@utils/common';
import { formatMsToDateFull, formatDate } from '@utils/date-format';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistSession = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.mem.sessionno.istimeout'),
    dataIndex: 'istimeout',
    render: text => TableBadgeDot(text, isTimeoutDict, tagColors)
  }, {
    title: $t('app.options.mem.sessionno.reqipaddr'),
    dataIndex: 'reqipaddr'
  }, {
    title: $t('app.options.mem.sessionno.reqport'),
    dataIndex: 'reqport'
  }, {
    title: $t('app.options.mem.sessionno.rspipaddr'),
    dataIndex: 'rspipaddr'
  }, {
    title: $t('app.options.mem.sessionno.rspport'),
    dataIndex: 'rspport'
  }, {
    title: $t('app.options.db.userinfo.lastlogintime'),
    dataIndex: 'logintime',
    render: text => formatMsToDateFull(text),
  }, {
    title: $t('app.options.mem.sessionno.lastrecmsg'),
    dataIndex: 'lastrecmsg',
    render: text => formatMsToDateFull(text),
  }, {
    title: $t('app.menu.db.appid'),
    dataIndex: 'appid'
  }, {
    title: $t('app.options.mem.sessionno.apiversion'),
    dataIndex: 'apiversion',
  }, {
    title: $t('app.options.mem.sessionno.prvsub'),
    dataIndex: 'prvsub',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: $t('app.options.mem.sessionno.pubsub'),
    dataIndex: 'pubsub',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }].filter(i => i), [intl, checkDate]);

  const [userOptions, setUserOptions] = useState([]);
  const userFocus = useCallback(() => {
    getHistUserList(request.get, checkDate, setUserOptions);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userOptions, loading), [intl, userFocus, userOptions, loading]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('Session', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };
  const onRefresh = () => {
    setFilterParams({...filterParams});
  }
  const onSearch = (values) => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          istimeout: 0,
        },
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistSessionAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistSession {...props} />
  </KeepAlive>
);

export default HistSessionAlive;
