import { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import dayjs from 'dayjs';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useMount, useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, TableTag, TableTitle, ErrorMsg } from '@components/Table/fields';
import { searchForm } from '@pages/memory/OrderAction/data';

import { ordActionTypeDict, ordStatusDict, ordStatusColor } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserList, getErrorCodeMsg } from '@utils/common';
import { formatDate, formatTime } from '@utils/date-format';
import { getFloat } from '@utils/currency';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistOrderAction = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [errorCodeMsg, setErrorCodeMsg] = useState([]);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();
  const { get: eGet } = useFetch('');

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.userno}]`}
    </>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.order.cancel.cmdtype'),
    dataIndex: 'cmdtype',
    render: text => ordActionTypeDict[text] || text,
  }, {
    title: $t('app.options.order.cancel.ordtime'),
    dataIndex: 'ordtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.order.exrequestid'),
    dataIndex: 'exrequestid',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {record.responsestr && <Text>{` [${record.responsestr}]`}</Text>}
    </>,
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno',
  }, {
    title: $t('app.options.order.cancel.ordresponsecode'),
    dataIndex: 'ordresponsecode',
    render: text => <ErrorMsg titdErrorCode={errorCodeMsg} errcode={text} />,
  }, {
    title: $t('app.options.order.cancel.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.orilocalorderno'),
    dataIndex: 'orilocalorderno',
  }, {
    title: $t('app.options.order.oriexrequestid.long'),
    dataIndex: 'oriexrequestid',
  }, {
    title: TableTitle(
      $t('app.options.order.orisessionno'),
      $t('app.options.order.orisessionno.long'),
    ),
    dataIndex: 'orisessionno',
  }, {
    title: $t('app.options.order.oriordersysid'),
    dataIndex: 'oriordersysid',
  }, {
    title: $t('app.options.order.rec2send'),
    dataIndex: 'rec2send',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.cpuid'),
    dataIndex: 'cpuid',
  }].filter(i => i), [intl, checkDate, errorCodeMsg]);

  const [userOptions, setUserOptions] = useState([]);
  const userFocus = useCallback(() => {
    getHistUserList(request.get, checkDate, setUserOptions);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userOptions, loading, false), [intl, userFocus, userOptions, loading]);

  useMount(() => {
    getErrorCodeMsg(eGet, setErrorCodeMsg);
  });
  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('OrderAction', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
          rec2send: getFloat(item.rec2send, 4),
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    filterParams && setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      begtime: values.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    setFilterParams(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        labelWidth: 145,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistOrderActionAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistOrderAction {...props} />
  </KeepAlive>
);

export default HistOrderActionAlive;
