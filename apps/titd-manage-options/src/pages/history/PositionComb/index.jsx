import { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, NegaNumber, TableBadgeDot, TableTitle } from '@components/Table/fields';
import { searchForm } from '@pages/memory/PositionComb/data';

import { exchangeDict, legSideDict, coveredDict, tagNormalColors } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserClientList } from '@utils/common';
import { formatDate } from '@utils/date-format';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistPositionComb = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.postion.combinstid'),
      $t('app.options.postion.combinstid.long')
    ),
    dataIndex: 'combinstid',
  }, {
    title: $t('app.options.postion.omlid'),
    dataIndex: 'omlid',
  }, {
    title: $t('app.options.postion.volume'),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.postion.frozenvolume'),
      $t('app.options.postion.frozenvolume.long')
    ),
    dataIndex: 'frozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.postion.legmargin'),
    dataIndex: 'margin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.postion.noleges'),
    dataIndex: 'noleges',
    align: 'right',
    render: text => NegaNumber(text),
    className: 'ti-group-border',
  }, {
    title: $t('app.options.postion.legsecurityid') + '1',
    dataIndex: 'legsecurityid1',
    render: (text, record) => text !== '' ? <Text>{`${text} [${record.legsecurityno1}]`}</Text> : '',
  }, {
    title: $t('app.options.postion.legside') + '1',
    dataIndex: 'legside1',
    render: text => legSideDict[String.fromCharCode(text)] || text,
  }, {
    title: $t('app.options.postion.covereduncovered') + '1',
    dataIndex: 'coveredoruncovered1',
    render: text => coveredDict[text] || text,
  }, {
    title: $t('app.options.postion.legorderqty') + '1',
    dataIndex: 'legorderqty1',
    align: 'right',
    render: text => NegaNumber(text),
    className: 'ti-group-border',
  }, {
    title: $t('app.options.postion.legsecurityid') + '2',
    dataIndex: 'legsecurityid2',
    render: (text, record) => text !== '' ? <Text>{`${text} [${record.legsecurityno2}]`}</Text> : '',
  }, {
    title: $t('app.options.postion.legside') + '2',
    dataIndex: 'legside2',
    render: text => legSideDict[String.fromCharCode(text)] || text,
  }, {
    title: $t('app.options.postion.covereduncovered') + '2',
    dataIndex: 'coveredoruncovered2',
    render: text => coveredDict[text] || text,
  }, {
    title: $t('app.options.postion.legorderqty') + '2',
    dataIndex: 'legorderqty2',
    align: 'right',
    render: text => NegaNumber(text),
  }].filter(i => i), [intl, checkDate]);

  const [userClientGroup, setUserClientGroup] = useState([]);
  const userFocus = useCallback(() => {
    getHistUserClientList(request.get, checkDate, setUserClientGroup);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userClientGroup, loading, null, false), [intl, userFocus, userClientGroup, loading]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('PositionComb', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    filterParams && setFilterParams({...filterParams});
  }
  const onSearch = (values) => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    setFilterParams(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistPositionCombAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistPositionComb {...props} />
  </KeepAlive>
);

export default HistPositionCombAlive;
