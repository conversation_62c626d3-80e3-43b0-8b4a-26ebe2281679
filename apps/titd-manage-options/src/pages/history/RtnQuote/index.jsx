import { useRef, useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import dayjs from 'dayjs';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount, useDebounceFn } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { TableTitle, IndexColumn, TableTag, NegaNumber, TableBadgeDot } from '@components/Table/fields';
import { searchForm } from '@pages/memory/RtnQuote/data';

import { exchangeDict, ordStatusDict, ownerTypeDict, offsetFlagDict, ordStatusColor, tagNormalColors } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserClientList, getClientByUser } from '@utils/common';
import { formatDate, formatTime } from '@utils/date-format';
import { optDiffTwo } from '@utils/opt-diff';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistRtnQuote = () => {
  const queryForm = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.rtn.sequenceno'),
    dataIndex: 'sequenceno',
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.rtn.transtime'),
    dataIndex: 'transtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno'
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.order.quotereqid'),
    dataIndex: 'quotereqid',
  }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    className: 'ti-group-border',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.buy'),
    className: 'ti-group-border',
    children: [{
      title: TableTitle(
        $t('app.options.rtn.bidorderid'),
        $t('app.options.rtn.bidorderid.long')
      ),
      dataIndex: 'bidorderid',
    }, {
      title: $t('app.options.order.quote.px'),
      dataIndex: 'bidpx',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }, {
      title: $t('app.options.order.volume'),
      dataIndex: 'bidsize',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.order.quote.offsetflag'),
      dataIndex: 'bidoffsetflag',
      className: 'ti-group-border',
      render: text => offsetFlagDict[text] || text,
    }]
  }, {
    title: $t('app.options.order.sell'),
    className: 'ti-group-border',
    children: [{
      title: TableTitle(
        $t('app.options.rtn.askorderid'),
        $t('app.options.rtn.askorderid.long')
      ),
      dataIndex: 'askorderid',
    }, {
      title: $t('app.options.order.quote.px'),
      dataIndex: 'askpx',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }, {
      title: $t('app.options.order.volume'),
      dataIndex: 'asksize',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.order.quote.offsetflag'),
      dataIndex: 'askoffsetflag',
      className: 'ti-group-border',
      render: text => offsetFlagDict[text] || text,
    }]
  }, {
    title: $t('app.options.order.ownertype'),
    dataIndex: 'ownertype',
    render: text => ownerTypeDict[text] || text,
  }, {
    title: TableTitle(
      $t('app.options.userid'),
      $t('app.options.rtn.userid.long')
    ),
    dataIndex: 'userid',
  }].filter(i => i), [intl, checkDate]);

  const [userClientGroup, setUserClientGroup] = useState([]);
  const [userOptions, setUserOptions] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);

  const userFocus = useCallback(() => {
    getHistUserClientList(request.get, checkDate, setUserClientGroup, true);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userOptions, loading, clientOptions, false), [intl, userFocus, userOptions, loading, clientOptions]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const users = optDiffTwo(userClientGroup);

    setUserOptions(users);
  }, [userClientGroup]);

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('RtnQuote', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    filterParams && setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      begtime: values?.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values?.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    setFilterParams(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }
    // 重置股东代码
    setClientOptions([]);

    setFilterParams(defaultParams);
  }

  const { run } = useDebounceFn(async (value) => {
    if (value) {
      const clientGroup = getClientByUser(userClientGroup, value);

      setClientOptions(clientGroup);
    } else {
      setClientOptions([]);
    }

    // 交易编码赋值
    queryForm.current?.set({
      'clientid': undefined,
    });
  }, { wait: 500 });
  const formValueChange = changedValues => {
    if ('userid' in changedValues) {
      const value = changedValues.userid;
      run(value);
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        onValuesChange: formValueChange,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistRtnQuoteAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistRtnQuote {...props} />
  </KeepAlive>
);

export default HistRtnQuoteAlive;
