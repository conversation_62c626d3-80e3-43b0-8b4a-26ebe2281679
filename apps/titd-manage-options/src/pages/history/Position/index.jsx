import { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, NegaNumber, TableBadgeDot, TableTitle } from '@components/Table/fields';
import { searchForm } from '@pages/memory/Position/data';

import { exchangeDict, tagNormalColors } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserClientList } from '@utils/common';
import { formatDate } from '@utils/date-format';
import { getFloat } from '@utils/currency';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistPosition = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.postion.l'),
    className: 'ti-group-border',
    children: [{
      title: TableTitle(
        $t('app.options.postion.position'),
        $t('app.options.postion.position.long')
      ),
      dataIndex: 'lposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.ydposition'),
        $t('app.options.postion.ydposition.long')
      ),
      dataIndex: 'lydposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.total'),
        $t('app.options.postion.tips')
      ),
      dataIndex: 'lpositionall',
      align: 'right',
      render: text => <Text strong>{NegaNumber(text)}</Text>
    }, {
      title: $t('app.options.postion.frozen'),
      dataIndex: 'lfrozen',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.buy'),
      dataIndex: 'lbuy',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.sell'),
      dataIndex: 'lsell',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.holdprice'),
        $t('app.options.postion.holdprice.long')
      ),
      dataIndex: 'lholdprice',
      align: 'right',
      className: 'ti-group-border',
      render: text => NegaNumber(text, 4, 4),
    }]
  }, {
    title: $t('app.options.postion.s'),
    className: 'ti-group-border',
    children: [{
      title: TableTitle(
        $t('app.options.postion.position'),
        $t('app.options.postion.position.long')
      ),
      dataIndex: 'sposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.ydposition'),
        $t('app.options.postion.ydposition.long')
      ),
      dataIndex: 'sydposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.total'),
        $t('app.options.postion.tips')
      ),
      dataIndex: 'spositionall',
      align: 'right',
      render: text => <Text strong>{NegaNumber(text)}</Text>
    }, {
      title: $t('app.options.postion.frozen'),
      dataIndex: 'sfrozen',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.buy'),
      dataIndex: 'sbuy',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.sell'),
      dataIndex: 'ssell',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.holdprice'),
        $t('app.options.postion.holdprice.long')
      ),
      dataIndex: 'sholdprice',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }, {
      title: TableTitle(
        $t('app.options.postion.margin'),
        $t('app.options.postion.margin.long')
      ),
      dataIndex: 'smargin',
      align: 'right',
      className: 'ti-group-border',
      render: text => NegaNumber(text, 2, 2),
    }]
  }, {
    title: $t('app.options.postion.c'),
    children: [{
      title: TableTitle(
        $t('app.options.postion.position'),
        $t('app.options.postion.position.long')
      ),
      dataIndex: 'cposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.ydposition'),
        $t('app.options.postion.ydposition.long')
      ),
      dataIndex: 'cydposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.total'),
        $t('app.options.postion.tips')
      ),
      dataIndex: 'cpositionall',
      align: 'right',
      render: text => <Text strong>{NegaNumber(text)}</Text>
    }, {
      title: $t('app.options.postion.frozen'),
      dataIndex: 'cfrozen',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.buy'),
      dataIndex: 'cbuy',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.sell'),
      dataIndex: 'csell',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.holdprice'),
        $t('app.options.postion.holdprice.long')
      ),
      dataIndex: 'choldprice',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }]
  }].filter(i => i), [intl, checkDate]);

  const [userClientGroup, setUserClientGroup] = useState([]);
  const userFocus = useCallback(() => {
    getHistUserClientList(request.get, checkDate, setUserClientGroup);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userClientGroup, loading), [intl, userFocus, userClientGroup, loading]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('Position', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
          lmargin: getFloat(item.lmargin, 4),
          smargin: getFloat(item.smargin, 4),
          cmargin: getFloat(item.cmargin, 4),
          lpositionall: item.lposition + item.lydposition,
          spositionall: item.sposition + item.sydposition,
          cpositionall: item.cposition + item.cydposition,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    setFilterParams(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistPositionAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistPosition {...props} />
  </KeepAlive>
);

export default HistPositionAlive;
