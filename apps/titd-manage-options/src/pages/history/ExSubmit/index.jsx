import { useState, useMemo, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, Typography } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import TiTable from '@components/Table';
import { IndexColumn } from '@components/Table/fields';

import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { formatDate } from '@utils/date-format';

const { Text } = Typography;

const HistExSubmit = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.db.exsubmit.serverid'),
    dataIndex: 'serverid',
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.db.exsubmit.loginid'),
    dataIndex: 'loginid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.db.exsubmit.submitinfo'),
    dataIndex: 'submitinfo',
    render: text => <div style={{ maxWidth: '1200px' }}>{text}</div>,
  }].filter(i => i), [intl, checkDate]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('ExSubmit', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    setFilterParams({...filterParams});
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <Card>
      <TiTable
        columns={columns}
        dataSource={dataSource}
        isLoading={isLoading}
        onRefresh={onRefresh}
        checkable={false}
        page={page}
        total={total}
        pageChange={pageChange}
      />
    </Card>
  );
}

const HistExSubmitAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistExSubmit {...props} />
  </KeepAlive>
);

export default HistExSubmitAlive;
