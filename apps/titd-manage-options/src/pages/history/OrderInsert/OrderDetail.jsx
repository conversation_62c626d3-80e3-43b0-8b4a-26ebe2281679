import { useEffect, useState } from 'react';
import { Typography } from 'antd';
import useFetch from 'use-http';
import { useIntl } from 'react-intl';

import TableSimple from '@components/Table/Simple';
import { TableTag, IndexColumn, NegaNumber } from '@components/Table/fields';

import { ordStatusDict, ordStatusColor } from '@utils/dicts';
import { SITE_URL } from '@utils/consts';
import { formatTime } from '@utils/date-format';

import Descs from '@pages/memory/OrderInsert/Descs';
import { baseMap } from '@pages/memory/OrderInsert/map';

const { Text } = Typography;

const OrderDetail = props => {
  const {
    order,
    params,
    checkDate,
  } = props;

  const intl = useIntl();

  const [base, setBase] = useState([]);
  const [rtnData, setRtnData] = useState([]);

  const { get, response, loading: isLoading, abort } = useFetch();

  const columns = [{
    title: $t('app.options.rtn.sequenceno'),
    dataIndex: 'sequenceno',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.rtn.transtime'),
    dataIndex: 'transtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.price'),
    dataIndex: 'price',
    align: 'right',
    render: text => NegaNumber(text)
  }, {
    title: $t('app.options.order.volume'),
    dataIndex: 'volume',
    align: 'right',
  }, {
    title: $t('app.options.rtn.leavesvolume'),
    dataIndex: 'leavesvolume',
    align: 'right',
  }, {
    title: $t('app.options.order.cancelvolume'),
    dataIndex: 'cancelvolume',
    align: 'right',
  }, {
    title: $t('app.options.order.minvolume'),
    dataIndex: 'minvolume',
    align: 'right',
  }, {
    title: $t('app.options.order.stopprice'),
    dataIndex: 'stopprice',
    align: 'right',
    render: text => NegaNumber(text),
  }];

  const getGroupData = async record => {
    // 基础消息
    const baseData = baseMap(intl, record);
    setBase(baseData);

    const postData = {
      ...params,
      clientid: record.clientid,
      ordersysid: record.ordersysid,
      userid: record.userid,
      histat: checkDate,
    };

    // 委托
    const resp = await get(SITE_URL.HISTORY_PATH('RtnOrder', postData));
    if (response.ok && resp?.list.length > 0) {
      setRtnData(resp.list);
    }
  }

  useEffect(() => {
    getGroupData(order);

    return () => {
      abort();
      setBase([]);
      setRtnData([]);
    }
  }, [order]); // eslint-disable-line react-hooks/exhaustive-deps

  return (<>
    <Descs list={base} />
    <TableSimple
      customKey={'sequenceno'}
      columns={columns}
      dataSource={rtnData}
      isLoading={isLoading}
    />
  </>);
}

export default OrderDetail;
