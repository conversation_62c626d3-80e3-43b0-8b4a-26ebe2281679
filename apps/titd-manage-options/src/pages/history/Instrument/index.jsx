import { useState, useMemo, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import dayjs from 'dayjs';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useUnmount } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { TableTag, IndexColumn, NegaNumber, TableBadgeDot, TableTitle } from '@components/Table/fields';
import { searchForm } from '@pages/memory/Instrument/data';

import { exchangeDict, productDict, optionsDict, yesNoDict, tagNormalColors, yesNoColor } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { formatDate } from '@utils/date-format';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistInstrument = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    fixed: 'left',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    fixed: 'left',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.instrumentno}]`}
    </>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.instrument.instrumentname'),
    dataIndex: 'instrumentname',
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
  }, {
    title: $t('app.options.underlyinginstrid'),
    dataIndex: 'underlyinginstrid',
  }, {
    title: $t('app.options.instrument.producttype'),
    dataIndex: 'producttype',
    render: text => productDict[String.fromCharCode(text)] || text,
  }, {
    title: $t('app.options.instrument.optionstype'),
    dataIndex: 'optionstype',
    render: text => text === 0 ? '-' : optionsDict[String.fromCharCode(text)] || text,
  // }, {
  //   title: '合约数量乘数',
  //   dataIndex: 'volumemultiple',
  }, {
    title: $t('app.options.instrument.lastprice'),
    dataIndex: 'lastprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.settlementprice'),
    dataIndex: 'settlementprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: TableTitle(
      $t('app.options.instrument.marginprice'),
      $t('app.options.instrument.marginprice.long')
    ),
    dataIndex: 'marginprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: TableTitle(
      $t('app.options.instrument.undermarginprice'),
      $t('app.options.instrument.undermarginprice.long')
    ),
    dataIndex: 'undermarginprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.upperlimitprice'),
    dataIndex: 'upperlimitprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.lowerlimitprice'),
    dataIndex: 'lowerlimitprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.volumemultiple'),
    dataIndex: 'volumemultiple',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.stockprice'),
    dataIndex: 'stockprice',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.deliveryyear'),
    dataIndex: 'deliveryyear',
    align: 'right',
    render: text => text || '-',
  }, {
    title: $t('app.options.instrument.deliverymonth'),
    dataIndex: 'deliverymonth',
    align: 'right',
    render: text => text || '-',
  }, {
    title: $t('app.options.instrument.advancemonth'),
    dataIndex: 'advancemonth',
    align: 'right',
    render: text => text || '-',
  // }, {
  //   title: '交易权限',
  //   dataIndex: 'tradingright',
  //   render: text => TableTag(text, yesNoColor),
  }, {
    title: $t('app.options.instrument.createdate'),
    dataIndex: 'createdate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.opendate'),
    dataIndex: 'opendate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.expiredate'),
    dataIndex: 'expiredate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.startdelivdate'),
    dataIndex: 'startdelivdate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.enddelivdate'),
    dataIndex: 'enddelivdate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.basisprice'),
    dataIndex: 'basisprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.strikeprice'),
    dataIndex: 'strikeprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.maxmarketordervolume'),
    dataIndex: 'maxmarketordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.minmarketordervolume'),
    dataIndex: 'minmarketordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.maxlimitordervolume'),
    dataIndex: 'maxlimitordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.minlimitordervolume'),
    dataIndex: 'minlimitordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.pricetick'),
    dataIndex: 'pricetick',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.allowdelivpersonopen'),
    dataIndex: 'allowdelivpersonopen',
  }, {
    title: TableTitle(
      $t('app.options.instrument.tradedays'),
      $t('app.options.instrument.tradedays.long')
    ),
    dataIndex: 'tradedays',
    align: 'right',
  }, {
    title: $t('app.options.instrument.tplus1'),
    dataIndex: 'tplus1',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }].filter(i => i), [intl, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl), [intl]);

  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('Instrument', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    setFilterParams({...filterParams});
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams({
      ...defaultParams,
      ...objFilter(values),
      expiredate: values?.expiredate ? dayjs(values.expiredate).format('YYYYMMDD') : '',
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        formData: createSearchForm,
        isLoading: isLoading,
        resetSearch: false,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistInstrumentAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistInstrument {...props} />
  </KeepAlive>
);

export default HistInstrumentAlive;
