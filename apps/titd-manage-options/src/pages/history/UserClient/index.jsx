import { useRef, useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Typography } from 'antd';
import useFetch from 'use-http';
import KeepAlive from 'react-activation';
import { useIntl } from 'react-intl';
import { useMount, useUnmount, useDebounceFn } from 'ahooks';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import PageContent from '@components/PageContent';
import { IndexColumn, TableTag, TableBadgeDot } from '@components/Table/fields';
import { searchForm } from '@pages/memory/UserClient/data';

import { clientTypeDict, accountTypeDict, exchangeDict, clientStatusDict, clientRightDict, tagNormalColors, tagColors } from '@utils/dicts';
import { DEFAULT_CONFIGS, SITE_URL, DEFAULT_HISTORY_PARAMS } from '@utils/consts';
import { histErrorResp } from '@utils/error-response';
import { getHistUserClientList, getClientByUser } from '@utils/common';
import { formatDate } from '@utils/date-format';
import { optDiff, optDiffTwo } from '@utils/opt-diff';
import { objFilter } from '@utils/tools';

const { Text } = Typography;

const HistUserClient = () => {
  const queryForm = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();

  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = DEFAULT_HISTORY_PARAMS;
  const [filterParams, setFilterParams] = useState(defaultParams);

  const { get, response, loading: isLoading, abort } = useFetch();
  const [request, , loading] = useFetch();

  const columns = useMemo(() => [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, checkDate.split(',').length > 1 && {
    title: $t('app.system.tradeday'),
    dataIndex: 'tradeday',
    fixed: 'left',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    render: (text, record) => <Text>{`${text} [${record.userno}]`}</Text>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.clientno}]`}
    </>,
  }, {
    title: $t('app.options.clienttype'),
    dataIndex: 'clienttype',
    render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.accounttype'),
    dataIndex: 'accounttype',
    render: text => TableBadgeDot(text, accountTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.bizpbu'),
    dataIndex: 'bizpbu',
  }, {
    title: $t('app.options.salesnumb'),
    dataIndex: 'salesnumb',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.mem.userclient.status'),
    dataIndex: 'status',
    render: text => TableTag(text, clientStatusDict, tagColors),
  }, {
    title: $t('app.options.rightid'),
    dataIndex: 'rightids',
    render: text => rightFunc(text)
  }].filter(i => i), [intl, checkDate]);

  const rightFunc = text => {
    return text && text?.map((item, idx) => (
      <span key={idx}>
        {TableTag(item, clientRightDict, tagNormalColors, true)}
      </span>
    )).filter(i => i);
  }

  const [userClientGroup, setUserClientGroup] = useState([]);
  const [userOptions, setUserOptions] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);

  const userFocus = useCallback(() => {
    getHistUserClientList(request.get, checkDate, setUserClientGroup, true);
  }, [request, checkDate]);

  const createSearchForm = useMemo(() => searchForm(intl, userFocus, userOptions, loading, clientOptions), [intl, userFocus, userOptions, loading, clientOptions]);

  useMount(() => {
    // 默认先载入一次
    getHistUserClientList(request.get, checkDate, setUserClientGroup, true);
  });
  useUnmount(() => abort());

  useEffect(() => {
    getTableData(checkDate, filterParams);
  }, [filterParams, checkDate]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (userClientGroup.length > 0) {
      const users = optDiffTwo(userClientGroup);
      setUserOptions(users);

      const clients = optDiff(userClientGroup, 'clientid');
      setClientOptions(clients);
    } else {
      setUserOptions([]);
      setClientOptions([]);
    }
  }, [userClientGroup]);


  const getTableData = async (date, params) => {
    const resp = await get(SITE_URL.HISTORY_PATH('UserClient', {
      ...params,
      histat: date,
    }));

    if (response.ok) {
      if (resp.list?.length > 0) {
        const tableData = resp.list?.map((item, idx) => ({
          ...item,
          id: (params.page - 1) * params.limit + idx + 1,
          // rights: [item.right1, item.right2, item.right3, item.right4],
        }));

        setDataSource(tableData);
        setTotal(resp.count);
      } else {
        setDataSource([]);
        setTotal(0);
      }
    } else {
      histErrorResp(resp, navigate, location, setCheckDate);
    }
  };

  const onRefresh = () => {
    setFilterParams({...filterParams});
  }
  const onSearch = (values) => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    setFilterParams({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }
    // 重置股东代码
    // run('');

    setFilterParams(defaultParams);
  }

  const { run } = useDebounceFn(async (value) => {
    if (value) {
      const clientGroup = getClientByUser(userClientGroup, value);

      setClientOptions(clientGroup);
    } else {
      const clients = optDiff(userClientGroup, 'clientid');
      setClientOptions(clients);
    }

    // 交易编码赋值
    queryForm.current?.set({
      'clientid': undefined,
    });
  }, { wait: 500 });
  const formValueChange = changedValues => {
    if ('userid' in changedValues) {
      const value = changedValues.userid;
      run(value);
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        setDataSource([]);
      }

      setLimit(size);
    }

    setFilterParams({
      ...filterParams,
      page: newPage,
      limit: size,
    });
  }

  return (
    <PageContent
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        // initValues: {
        //   exchangeid: defaultExchange,
        // },
        onValuesChange: formValueChange,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const HistUserClientAlive = ({ keepName, ...props }) => (
  <KeepAlive name={keepName} key={keepName}>
    <HistUserClient {...props} />
  </KeepAlive>
);

export default HistUserClientAlive;
