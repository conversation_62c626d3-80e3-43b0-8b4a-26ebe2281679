import { dicts, formOptions } from '@titd/publics/utils';

const { yesNoDict, exchangeDict, gatewayTypeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, userOptions) => [{
  valueType: 'input',
  name: 'gatewayid',
  label: $t('app.options.db.gateway.id'),
}, {
  valueType: 'select',
  name: 'gatewaytype',
  label: $t('app.options.db.gateway.type'),
  fieldProps: {
    options: selectOptions(gatewayTypeDict),
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'select',
  name: 'ispreorder',
  label: $t('app.options.db.gateway.ispreorder'),
  fieldProps: {
    options: selectOptions(yesNoDict),
  }
}, {
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.userid'),
  fieldProps: {
    options: userOptions
  }
}];

export const addForm = ($t, isModify) => [{
  valueType: 'input',
  name: 'gatewayid',
  label: $t('app.options.db.gateway.id'),
  rules: [{ required: true }],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'radio',
  name: 'gatewaytype',
  label: $t('app.options.db.gateway.type'),
  fieldProps: {
    options: selectOptions(gatewayTypeDict),
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'radio',
  name: 'ispreorder',
  label: $t('app.options.db.gateway.ispreorder'),
  fieldProps: {
    options: selectOptions(yesNoDict),
  }
// }, {
//   valueType: 'select',
//   name: 'useridlist',
//   label: $t('app.options.userid'),
//   rules: [{ required: !noUser }],
//   fieldProps: {
//     mode: 'tags',
//     disabled: noUser,
//     options: userOptions,
//   }
}];
