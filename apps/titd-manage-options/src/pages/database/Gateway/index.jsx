import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, Space, Tag, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  formOptions,
  errorResp,
} from '@titd/publics/utils';

import UserTransfer from './Transfer';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, TableTag } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeDict, exchangeAllDict, gatewayTypeDict, yesNoDict, tagNormalColors, yesNoColor } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;
const { getUserList } = getList;
const { checkOptions } = formOptions;

const defaultGatewaytype = 1;

const GatewayBase = () => {
  const queryForm = useRef(null);
  const modalForm = useRef(null);
  const userRef = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();
  const defaultSearch = {
    ...defaultParams,
    ispreorder: -1,
  };

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    filters: checkOptions(exchangeDict),
    filteredValue: filteredInfo.exchangeid || null,
    onFilter: (value, record) => record.exchangeid === value,
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.gateway.id'),
    dataIndex: 'gatewayid',
    key: 'gatewayid',
    sorter: sortByName('gatewayid'),
    sortOrder: sortedInfo.columnKey === 'gatewayid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.db.gateway.type'),
    dataIndex: 'gatewaytype',
    render: text => gatewayTypeDict[text] || text,
  }, {
    title: $t('app.options.db.gateway.ispreorder'),
    dataIndex: 'ispreorder',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
  //   title: $t('app.options.userid'),
  //   dataIndex: 'useridlist',
  //   render: text => formatUser(text),
  // }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const formatUser = text => (<>
    {$t('app.options.userid') + '：'}
    <Space size={[0, 16]} wrap>
      {text?.map((item, idx) => {
        const userItem = userOptions?.find(i => i.value === item);
        return (<Tag key={idx}>{userItem?.label || item}</Tag>);
      })}
    </Space>
  </>);

  const [userOptions, setUserOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);
  const [oldRecord, setOldRecord] = useState({});
  const [noUser, setNoUser] = useState(false);

  const createSearchForm = useMemo(() => searchForm($t, userOptions), [$t, userOptions]);
  const createAddForm = useMemo(() => addForm($t, isModify), [$t, isModify]);

  useMount(() => {
    getUserList(Request.post, defaultParams, setUserOptions);
  });
  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryGateway), params);

    const { data: respData } = resp;
    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    setFilteredInfo({});
    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultSearch],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onSearch = values => {
    if (values.ispreorder === undefined) {
      delete values.ispreorder;
    }

    run({
      ...defaultSearch,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultSearch);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setNoUser(!defaultGatewaytype);
    modalForm.current.show();
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    setIsModify(true);
    setNoUser(record.gatewaytype !== 1);
    setOldRecord(record);
    modalForm.current.show(1, record);
  }
  const deleteFunc = async record => {
    const deleteData = {
      ...defaultParams,
      gatewayid: record.gatewayid,
    }

    fetchFunc(MESSAGE_TYPE.DelGateway, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      refresh();
    });
  }
  const toDeleteMore = records => {
    // console.log('Delete More');
    const ids = records.map(i => i.gatewayid);
    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record);
        });

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }
  const toDelete = record => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.gatewayid,
      onOk: () => {
        // console.log('ok', record);
        deleteFunc(record);

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }

  const formValueChange = changedValues => {
    if ('gatewaytype' in changedValues) {
      const value = changedValues.gatewaytype;

      // 有值则清空品种
      if (value === 1) {
        setNoUser(false);
      } else {
        setNoUser(true);
      }
    }
  }

  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    if (!noUser) {
      // 最后一步权限保存，统一处理
      const newUsers = userRef.current?.getValues();
      postData.useridlist = newUsers;
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdGateway
      : MESSAGE_TYPE.InsGateway, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      refresh();
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    // console.log('Various parameters', pagination, filters, sorter);
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.gateway', {
        defaultMsg: '网关管理',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: refresh,
        handleChange: tableChange,
        expandable: {
          expandedRowRender: record => formatUser(record.useridlist),
          rowExpandable: record => record.useridlist?.length > 0,
        }
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      initValues={{
        gatewaytype: defaultGatewaytype,
        ispreorder: 1,
      }}
      onValuesChange={formValueChange}
    >
      {!noUser && <UserTransfer
        ref={userRef}
        userOptions={userOptions}
        record={oldRecord}
      />}
    </ModalForm>
  </>);
}

// const GatewayAlive = ({ keepName, ...props }) => (
const Gateway = props => (
  // <KeepAlive name={keepName}>
  <GatewayBase {...props} />
  // </KeepAlive>
);

export { Gateway };
