import { useRef, useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Typography } from 'antd';

import {
  TiTable,
  TableFields,
} from '@titd/publics/components';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  dicts,
} from '@titd/publics/utils';

import RoleTransfer from './RoleTransfer';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot } = TableFields;
const { clientTypeDict, exchangeAllDict, tagNormalColors } = dicts;
const { TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;

const Client = (props, ref) => {
  const {
    clients,
    accountPatch,
    isModify,
  } = props;

  const roleTrans = useRef(null);
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [clientTmpData, setClientTmpData] = useState([]);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  useImperativeHandle(ref, () => ({
    reset: () => {
      setSelectKeys([]);
    },
    getValues: () => {
      // 最后一步权限保存，统一处理
      roleTrans.current?.setRole();

      return clientTmpData.map(tmpData => ({
        mainclientid: tmpData.clientid,
        clientid: tmpData.tmpclientid + (tmpData.accountpatch || ''),
        segmentid: tmpData.segmentid,
        defaultsvrid: tmpData.defaultsvrid,
        clienttype: tmpData.clienttype,
        bizpbu: tmpData.bizpbu,
        salesnumb: tmpData.salesnumb,
        exchangeid: tmpData.exchangeid,
        templateid: tmpData.templateid,
        quotetemplateid: tmpData.quotetemplateid,
        rightid: tmpData.rightid,
      }));
      // if (selectKeys.length === 0) {
      //   return null;
      // } else {
      //   const tmpData = clientTmpData.find(i => i.id === selectKeys[0]);

      //   return tmpData ? [{
      //     mainclientid: tmpData.clientid,
      //     clientid: tmpData.clientid + tmpData.accountpatch,
      //     segmentid: tmpData.segmentid,
      //     defaultsvrid: tmpData.defaultsvrid,
      //     clienttype: tmpData.clienttype,
      //     exchangeid: tmpData.exchangeid,
      //     templateid: tmpData.templateid,
      //     quotetemplateid: tmpData.quotetemplateid,
      //     rightid: tmpData.rightid,
      //   }] : null;
      // }
    }
  }));

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.segmentid'),
    dataIndex: 'segmentid',
  }, {
    title: $t('app.options.defaultsvrid'),
    dataIndex: 'defaultsvrid',
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'tmpclientid',
    render: (text, record) => isModify ? (
      <Text>{text}</Text>
    ) : (<>
      <Text>{text}</Text>
      <Text strong>{record.accountpatch}</Text>
      <Text type="secondary"> ({record.clientid})</Text>
    </>)
  }, {
    title: $t('app.options.clienttype'),
    dataIndex: 'clienttype',
    render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.bizpbu'),
    dataIndex: 'bizpbu',
  }, {
    title: $t('app.options.salesnumb'),
    dataIndex: 'salesnumb',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  // }, {
  //   title: $t('app.options.accountid'),
  //   dataIndex: 'accountid',
  // }, {
  //   title: $t('app.options.action'),
  //   dataIndex: 'actions',
  //   align: 'center',
  //   fixed: 'right',
  //   width: 80,
  //   onCell: () => {
  //     return { onClick: e => e.stopPropagation() }
  //   },
  //   render: (_, record) => (
  //     <Switch
  //       checkedChildren={<CheckOutlined />}
  //       unCheckedChildren={<CloseOutlined />}
  //       onChange={value => importClientChange(value, record)}
  //       checked={!record.isDisabled}
  //     />
  //   ),
  }];

  useEffect(() => {
    if (clients?.length > 0) {
      getClientTable(clients);
    }
  }, [clients]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (clientTmpData.length > 0 && accountPatch?.length === 2) {
      const tmpData = clientTmpData.map(item => ({
        ...item,
        accountpatch: accountPatch,
      }));

      setClientTmpData(tmpData);
    }
  }, [accountPatch]); // eslint-disable-line react-hooks/exhaustive-deps

  const getClientTable = clients => {
    const tableData = clients.map((item, idx) => ({
      ...item,
      id: idx + 1,
      tmpclientid: isModify ? item.clientid : item.clientid.slice(2),
    }));

    setClientTmpData(tableData);

    // 默认选择第一个可选项
    if (tableData.length > 0) {
      setSelectKeys([tableData[0].id]);
    } else {
      setSelectKeys([]);
    }
  }

  // const importClientChange = (value, record) => {
  //   const idx = dataFindIndex(clientTmpData, record, clientNames);

  //   clientTmpData[idx] = {
  //     ...clientTmpData[idx],
  //     isDisabled: !value,
  //     type: 1,
  //   }

  //   setClientTmpData([...clientTmpData]);
  //   if (value) {
  //     setSelectKeys([clientTmpData[idx].id]);
  //   } else {
  //     if (selectKeys[0] === clientTmpData[idx].id) {
  //       setSelectKeys([]);
  //     }
  //   }
  // }

  return (<>
    <TiTable
      columns={columns}
      dataSource={clientTmpData}
      // isLoading={isLoading}
      hasToolBar={false}
      // checkable={false}
      rowClickable
      footer={() => $t('app.options.db.userinfo.clientid.tip') }
      selectionType="radio"
      selectKeys={selectKeys}
      setSelectKeys={(keys) => {
        setSelectKeys(keys);
        // roleTrans.current?.submit();
        roleTrans.current?.setRole();
      }}
    />

    {/* 权限管理 */}
    {selectKeys.length > 0 && (
      <RoleTransfer
        ref={roleTrans}
        clientData={clientTmpData[selectKeys[0] - 1]}
        isModify={isModify}
        defaultParams={defaultParams}
        setRoleFunc={roleData => {
          clientTmpData[selectKeys[0] - 1] = roleData;
          setClientTmpData([...clientTmpData]);
        }}
      />
    )}
  </>);
}

export default forwardRef(Client);
