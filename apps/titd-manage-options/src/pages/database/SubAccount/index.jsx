import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Card, Space, Typography, Tag, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  TiTable,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
  HeadTitle,
} from '@titd/publics/components';
import { addForm, resetPwdForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

import Client from './Client';

const { Text, Link } = Typography;
const { IndexColumn, TableTitle, NegaNumber, TableBadgeDot, TableTag } = TableFields;
const { ModalForm, Confirm } = forms;
const { insertBtn } = btns;
const { insertLink, updateLink, deleteLink, resetPwdLink } = links;
const { userStatusDict, clientTypeDict, exchangeAllDict, clientRightDict, yesNoDict, tagNormalColors, tagColors, yesNoColor } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { formatDateFull } = dateFormat;

const SubAccountBase = () => {
  const modalForm = useRef(null);
  const modalClient = useRef(null);
  const resetForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [checkUser, setCheckUser] = useState([]);
  const [expandRows, setExpandRows] = useState([]);

  const [childDataSource, setChildDataSource] = useState([]);
  const [accountPatch, setAccountPatch] = useState();

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'mainaccountid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.future.accountname'),
    dataIndex: 'accountname',
  }, {
  //   title: $t('app.options.accounttype'),
  //   dataIndex: 'accounttype',
  //   render: text => TableBadgeDot(text, accountTypeDict, tagNormalColors, true),
  // }, {
    title: TableTitle(
      $t('app.future.prebalance'),
      $t('app.future.prebalance.tip'),
    ),
    dataIndex: 'prebalance',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ID,
    render: (_, record) => (
      <ActionLinks links={[
        insertLink(toInsert, record),
      ]} />
    )
  }];

  const childColumns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.future.accountid'),
    dataIndex: 'userid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.db.userinfo.name'),
    dataIndex: 'name',
  }, {
    title: $t('app.options.db.userinfo.status'),
    dataIndex: 'status',
    // filters: checkOptions(userStatusDict),
    // filteredValue: filteredInfo.status || null,
    // onFilter: (value, record) => record.status === value,
    render: text => TableTag(text, userStatusDict, tagColors),
  }, {
    title: $t('app.options.db.userinfo.logincount'),
    dataIndex: 'logincount',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.db.userinfo.tradeflow'),
      $t('app.options.db.userinfo.tradeflow.long')
    ),
    dataIndex: 'tradeflow',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.db.userinfo.loginsuccess'),
      $t('app.options.db.userinfo.loginsuccess.long')
    ),
    dataIndex: 'loginsuccess',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.db.userinfo.loginfailed'),
      $t('app.options.db.userinfo.loginfailed.long')
    ),
    dataIndex: 'loginfailed',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.db.userinfo.minlocalid'),
    dataIndex: 'minlocalid',
  }, {
    title: $t('app.options.db.userinfo.maxlocalid'),
    dataIndex: 'maxlocalid',
  }, {
    title: $t('app.options.db.userinfo.lastloginip'),
    dataIndex: 'lastloginip',
  }, {
    title: $t('app.options.db.userinfo.lastlogintime'),
    dataIndex: 'lastlogintime',
    render: text => formatDateFull(text),
  }, {
    title: TableTitle(
      $t('app.future.setaccount'),
      $t('app.future.setaccount.long')
    ),
    dataIndex: 'setaccout',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.future.checkaccount'),
      $t('app.future.checkaccount.long')
    ),
    dataIndex: 'svrid',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }, {
    title: $t('app.menu.db.appid'),
    dataIndex: 'appid',
    render: text => <Space size={[0, 16]} wrap>
      {text?.map((item, idx) => <Tag key={idx}>{item}</Tag>)}
    </Space>,
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.USER,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
        resetPwdLink(toResetPwd, record),
      ]} />
    )
  }];

  const [isModify, setIsModify] = useState(false);

  const createAddForm = useMemo(() => addForm($t, isModify), [$t, isModify]);
  const createResetPwdForm = useMemo(() => resetPwdForm($t), [$t]);

  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryMainUserInfo), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));

      // 重新载入，选择第一项
      const defaultKey = tableData[0].id;
      setSelectKeys([defaultKey]);
      getChildUserInfo(defaultKey, tableData);

      return tableData;
    } else {
      setSelectKeys([]);
      setChildDataSource([]);

      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getUserData = async (userId, mainUserId) => {
    const params = {
      ...defaultParams,
      userid: userId,
    };

    if (mainUserId) {
      params.mainuserid = mainUserId;
    }

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserInfoDetail), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }
    return null;
  }
  const getChildUserInfo = async (key, mainData) => {
    const record = (mainData || dataSource).find(i => i.id === key);

    if (record) {
      setCheckUser({
        mainuserid: record.mainaccountid,
        userid: record.mainaccountid,
        id: key,
      });

      fetchFunc(MESSAGE_TYPE.QryUserInfoDetail, {
        ...defaultParams,
        mainuserid: record.mainaccountid,
      }, resp => {
        const tableData = resp?.map((item, idx) => ({
          ...item,
          id: idx + 1,
          clientitem: item.clientitem?.map((i, num) => ({
            ...i,
            id: num + 1,
            // 隐藏检查自成交
            rightid: i.rightid?.filter(a => a !== 1),
          })) || []
        }));
        setChildDataSource(tableData);

        // setRightData({});
        setExpandRows([]);
      });
    }
  }

  const toInsert = async record => {
    // console.log('Insert', record);
    setIsModify(false);
    const newRecord = await getUserData(record.mainaccountid || record.mainuserid);

    if (newRecord) {
      setCheckUser({
        mainuserid: newRecord.userid,
        userid: newRecord.userid,
        clients: newRecord.clientitem.map(item => ({
          ...item,
          oldtemplateid: item.templateid,
          oldquotetemplateid: item.quotetemplateid,
          oldrightid: item.rightid,
        })),
        id: record.id,
      });

      modalForm.current.show(0, {
        ...newRecord,
        svrid: 1,
      });

      // 更新本条数据
      // const newData = updateRowData(dataSource, {
      //   ...newRecord,
      //   id: record.id,
      // });
      // setDataSource(newData);
    } else {
      message.error($t('app.general.deleted'));
      onChildRefresh();
    }
  }
  const toInsertAll = () => {
    if (checkUser) {
      toInsert(checkUser);
    } else {
      message.error('请选择一个用户');
    }
  }

  const onChildRefresh = () => {
    // 获取子账户
    getChildUserInfo(checkUser.id);
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    setIsModify(true);
    setAccountPatch('');

    const newRecord = await getUserData(record.userid, checkUser.mainuserid);

    if (newRecord) {
      setCheckUser({
        mainuserid: checkUser.mainuserid,
        userid: newRecord.userid,
        clients: newRecord.clientitem,
        id: checkUser.id,
      });

      modalForm.current.show(1, newRecord);
    } else {
      message.error($t('app.general.deleted'));
    }
  }

  const clientColumns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.segmentid'),
    dataIndex: 'segmentid',
  }, {
    title: $t('app.options.defaultsvrid'),
    dataIndex: 'defaultsvrid',
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clienttype'),
    dataIndex: 'clienttype',
    render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.bizpbu'),
    dataIndex: 'bizpbu',
  }, {
    title: $t('app.options.salesnumb'),
    dataIndex: 'salesnumb',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.templateid'),
    dataIndex: 'templateid',
    render: text => templateFunc(text),
  }, {
    title: $t('app.future.quoteid'),
    dataIndex: 'quotetemplateid',
    render: text => templateFunc(text),
  }, {
    title: $t('app.options.rightid'),
    dataIndex: 'rightid',
    render: text => rightFunc(text)
  }];

  const templateFunc = text => {
    return text?.length > 0 && (<Space>
      {text.map((item, idx) => (
        <Link underline key={idx} onClick={() => navigate('/db/tradetpl', { state: { datas: item } })}>{item}</Link>
      ))}
    </Space>);
  }
  const rightFunc = text => {
    return text?.length > 0 && text?.map((item, idx) => (
      <span key={idx}>
        {TableTag(item, clientRightDict, tagNormalColors, true)}
      </span>
    ));
  }

  // 展开表格
  const expandable = {
    expandedRowRender: (record) => {
      return <TiTable
        columns={clientColumns}
        dataSource={record.clientitem}
        hasToolBar={false}
        checkable={false}
        bordered={false}
      />
    },
    // onExpand: (expanded, record) => {
    //   if (expanded) {
    //     getRight(record.userid, record.mainuserid);
    //   }
    // },
    expandedRowKeys: expandRows,
    onExpandedRowsChange: (expandedRows) => {
      setExpandRows(expandedRows);
    }
  }

  const addSubmit = async (form, type) => {
    // 获取交易编码和资金分配
    const clientValues = modalClient.current?.getValues();

    if (clientValues) {
      let postData = {
        ...defaultParams,
        ...form,
        mainuserid: form.userid,
        clientitem: clientValues,
      }

      if (type) {
        fetchFunc(MESSAGE_TYPE.UserInfoDetail, postData, () => {
          message.success(CURD.Update + CURD.StatusOkMsg);
          modalForm.current.close();
          onChildRefresh();
        });
      } else {
        postData.userid = form.userid + form.accountid;
        delete postData.accountid;

        fetchFunc(MESSAGE_TYPE.InsSubClient, postData, () => {
          message.success(CURD.Insert + CURD.StatusOkMsg);
          modalForm.current.close();
          onChildRefresh();
        });
      }
    } else {
      message.error('请选一个交易编码')
    }
  }

  const formValueChange = changedValues => {
    if ('accountid' in changedValues) {
      const value = changedValues.accountid;

      if (value) {
        setAccountPatch(value);
      }
    }
  }

  const deleteFunc = async record => {
    const deleteData = {
      ...defaultParams,
      userid: record.userid,
    }

    fetchFunc(MESSAGE_TYPE.DelSubClient, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onChildRefresh();
    });
  }
  const toDelete = (record) => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.userid,
      onOk: () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }
  const toResetPwd = (record) => {
    // console.log('ResetPwd', record);
    resetForm.current.show(2, record);
  }

  const resetPwdSubmit = form => {
    const postData = {
      ...defaultParams,
      userid: form.userid,
      newpassword: form.newpassword,
    }

    fetchFunc(MESSAGE_TYPE.UpdPassword, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onChildRefresh();
    });
  }

  return (<>
    <HeadTitle title={$t('app.menu.db.sub', {
      defaultMsg: '账号管理',
    })} />

    <Card>
      <TiTable
        columns={columns}
        dataSource={dataSource}
        isLoading={isLoading && dataSource.length === 0}
        pageSize={5}
        hasToolBar={false}
        rowClickable={true}
        selectionType={'radio'}
        selectKeys={selectKeys}
        setSelectKeys={(keys) => {
          setSelectKeys(keys);

          // 获取子账户
          // console.log(keys);
          if (keys.length > 0) {
            getChildUserInfo(keys[0]);
          }
        }}
      />
    </Card>

    <Card className="ti-table-content">
      <TiTable
        columns={childColumns}
        dataSource={childDataSource}
        isLoading={isLoading}
        actionBtns={[
          insertBtn(toInsertAll),
        ]}
        checkable={false}
        onRefresh={onChildRefresh}
        pageSize={10}
        expandable={expandable}
      />
    </Card>

    {/* 新增子用户 */}
    <ModalForm
      ref={modalForm}
      formData={createAddForm}
      formGroup
      spanDefault={3}
      width={'80%'}
      onOk={addSubmit}
      // onCancel={formCancel}
      lateClose={true}
      onValuesChange={formValueChange}
    >
      <Client
        ref={modalClient}
        currentForm={modalForm}
        clients={checkUser.clients}
        accountPatch={accountPatch}
        isModify={isModify}
        // segmentList={segmentList}
        // serverMap={serverMap}
      />
    </ModalForm>

    {/* 重置密码 */}
    <ModalForm
      ref={resetForm}
      onOk={resetPwdSubmit}
      formData={createResetPwdForm}
    />
  </>);
}

const SubAccount = props => (
  // <KeepAlive name={keepName}>
    <SubAccountBase {...props} />
  // </KeepAlive>
);

export { SubAccount };
