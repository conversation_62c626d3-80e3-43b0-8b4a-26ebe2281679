import { TableFields } from '@titd/publics/components';
import { dicts, formOptions, iconDom } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { userStatusDict, yesNoDict } = dicts;
const { selectOptions } = formOptions;
const { keyIcon, copyIcon, hideIcon, showIcon } = iconDom;

export const searchForm = ($t, userOptions) => [{
  valueType: 'autocomplete',
  name: 'mainaccountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: userOptions
  }
}];

export const addForm = ($t, isModify) => [isModify ? {
  valueType: 'input',
  name: 'userid',
  label: $t('app.future.accountid'),
  fieldProps: {
    readOnly: true,
  }
} : {
  valueType: 'group',
  label: $t('app.future.accountid'),
  otherProps: {
    required: true,
  },
  fieldProps: {
    children: [{
      valueType: 'input',
      name: 'userid',
      noStyle: true,
      fieldProps: {
        readOnly: true,
        style: { width: '65%' },
      }
    }, {
      valueType: 'input',
      name: 'accountid',
      label: $t('app.future.accountid'),
      noStyle: true,
      rules: [
        { required: true },
        { len: 2 },
      ],
      fieldProps: {
        allowClear: false,
        style: { width: '35%' },
      }
    }]
  }
}, {
  valueType: 'password',
  name: 'password',
  label: $t('app.auth.password'),
  rules: [{ required: !isModify }],
  fieldProps: {
    disabled: isModify,
  }
}, {
  valueType: 'input',
  name: 'name',
  label: $t('app.options.db.userinfo.name'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.options.db.userinfo.status'),
  fieldProps: {
    allowClear: false,
    options: selectOptions(userStatusDict),
  }
}, {
  valueType: 'number',
  name: 'tradeflow',
  label: $t('app.options.db.userinfo.tradeflow'),
}, {
  valueType: 'number',
  name: 'logincount',
  label: $t('app.options.db.userinfo.logincount'),
}, {
  valueType: 'number',
  name: 'loginsuccess',
  label: TableTitle(
    $t('app.options.db.userinfo.loginsuccess'),
    $t('app.options.db.userinfo.loginsuccess.long')
  ),
}, {
  valueType: 'number',
  name: 'loginfailed',
  label: TableTitle(
    $t('app.options.db.userinfo.loginfailed'),
    $t('app.options.db.userinfo.loginfailed.long')
  ),
}, {
  valueType: 'number',
  name: 'setaccout',
  label: TableTitle(
    $t('app.future.setaccount'),
    $t('app.future.setaccount.long')
  ),
  fieldProps: {
    disabled: isModify,
  }
}, {
  valueType: 'number',
  name: 'minlocalid',
  label: $t('app.options.db.userinfo.minlocalid')
}, {
  valueType: 'number',
  name: 'maxlocalid',
  label: $t('app.options.db.userinfo.maxlocalid')
}, {
  valueType: 'select',
  name: 'svrid',
  label: TableTitle(
    $t('app.future.checkaccount'),
    $t('app.future.checkaccount.long')
  ),
  fieldProps: {
    allowClear: false,
    options: selectOptions(yesNoDict),
  }
}, {
  valueType: 'hidden',
  name: 'appid',
  label: $t('app.menu.db.appid'),
  noStyle: true,
}];

export const childAddForm = $t => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.future.accountid'),
}, {
  valueType: 'text',
  name: 'accountname',
  label: $t('app.future.accountname')
}, {
  // valueType: 'select',
  // name: 'accounttype',
  // label: $t('app.options.accounttype'),
  // fieldProps: {
  //   disabled: true,
  //   options: selectOptions(accountTypeDict),
  // }
  valueType: 'hidden',
  name: 'accounttype',
  noStyle: true,
}, {
  valueType: 'number',
  name: 'prebalance',
  label: $t('app.future.prebalance'),
}, {
  valueType: 'number',
  name: 'precredit',
  label: $t('app.future.precredit')
}];

export const resetPwdForm = $t => [{
  valueType: 'text',
  name: 'userid',
  label: $t('app.future.accountid'),
}, {
  valueType: 'password',
  name: 'newpassword',
  label: $t('app.options.newpassword'),
  rules: [{ required: true }],
  fieldProps: {
    prefix: keyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}, {
  valueType: 'password',
  name: 'repassword',
  label: $t('app.options.repassword'),
  rules: [
    { required: true },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newpassword') === value) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error(
            $t('app.options.repassword.error')
          ));
        }
      }
    }),
  ],
  fieldProps: {
    prefix: copyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}];
