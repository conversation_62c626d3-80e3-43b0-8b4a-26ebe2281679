import { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Row, Col, Transfer, Divider, Typography } from 'antd';

import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
} from '@titd/publics/utils';

const { Text } = Typography;
const { clientRightDict } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;

const TransferItem = ({
  label,
  titles,
  dataSource,
  targetKeys,
  change,
  selectedKeys,
  selectChange,
}) => {
  return (
    <Col xs={24} lg={12} xxl={8}>
      <Divider orientation="left" plain>{label}</Divider>
      <Transfer
        oneWay
        titles={titles}
        dataSource={dataSource}
        targetKeys={targetKeys}
        selectedKeys={selectedKeys}
        onChange={change}
        onSelectChange={selectChange}
        render={item => item.title}
      />
    </Col>
  );
}

const RoleTransfer = (props, ref) => {
  const {
    clientData,
    defaultParams,
    setRoleFunc,
    isModify,
  } = props;

  const { FormattedMsg } = useFormattedMessage();

  // 权限模版
  const [tplData, setTplData] = useState([]);
  const [checkTpl, setCheckTpl] = useState([]);
  const [selectTpl, setSelectTpl] = useState([]);
  // 报价模版
  const [quoteData, setQuoteData] = useState([]);
  const [checkQuote, setCheckQuote] = useState([]);
  const [selectQuote, setSelectQuote] = useState([]);
  // 交易权限
  const [rightData, setRightData] = useState([]);
  const [checkRight, setCheckRight] = useState([]);
  const [selectRight, setSelectRight] = useState([]);

  const titles = [
    <Text type="secondary" key="0">{FormattedMsg('app.options.db.userinfo.right.none')}</Text>,
    <Text type="secondary" key="1">{FormattedMsg('app.options.db.userinfo.right.have')}</Text>,
  ];

  useImperativeHandle(ref, () => ({
    setRole: () => {
      setRoleFunc({
        ...clientData,
        templateid: checkTpl,
        quotetemplateid: checkQuote,
        rightid: checkRight,
      });
    },
  }));

  const getTpl = async clientid => {
    const clients = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QrySubClient), {
      ...defaultParams,
      clientid: clientid,
    });

    if (clients) {
      const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeRight), {
        ...defaultParams,
        clientid: clients.mainclientid,
      });

      if (resp?.length > 0) {
        const respData = resp[0];
        respData.templateid && setTpl(respData.templateid, setTplData);
        respData.quotetemplateid && setTpl(respData.quotetemplateid, setQuoteData);
        // respData.rightid && setRight(respData.rightid);
      }
    }
  };
  const setTpl = (tpl, func) => {
    const data = tpl.map(item => ({
      key: item,
      title: item,
    }));

    func && func(data);
  }
  const setRight = () => {
    const rightList = clientRightDict.map((item, idx) => ({
      key: idx,
      title: item,
      // disabled: rights?.indexOf(idx) > -1,
    })).splice(2);
    // console.log(rights, rightList);

    setRightData(rightList);
  }
  // const getHasRights = async (client) => {
  //   // console.log(client);
  //   const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeRight), {
  //     ...defaultParams,
  //     clientid: client.clientid,
  //     clienttype: client.clienttype,
  //     exchangeid: client.exchangeid,
  //   });

  //   if (resp?.length > 0) {
  //     const respData = resp[0];

  //     setCheckTpl(respData.templateid);
  //     setCheckRight(respData.rightid);

  //     isModify = true;
  //   }
  // }

  const clearReset = () => {
    setTplData([]);
    setCheckTpl([]);
    setQuoteData([]);
    setCheckRight([]);
    setCheckQuote([]);
    setSelectTpl([]);
    setSelectQuote([]);
    setSelectRight([]);
  }

  useEffect(() => {
    if (clientData) {
      if (isModify) {
        // 获取模版列表
        getTpl(clientData.clientid);

        setCheckTpl(clientData.templateid);
        setCheckQuote(clientData.quotetemplateid);
        // setCheckRight(clientData.rightid);
      } else {
        // 获取拥有的权限
        clientData.oldtemplateid && setTpl(clientData.oldtemplateid, setTplData);
        setCheckTpl(clientData.templateid);

        clientData.oldquotetemplateid && setTpl(clientData.oldquotetemplateid, setQuoteData);
        setCheckQuote(clientData.quotetemplateid);

        // 获取权限列表
        // clientData.oldrightid && setRight(clientData.oldrightid);
        // setCheckRight(clientData.rightid);
      }

      // 获取权限列表
      setRight();
      setCheckRight(clientData.rightid);
      // if (clientData.role) {
      //   const clientRole = clientData.role;
      //   setCheckTpl(clientRole.templateid);
      //   setCheckRight(clientRole.rightid);
      // } else {
      //   getHasRights(clientData);
      // }
    }

    return () => {
      // 每次切换清除权限选择列表
      clearReset();
    }
  }, [clientData]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <Row gutter={{ sm: 16, md: 24, lg: 32 }}>
      <TransferItem
        label={FormattedMsg('app.options.templateid')}
        titles={titles}
        dataSource={tplData}
        targetKeys={checkTpl}
        selectedKeys={selectTpl}
        change={(nextTargetKeys) => {
          // console.log('nextTargetKeys:', nextTargetKeys);
          setCheckTpl(nextTargetKeys);
        }}
        selectChange={(sourceSelectedKeys, targetSelectedKeys) => {
          // console.log(sourceSelectedKeys, targetSelectedKeys);
          setSelectTpl([...sourceSelectedKeys, ...targetSelectedKeys]);
        }}
      />
      <TransferItem
        label={FormattedMsg('app.future.quoteid')}
        titles={titles}
        dataSource={quoteData}
        targetKeys={checkQuote}
        selectedKeys={selectQuote}
        change={(nextTargetKeys) => {
          // console.log('nextTargetKeys:', nextTargetKeys);
          setCheckQuote(nextTargetKeys);
        }}
        selectChange={(sourceSelectedKeys, targetSelectedKeys) => {
          // console.log(sourceSelectedKeys, targetSelectedKeys);
          setSelectQuote([...sourceSelectedKeys, ...targetSelectedKeys]);
        }}
      />
      <TransferItem
        label={FormattedMsg('app.options.rightid')}
        titles={titles}
        dataSource={rightData}
        targetKeys={checkRight}
        selectedKeys={selectRight}
        change={(nextTargetKeys) => {
          // console.log('nextTargetKeys:', nextTargetKeys);
          setCheckRight(nextTargetKeys);
        }}
        selectChange={(sourceSelectedKeys, targetSelectedKeys) => {
          // console.log(sourceSelectedKeys, targetSelectedKeys);
          setSelectRight([...sourceSelectedKeys, ...targetSelectedKeys]);
        }}
      />
    </Row>
  );
}

export default forwardRef(RoleTransfer);
