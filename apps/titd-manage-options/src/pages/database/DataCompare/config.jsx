import { Upload, Row, Col, Space, Tooltip, Typography, message } from 'antd';
import {
  PlusOutlined,
  MinusOutlined,
  QuestionOutlined,
  CheckOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';

import { consts } from '@titd/publics/utils';

const { Text, Link } = Typography;
const { SITE_URL } = consts;

export const uploadProps = $t => ({
  maxCount: 1,
  beforeUpload: file => {
    const isCSV = file.name.match(/.csv/);
    if (!isCSV) {
      message.error(file.name + $t('app.options.db.datacompare.error'));
    }
    return !!isCSV || Upload.LIST_IGNORE;
  },
  // onChange: (info) => {
  //   console.log(info.file.status);
  // },
  progress: {
    strokeColor: {
      '0%': '#108ee9',
      '100%': '#87d068',
    },
    strokeWidth: 3,
    format: percent => `${parseFloat(percent.toFixed(2))}%`,
  },
});

export const actionUrl = type => SITE_URL.BASE + '/' + SITE_URL.EXTEND(type);

export const tableTitle = (name, $t) => (
  <Text strong>
    {$t(name)}
    {$t('app.options.db.datacompare.check') + $t('app.options.db.datacompare.title')}
  </Text>
);

const tipMsg = (num, title, $t) => (
  <Tooltip title={$t('app.options.db.datacompare.' + title + '.tip')}>
    <InfoCircleOutlined />
    <Text strong style={{ marginLeft: '5px' }}>{num || 0}</Text>
  </Tooltip>
);

// type 0:资金, 1:持仓
export const showMsg = (msg, type, $t) => (
  <Row justify="space-between">
    <Col>
      {$t('app.options.db.datacompare.uploadnum', {
        titdnum: <Text strong underline>{msg.up || 0}</Text>,
        ctpnum: <Text strong underline>{msg.td || 0}</Text>,
      })}
    </Col>
    <Col>
      <Space>
        {$t('app.options.db.datacompare.overview', {
          total: <Link strong>{msg.td + msg.up || 0}</Link>,
          same: <Text type="success" strong>{msg.success || 0}</Text>,
          diff: <Text type="danger" strong>{msg.failed || 0}</Text>
        })}
        <span>
          {$t('app.options.db.datacompare.onlynum', {
            titdonly: tipMsg(msg.titdonly, msg.titdonlytip, $t),
          })}
          {$t('app.options.db.datacompare.onlynum.' + (type ? 'position' : 'account'), {
            ctponly: tipMsg(msg.ctponly, msg.ctponlytip, $t),
            openonly: tipMsg(msg.openonly, msg.openonlytip, $t),
          })}
        </span>
      </Space>
    </Col>
  </Row>
);

export const compareIconDict = [
  <PlusOutlined key="0" style={{ color: '#d46b08' }} />,
  <MinusOutlined key="1" style={{ color: '#ff4d4f' }} />,
  <QuestionOutlined key="2" style={{ color: '#1890ff' }} />,
  <CheckOutlined key="3" style={{ color: '#52c41a' }} />,
];

export const compareBgColor = [
  'ti-bg-orange',
  'ti-bg-red',
  'ti-bg-blue',
  'ti-bg-green',
];
