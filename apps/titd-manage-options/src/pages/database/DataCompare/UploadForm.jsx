import { Form, Row, Col, Button } from 'antd';
import { useThrottleFn } from 'ahooks';

import { forms } from '@titd/publics/components';
import { useFormattedMessage } from '@titd/publics/utils';

const { TiFormItem } = forms;

const UploadForm = props => {
  const {
    formData,
    formLayout = 'horizontal',
    isLoading,
    onFinish,
  } = props;

  const [form] = Form.useForm();
  const { $t } = useFormattedMessage();

  const { run } = useThrottleFn((func) => {
    form.validateFields()
      .then(values => {
        func && func(values);
      }).catch(info => {
        console.log('Validate Failed: ' + info);
      });
  }, { wait: 1000 });

  return (
    <Form
      form={form}
      name="upload_form"
      autoComplete="off"
      layout={formLayout}
      onFinish={() => run(onFinish)}
    >
      <Row gutter={16}>
        <Col flex="auto">
          {formData && formData.map((item, idx) => (
            <TiFormItem key={idx} { ...item } />
          ))}
        </Col>
        <Col>
          <Form.Item
            label=' '
            colon={false}
          >
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
            >{$t('app.options.db.datacompare.check')}</Button>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
}

export default UploadForm;
