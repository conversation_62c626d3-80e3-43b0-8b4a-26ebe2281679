import { useState } from 'react';
import { Typography, Divider, App } from 'antd';
import { useRequest, useUnmount } from 'ahooks';
import { MacScrollbar } from 'mac-scrollbar';

import { TableSimple } from '@titd/publics/components';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  currency,
} from '@titd/publics/utils';

import UploadForm from './UploadForm';
import { accountForm } from './data';
import { tableTitle, uploadProps, actionUrl, showMsg, compareIconDict, compareBgColor } from './config';

const { Text } = Typography;
const { MESSAGE_TYPE, SITE_URL } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { formatDot } = currency;

const AccountCompare = ({ styles }) => {
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [sameSataSource, setSameSataSource] = useState([]);
  const [msgData, setMsgData] = useState({
    titdonlytip: 'account.titdonly',
    ctponlytip: 'account.ctponly',
    openonlytip: 'account.openonly',
  });

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const columns = [{
    dataIndex: 'diff',
    width: 36,
    align: 'center',
    render: text => compareIconDict[text],
  }, {
    title: $t('app.options.db.datacompare.accountid'),
    dataIndex: 'accountid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.db.datacompare.accountname'),
    dataIndex: 'accountname',
  }, {
    title: $t('app.options.frozenfunds'),
    dataIndex: 'prebalance',
    align: 'right',
    render: text => diffText(text)
  }, {
    title: $t('app.options.db.datacompare.distribfund'),
    dataIndex: 'distribfund',
    align: 'right',
    render: text => diffText(text)
  }, {
    title: $t('app.options.mem.account.availusedfund'),
    dataIndex: 'availusedfund',
    align: 'right',
    render: text => diffText(text)
  }, {
    title: $t('app.options.db.datacompare.futmargin'),
    dataIndex: 'futmargin',
    align: 'right',
    render: text => diffText(text)
  }, {
  //   title: '手续费',
  //   dataIndex: 'commi',
  //   align: 'right',
  //   render: text => formatDot(text)
  // }, {
    title: $t('app.options.db.datacompare.datasrc'),
    dataIndex: 'datasrc',
    width: 60,
    render: text => text && <Text code>{text}</Text>,
  }];

  const diffText = (text) => (
    <Text>{text && formatDot(text !== 0 ? text.toFixed(2) : text)}</Text>
  );

  const AccountUpload = {
    ...uploadProps($t),
    name: 'accountFile',
    action: actionUrl(MESSAGE_TYPE.UploadAccount),
    data: defaultParams,
  };

  const createUploadForm = accountForm($t, AccountUpload);

  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.CheckAccount), params);

    const { data: respData } = resp;
    // 不同
    const tableData = [];
    const compareList = respData.item;
    if (compareList?.length > 0) {
      compareList.forEach((item, idx) => {
        const filterArr = compareList.filter(i => i.accountid === item.accountid);
        let addData;

        if (filterArr.length === 2) {
          item.diff = 2;

          const filterIndex = filterArr.findIndex(i => i.datasrc === item.datasrc);

          if (filterIndex === 1) {
            addData = {
              id: -idx,
              border: true,
              prebalance: filterArr[0].prebalance - item.prebalance,
              distribfund: filterArr[0].distribfund - item.distribfund,
              availusedfund: filterArr[0].availusedfund - item.availusedfund,
              futmargin: filterArr[0].futmargin - item.futmargin,
            }
          }
        } else {
          if (item.datasrc === 'CTP') {
            item.diff = 1;
          } else {
            item.diff = 0;
          }
          item.border = true;
        }

        tableData.push({
          ...item,
          id: idx + 1,
        });

        // 添加计算行
        if (addData) {
          tableData.push(addData);
        }
      });
    }

    // 相同
    if (respData.sucitem?.length > 0) {
      const sameTableData = respData.sucitem.map((item, idx) => ({
        ...item,
        id: idx,
        diff: 3,
        border: idx % 2 === 0 ? false : true
      }));

      setSameSataSource(sameTableData);
    } else {
      setSameSataSource([]);
    }

    setMsgData({
      ...msgData,
      td: respData.tdnum,
      up: respData.upnum,
      success: respData.successnum,
      failed: respData.failednum,
      titdonly: respData.onlytdnum,
      ctponly: respData.egnorenum,
      openonly: respData.onlytdopennum,
    });

    return tableData;
  }

  const {
    data: dataSource = [],
    loading,
    run,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: { data: errData } } = err;
      message.error(errData.errmessage);
    },
  });

  const onSearch = () => {
    run(defaultParams);
  }

  return (<>
    <UploadForm
      formData={createUploadForm}
      onFinish={onSearch}
      isLoading={loading}
    />

    {/* 资金 */}
    <TableSimple
      className={styles.doubleTable}
      columns={columns}
      dataSource={dataSource}
      isLoading={loading}
      title={() => tableTitle('app.options.db.datacompare.account', $t)}
      footer={() => showMsg(msgData, 0, $t)}
      rowClassName={record => [
        compareBgColor[record.diff],
        record.border ? 'ti-tr-border' : '',
      ].join(' ')}
    />

    {sameSataSource.length > 0 && (
      <div className="ti-same-table">
        <Divider
          plain
          orientation="left"
        >{$t('app.options.db.datacompare.common')}
        </Divider>

        <MacScrollbar style={{ maxHeight: '600px' }}>
          <TableSimple
            className={styles.doubleTable}
            columns={columns}
            dataSource={sameSataSource}
            isLoading={loading}
            rowClassName={record => record.border ? 'ti-tr-border' : ''}
          />
        </MacScrollbar>
      </div>
    )}
  </>);
}

export default AccountCompare;
