import { Tabs, Alert } from 'antd';
// import KeepAlive from 'react-activation';

import { HeadTitle } from '@titd/publics/components';
import { useFormattedMessage } from '@titd/publics/utils';

import AccountCompare from './Account';
import PositionCompare from './Position';

import { useStyles } from './style';

const tabGroups = ($t, styles) => [{
  label: <span>{$t('app.options.db.datacompare.account') + $t('app.options.db.datacompare.check')}</span>,
  key: '1',
  children: (<AccountCompare styles={styles} />)
}, {
  label: <span>{$t('app.options.db.datacompare.position') + $t('app.options.db.datacompare.check')}</span>,
  key: '2',
  children: (<PositionCompare styles={styles} />)
}];

const DataCompareBase = () => {
  const { $t } = useFormattedMessage();
  const { styles } = useStyles();

  return (<>
    <HeadTitle title={$t('app.menu.db.datacompare', {
      defaultMsg: '数据校验',
    })} />

    <Alert
      showIcon
      closable
      type="warning"
      message={$t('app.options.db.datacompare.tips')}
      style={{ marginBottom: '16px' }}
    />

    <div className={styles.cardContainer}>
      <Tabs
        type="card"
        defaultActiveKey="1"
        items={tabGroups($t, styles)}
      />
    </div>
  </>);
}

const DataCompare = props => (
  // <KeepAlive name={keepName}>
  <DataCompareBase {...props} />
  // </KeepAlive>
);

export { DataCompare };
