const normFile = (e) => {
  if (Array.isArray(e)) {
    return e;
  }
  return e && e.fileList;
};

export const accountForm = ($t, accountProps) => [{
  valueType: 'upload',
  name: accountProps.name,
  label: $t('app.options.db.datacompare.account') + $t('app.options.db.datacompare.file'),
  rules: [{ required: true, message: $t('app.options.db.datacompare.please') }],
  otherProps: {
    valuePropName: 'fileList',
    getValueFromEvent: normFile
  },
  fieldProps: accountProps
}];

export const positionForm = ($t, positionProps) => [{
  valueType: 'upload',
  name: positionProps.name,
  label: $t('app.options.db.datacompare.position') + $t('app.options.db.datacompare.file'),
  rules: [{ required: true,  message: $t('app.options.db.datacompare.please') }],
  otherProps: {
    valuePropName: 'fileList',
    getValueFromEvent: normFile
  },
  fieldProps: positionProps
}];
