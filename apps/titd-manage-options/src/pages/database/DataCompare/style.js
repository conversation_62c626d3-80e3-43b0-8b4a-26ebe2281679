import { createStyles } from 'antd-style';

export const useStyles = createStyles(({ css, prefixCls }) => ({
  cardContainer: css`
    & > .${prefixCls}-tabs-card {
      .${prefixCls}-tabs-content {
        margin-top: -16px;

        .${prefixCls}-tabs-tabpane {
          padding: 24px;
          background: #fff;
        }
      }

      .${prefixCls}-tabs-nav::before {
        display: none;
      }
      .${prefixCls}-tabs-tab {
        background: transparent;
        border-color: transparent;
      }
      .${prefixCls}-tabs-tab-active{
        background: #fff;
        border-color: #fff;
      }
    }
  `,
  doubleTable: css`
    table {
      border-collapse: collapse;
    }
    .ti-tr-border:not(:last-child) {
      border-bottom: 3px double #ddd;
    }

    .ti-bg-orange {
      background-color: #fff7e6;
    }
    .ti-bg-red {
      background-color: #fff1f0;
    }
    .ti-bg-blue {
      background-color: #e6f7ff;
    }
    .ti-bg-green {
      background-color: #f6ffed;
    }

    // 取消行 Hover 颜色，排除固定列
    tr.${prefixCls}-table-row:hover > td:not(.ant-table-cell-fix-right) {
      background: none;
    }
  `,
}));
