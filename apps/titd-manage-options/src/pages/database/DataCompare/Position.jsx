import { useState } from 'react';
import { useNavigate } from 'react-router';
import { Typography, Divider, App } from 'antd';
import { useRequest, useUnmount } from 'ahooks';
import { MacScrollbar } from 'mac-scrollbar';

import {
  TableSimple,
  TableFields,
  ActionLinks,
  links,
  callOptionModal,
  putOptionModal,
} from '@titd/publics/components';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  currency,
  errorResp,
} from '@titd/publics/utils';

import UploadForm from './UploadForm';
import { positionForm } from './data';
import { tableTitle, uploadProps, actionUrl, showMsg, compareIconDict, compareBgColor } from './config';

const { Text } = Typography;
const { TableBadgeDot } = TableFields;
const { showCalcLink } = links;
const { exchangeAllDict, coveredDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { formatDot } = currency;

const PositionCompare = ({ styles }) => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [sameSataSource, setSameSataSource] = useState([]);
  const [dataFilter, setDataFilter] = useState([]);
  const [msgData, setMsgData] = useState({
    titdonlytip: 'position.titdonly',
    ctponlytip: 'position.ctponly',
    openonlytip: 'position.openonly',
  });

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = accountFilter => {
    return [{
      dataIndex: 'diff',
      width: 36,
      align: 'center',
      render: text => compareIconDict[text],
    }, {
      title: $t('app.options.db.datacompare.accountid'),
      dataIndex: 'accountid',
      render: (text, record) => record.id >= 0 && <Text strong>{text}</Text>,
      filters: accountFilter?.map(item => ({
        text: item,
        value: item,
      })),
      onFilter: (value, record) => record.accountid?.indexOf(value) === 0,
    }, {
      title: $t('app.options.db.datacompare.clientid'),
      dataIndex: 'clientid',
      render: text => <Text strong>{text}</Text>,
    }, {
      title: $t('app.options.exchangeid'),
      dataIndex: 'exchangeid',
      render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
    }, {
      title: $t('app.options.instrumentid'),
      dataIndex: 'instrumentid',
    }, {
      title: $t('app.options.postion.covereduncovered'),
      dataIndex: 'coveredoruncovered',
      render: text => coveredDict[text] || text,
    }, {
      title: $t('app.options.db.datacompare.longvolume'),
      dataIndex: 'longvolume',
      align: 'right',
      render: text => formatDot(text)
    }, {
      title: $t('app.options.db.datacompare.shortvolume'),
      dataIndex: 'shortvolume',
      align: 'right',
      render: text => formatDot(text)
    }, {
      title: $t('app.options.db.datacompare.longcomb'),
      dataIndex: 'longcomb',
      align: 'right',
      render: text => formatDot(text)
    }, {
      title: $t('app.options.db.datacompare.shortcomb'),
      dataIndex: 'shortcomb',
      align: 'right',
      render: text => formatDot(text)
    }, {
      title: $t('app.options.db.datacompare.margin'),
      dataIndex: 'margin',
      align: 'right',
      render: (text, record) => record.datasrc !== 'CTP' ? diffText(text) : '-'
    }, {
      title: $t('app.options.mem.account.combmargin'),
      dataIndex: 'combmargin',
      align: 'right',
      render: (text, record) => record.datasrc !== 'CTP' ? diffText(text) : '-'
    }, {
      title: $t('app.options.db.datacompare.discount'),
      dataIndex: 'discount',
      align: 'right',
      render: text => diffText(text)
    }, {
      title: $t('app.options.db.datacompare.datasrc'),
      dataIndex: 'datasrc',
      width: 60,
      render: text => text && <Text code>{text}</Text>,
    }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 120,
    render: (_, record) => (record.datasrc && record.datasrc !== 'CTP') && (
      <ActionLinks links={[
        showCalcLink(toShowCalc, record),
      ]} />
    )
  }];
  }

  const diffText = text => (
    <Text>{text && formatDot(text !== 0 ? text.toFixed(2) : text)}</Text>
  );

  const toShowCalc = record => {
    const params = {
      ...defaultParams,
      exchangeid: record.exchangeid,
      clientid: record.clientid,
      instrumentid: record.instrumentid,
      svrid: record.datasrc,
    };

    fetchFunc(MESSAGE_TYPE.MemQryMarginPrice, params, (resp) => {
      if (resp?.length > 0) {
        const respData = resp[0];
        const optionName = respData.instrumentname?.slice(6, 7);

        // C:认购期权, P:认沽期权
        if (optionName === 'P') {
          putOptionModal({
            intl: $t,
            respData: respData,
          });
        } else if (optionName === 'C') {
          callOptionModal({
            intl: $t,
            respData: respData,
          });
        } else {
          message.error($t('app.options.mem.margincalc.notopt'));
        }
      } else {
        message.error($t('app.options.mem.margincalc.error'));
      }
    });
  }

  const PositionUpload = {
    ...uploadProps($t),
    name: 'postionFile',
    action: actionUrl(MESSAGE_TYPE.UploadPosition),
    data: defaultParams,
  };

  const createUploadForm = positionForm($t, PositionUpload);

  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.CheckPosition), params);

    const { data: respData } = resp;
    const tableData = [];
    const tableFilter = [];
    // 不同
    const compareList = respData.item;
    if (compareList?.length > 0) {
      compareList.forEach((item, idx) => {
        const filterArr = compareList.filter(i => i.idxno === item.idxno);
        let addData;

        // 计算投资者保证金
        item.discount = item.datasrc === 'CTP' ? item.margin : item.margin - item.combmargin;

        if (filterArr.length === 2) {
          item.diff = 2;

          const filterIndex = filterArr.findIndex(i => i.datasrc === item.datasrc);

            if (filterIndex === 1) {
              addData = {
                id: -idx,
                border: true,
                accountid: item.accountid,
                longvolume: filterArr[0].longvolume - item.longvolume,
                shortvolume: filterArr[0].shortvolume - item.shortvolume,
                longcomb: filterArr[0].longcomb - item.longcomb,
                shortcomb: filterArr[0].shortcomb - item.shortcomb,
                discount: filterArr[0].discount - item.discount,
              }
            }
        } else {
          if (item.datasrc === 'CTP') {
            item.diff = 1;
          } else {
            item.diff = 0;
          }
          item.border = true;
        }

        tableData.push({
          ...item,
          id: idx + 1,
        });

        // 添加计算行
        if (addData) {
          tableData.push(addData);
        }

        // 过滤条件
        if (tableFilter.indexOf(item.accountid) === -1) {
          tableFilter.push(item.accountid);
        }
      });
    }

    // 相同
    if (respData.sucitem?.length > 0) {
      const sameTableData = respData.sucitem.map((item, idx) => {
        if (tableFilter.indexOf(item.accountid) === -1) {
          tableFilter.push(item.accountid);
        }

        return {
          ...item,
          id: idx,
          discount: item.datasrc === 'CTP' ? item.margin : item.margin - item.combmargin,
          diff: 3,
          border: idx % 2 === 0 ? false : true
        };
      });

      setSameSataSource(sameTableData);
    } else {
      setSameSataSource([]);
    }

    setDataFilter([...tableFilter]);

    setMsgData({
      ...msgData,
      td: respData.tdnum,
      up: respData.upnum,
      success: respData.successnum,
      failed: respData.failednum,
      titdonly: respData.onlytdnum,
      ctponly: respData.onlyupnum,
      openonly: respData.egnorenum,
    });

    // 校验成功后清除上传文件
    // uploadForm.current.reset();

    return tableData;
  }

  const {
    data: dataSource = [],
    loading,
    run,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: { data: errData } } = err;
      message.error(errData.errmessage);
    },
  });

  const onSearch = async () => {
    // console.log(form);
    run(defaultParams);
  }

  return (<>
    <UploadForm
      formData={createUploadForm}
      onFinish={onSearch}
      isLoading={loading}
    />

    {/* 持仓 */}
    <TableSimple
      className={styles.doubleTable}
      columns={columns(dataFilter)}
      dataSource={dataSource}
      isLoading={loading}
      title={() => tableTitle('app.options.db.datacompare.position', $t)}
      footer={() => showMsg(msgData, 1, $t)}
      rowClassName={record => [
        compareBgColor[record.diff],
        record.border ? 'ti-tr-border' : '',
      ].join(' ')}
    />

    {sameSataSource.length > 0 && (
      <div className="ti-same-table">
        <Divider
          plain
          orientation="left"
        >{$t('app.options.db.datacompare.common')}</Divider>

        <MacScrollbar style={{ maxHeight: '600px' }}>
          <TableSimple
            className={styles.doubleTable}
            columns={columns(dataFilter)}
            dataSource={sameSataSource}
            isLoading={loading}
            rowClassName={record => record.border ? 'ti-tr-border' : ''}
          />
        </MacScrollbar>
      </div>
    )}
  </>);
}

export default PositionCompare;
