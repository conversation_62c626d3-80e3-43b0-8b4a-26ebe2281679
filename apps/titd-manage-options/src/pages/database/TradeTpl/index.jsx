import { useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router';
import { Typography, Space, Tag, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount, useDebounceFn } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  formOptions,
  updateRowData,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeDict, exchangeAllDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;
const { checkOptions } = formOptions;

const TradeTplBase = ({ tplId }) => {
  const queryForm = useRef(null);
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.templateid'),
    dataIndex: 'templateid',
    key: 'templateid',
    sorter: sortByName('templateid'),
    sortOrder: sortedInfo.columnKey === 'templateid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    filters: checkOptions(exchangeDict),
    filteredValue: filteredInfo.exchangeid || null,
    onFilter: (value, record) => record.exchangeid === value,
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
    render: text => formatProduct(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const [autoOptions, setAutoOptions] = useState([]);
  const [productGroup, setProductGroup] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [productSearch, setProductSearch] = useState([]);
  const [isModify, setIsModify] = useState(false);

  // const { run } = useDebounceFn((value) => {
  //   var isExist = autoOptions.find(item => item.value === value);
  //   if (isExist) {
  //     message.error('权限模版已存在，请重新输入');
  //     return;
  //   }
  // }, { wait: 500 });

  const { run: productRun } = useDebounceFn((rules, value, callback) => {
    const isExist = autoOptions.find(item => item.value === value);
    if (isExist) {
      callback(new Error($t('app.options.templateid.error')));
    } else {
      callback();
    }
  }, { wait: 500 });

  const createSearchForm = useMemo(() => searchForm($t, autoOptions, productSearch), [$t, autoOptions, productSearch]);
  const createAddForm = useMemo(() => addForm($t, isModify, productRun, productOptions), [$t, isModify, productRun, productOptions]);

  useMount(() => {
    getProduct();
  });
  useUnmount(() => cancel());

  useEffect(() => {
    // console.log(tplId);
    if (tplId) {
      queryForm.current?.set({
        'templateid': tplId
      });
      onSearch({ templateid: tplId });
    }
  }, [tplId]); // eslint-disable-line react-hooks/exhaustive-deps

  const formatProduct = text => (
    <Space size={[0, 16]} wrap style={{ maxWidth: '800px' }}>
      {text?.map((item, idx) => <Tag key={idx}>{item}</Tag>)}
    </Space>
  );

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeTemplate), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        const autoId = respData.map(item => ({
          label: item.templateid,
          value: item.templateid,
        }));
        setAutoOptions(autoId);
      } else {
        setAutoOptions([]);
      }
    }

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    setFilteredInfo({});
    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeTemplate), {
      ...defaultParams,
      templateid: record.templateid,
      exchangeid: record.exchangeid,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }
    return null;
  }
  const getProduct = async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryProduct), defaultParams);

    const { data: productRsp } = resp;
    if (productRsp.length > 0) {
      const groups = [];
      productRsp?.forEach(item => {
        if (!groups[item.exchangeid]) {
          groups[item.exchangeid] = [];
        }
        groups[item.exchangeid].push({
          value: item.underlyinginstrid,
        });
      });
      // console.log(groups.flat());
      setProductGroup(groups);

      const flatProduct = [];
      productRsp.forEach(item => {
        const idx = flatProduct.findIndex(i => i.value === item.underlyinginstrid);

        if (idx === -1) {
          flatProduct.push({
            value: item.underlyinginstrid,
          });
        }
      });
      setProductSearch(flatProduct);
    }
  }

  const onRefresh = () => {
    refresh();
  };
  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });

    // 去除Route的过滤项
    tplId && navigate(location.pathname, { replace: true });
  }
  const onReset = () => {
    run(defaultParams);

    // 去除Route的过滤项
    tplId && navigate(location.pathname, { replace: true });
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setProductOptions([]);
    modalForm.current.show();
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      setIsModify(true);
      setProductOptions(productGroup[newRecord.exchangeid]);
      modalForm.current.show(1, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
      });
      mutate(newData);
    } else {
      message.error($t('app.general.deleted'));
      onRefresh();
    }
  }
  const deleteFunc = async (record) => {
    const deleteData = {
      ...defaultParams,
      templateid: record.templateid,
      exchangeid: record.exchangeid,
    }

    fetchFunc(MESSAGE_TYPE.DelTradeTemplate, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }
  const toDeleteMore = (records) => {
    // console.log('Delete More');
    const ids = records.map(i => i.templateid + ':' + i.productid);
    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record);
        });

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }
  const toDelete = (record) => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.templateid + ':' + record.productid,
      onOk: () => {
        // console.log('ok', record);
        deleteFunc(record);

        // 删除后取消选择
        setSelectKeys([]);
      }
    })
  }

  const formValueChange = changedValues => {
    if ('exchangeid' in changedValues) {
      const value = changedValues.exchangeid;

      setProductOptions(productGroup[value] || []);

      // 有值则清空品种
      if (value) {
        modalForm.current?.set({
          'productid': []
        });
      }
    }
  }
  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdTradeTemplate
      : MESSAGE_TYPE.InsTradeTemplate, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      onRefresh(!type);
    });

    // await form.productid?.forEach(item => {
    //   const postData = {
    //     ...defaultParams,
    //     templateid: form.templateid,
    //     exchangeid: form.exchangeid,
    //     productid: item,
    //   }

    //   fetchFunc(MESSAGE_TYPE.InsTradeTemplate, postData, () => {
    //     message.success(CURD.Insert + CURD.StatusOkMsg);
    //   });
    // });

    // onRefresh();
  }

  const tableChange = (pagination, filters, sorter) => {
    // console.log('Various parameters', pagination, filters, sorter);
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.tradetpl', {
        defaultMsg: '权限模版',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      onValuesChange={formValueChange}
    />
  </>);
}

const TradeTpl = props => {
  const location = useLocation();
  const tplId = location.state?.datas || '';

  return (
    // <KeepAlive name={keepName}>
    <TradeTplBase {...props} tplId={tplId} />
    // </KeepAlive>
  );
}

export { TradeTpl };
