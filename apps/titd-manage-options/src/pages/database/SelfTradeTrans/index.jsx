import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  formOptions,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, NegaNumber, TableTitle } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { exchangeDict, exchangeAllDict, selfTradeDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;
const { getProductByExchange } = getList;
const { checkOptions } = formOptions;

const SelfTradeTransBase = () => {
  const queryForm = useRef(null);
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    filters: checkOptions(exchangeDict),
    filteredValue: filteredInfo.exchangeid || null,
    onFilter: (value, record) => record.exchangeid === value,
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
  //   title: $t('app.future.producttype'),
  //   dataIndex: 'producttype',
  //   filters: checkOptionsMap(productTypeDict, {isNum: true}),
  //   filteredValue: filteredInfo.producttype || null,
  //   onFilter: (value, record) => record.producttype === value,
  //   render: text => productTypeDict[text] || text,
  // }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
    key: 'productid',
    sorter: sortByName('productid'),
    sortOrder: sortedInfo.columnKey === 'productid' ? sortedInfo.order : '',
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => text || (<Text type="secondary">
      {$t('app.general.all') + $t('app.options.clientid')}
    </Text>),
  }, {
    title: $t('app.future.selftradetrans.selftradetype'),
    dataIndex: 'open1',
    render: text => selfTradeDict[text] || text,
  }, {
    title: TableTitle(
      $t('app.future.selftradetrans.ratio'),
      $t('app.future.selftradetrans.ratio.long')
    ),
    dataIndex: 'open2',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const [exchangeId, setExchangeId] = useState();
  const [productSearch, setProductSearch] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);
  const [hasRatio, setHasRatio] = useState(false);

  const searchFocus = useCallback(() => {
    const qForm = queryForm.current;
    const searchParams = {
      ...defaultParams,
      exchangeid: qForm?.get('exchangeid') || 0,
      // producttype: qForm?.get('producttype'),
    }

    getProductByExchange(Request, searchParams, setProductSearch);
  }, [defaultParams]);

  const productFocus = useCallback(async () => {
    if (exchangeId) {
      // const aForm = modalForm.current;
      const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryProductStk), {
        ...defaultParams,
        exchangeid: exchangeId,
        // producttype: aForm?.get('producttype'),
      });

      const products = resp?.map(item => ({
        value: item.productid,
      }));

      setProductOptions(products);
    } else {
      setProductOptions([]);
      message.warning('请先选择交易所');
    }
  }, [defaultParams, exchangeId]); // eslint-disable-line react-hooks/exhaustive-deps

  const createSearchForm = useMemo(() => searchForm($t, searchFocus, productSearch), [$t, searchFocus, productSearch]);
  const createAddForm = useMemo(() => addForm($t, isModify, productFocus, productOptions, hasRatio), [$t, isModify, productFocus, productOptions, hasRatio]);

  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QrySelfTradeTrans), params);

    const { data: respData } = resp;
    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setExchangeId(null);
    setProductOptions([]);
    setHasRatio(false);

    modalForm.current.show();
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    setIsModify(true);
    setExchangeId(record.exchangeid);
    setProductOptions([]);
    setHasRatio(record.open1 === 4);

    modalForm.current.show(1, record);
  }
  const deleteFunc = async (record, showMsg) => {
    const deleteData = {
      ...defaultParams,
      exchangeid: record.exchangeid,
      producttype: record.producttype,
      productid: record.productid,
      clientid: record.clientid,
    }

    fetchFunc(MESSAGE_TYPE.DelSelfTradeTrans, deleteData, () => {
      showMsg && message.success(CURD.Delete + CURD.StatusOkMsg);
      refresh();
    });
  }
  const toDeleteMore = (records) => {
    // console.log('Delete More');
    const ids = records.map(i => i.accountid);

    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record, true);
        });
      }
    });
  }
  const toDelete = (record) => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.accountid,
      onOk: () => {
        // console.log('ok', record);
        deleteFunc(record, true);
      }
    });
  }

  const formValueChange = changedValues => {
    if ('exchangeid' in changedValues) {
      setExchangeId(changedValues.exchangeid);

      // 有值则清空品种
      modalForm.current?.set({
        'productid': undefined,
        // 'instrumentid': undefined,
      });
    }

    if ('open1' in changedValues) {
      const value = changedValues.open1;

      setHasRatio(value === 4);
    }
  }

  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
      producttype: 79,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdSelfTradeTrans
      : MESSAGE_TYPE.InsSelfTradeTrans, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      refresh();
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.limit.selftradetrans', {
        defaultMsg: '自成交转换',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: refresh,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
      onValuesChange={formValueChange}
    />
  </>);
}

const SelfTradeTrans = props => (
  // <KeepAlive name={keepName}>
  <SelfTradeTransBase {...props} />
  // </KeepAlive>
);

export { SelfTradeTrans };
