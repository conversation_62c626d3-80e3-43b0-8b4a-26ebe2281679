import { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Row, Col, Transfer, Divider, Typography } from 'antd';

import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
} from '@titd/publics/utils';

const { Text } = Typography;
const { clientRightDict } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;

const tplDiff = arr => {
  if (arr.length === 0) return [];

  const arrSet = new Set(arr.map(item => item.templateid));
  const arrName = [...arrSet].map(item => ({
    key: item,
    title: item,
  }));

  return arrName;
}

const TransferItem = ({
  label,
  titles,
  dataSource,
  targetKeys,
  change,
  selectedKeys,
  selectChange,
}) => {
  return (
    <Col xs={24} lg={12} xxl={8}>
      <Divider orientation="left" plain>{label}</Divider>
      <Transfer
        oneWay
        titles={titles}
        dataSource={dataSource}
        targetKeys={targetKeys}
        selectedKeys={selectedKeys}
        onChange={change}
        onSelectChange={selectChange}
        render={item => item.title}
      />
    </Col>
  );
}

let isModify = false;

const RoleTransfer = (props, ref) => {
  const {
    clientData,
    defaultParams,
    setRoleFunc,
  } = props;
  const { FormattedMsg } = useFormattedMessage();

  const titles = [
    <Text type="secondary" key="0">{FormattedMsg('app.options.db.userinfo.right.none')}</Text>,
    <Text type="secondary" key="1">{FormattedMsg('app.options.db.userinfo.right.have')}</Text>,
  ]

  const [isChange, setIsChange] = useState(false);
  // 权限模版
  const [tplData, setTplData] = useState([]);
  const [checkTpl, setCheckTpl] = useState([]);
  const [selectTpl, setSelectTpl] = useState([]);
  // 报价模版
  // const [quoteData, setQuoteData] = useState([]);
  const [checkQuote, setCheckQuote] = useState([]);
  const [selectQuote, setSelectQuote] = useState([]);
  // 交易权限
  const [rightData, setRightData] = useState([]);
  const [checkRight, setCheckRight] = useState([]);
  const [selectRight, setSelectRight] = useState([]);

  useImperativeHandle(ref, () => ({
    submit: () => {
      if (isChange) {
        // console.log('Role submit', checkClient);
        // const roleParams = {
        //   ...defaultParams,
        //   exchangeid: checkClient.exchangeid,
        //   clientid: checkClient.clientid,
        //   clienttype: checkClient.clienttype,
        //   templateid: checkTpl,
        //   rightid: checkRight,
        // }
        // // console.log(roleParams, isModify);
        // const tmpModify = isModify;

        // fetchFunc(tmpModify
        //   ? MESSAGE_TYPE.UpdTradeRight
        //   : MESSAGE_TYPE.InsTradeRight, roleParams, () => {
        //     message.success((tmpModify ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
        //   });

        setRoleFunc({
          ...clientData,
          modify: isModify,
          templateid: checkTpl,
          quotetemplateid: checkQuote,
          rightid: checkRight,
        });
      }
    },
    setRole: () => {
      setRoleFunc({
        ...clientData,
        templateid: checkTpl,
        quotetemplateid: checkQuote,
        rightid: checkRight,
      });
    },
  }));

  const getTpl = async (exchangeid) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeTemplate), {
      ...defaultParams,
      exchangeid: exchangeid,
    });
    const { data: respData } = resp;
    const autoId = tplDiff(respData);
    setTplData(autoId);
  };
  const setRight = () => {
    const rightList = clientRightDict.map((item, idx) => ({
      key: idx,
      title: item,
    })).splice(1);
    // console.log(rightList);

    setRightData(rightList);
  }
  // const getHasRights = async (client) => {
  //   // console.log(client);
  //   const resp = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeRight), {
  //     ...defaultParams,
  //     clientid: client.clientid,
  //     clienttype: client.clienttype,
  //     exchangeid: client.exchangeid,
  //   });

  //   if (resp?.length > 0) {
  //     const respData = resp[0];

  //     setCheckTpl(respData.templateid);
  //     setCheckRight(respData.rightid);

  //     isModify = true;
  //   }
  // }

  const clearReset = () => {
    setCheckTpl([]);
    setCheckQuote([]);
    setCheckRight([]);
    setSelectTpl([]);
    setSelectQuote([]);
    setSelectRight([]);
    setIsChange(false);
    isModify = false;
  }

  useEffect(() => {
    if (clientData) {
      // 获取模版列表
      getTpl(clientData.exchangeid);
      // 获取权限列表
      setRight();
      // 获取拥有的权限
      clientData.templateid && setCheckTpl(clientData.templateid);
      clientData.quotetemplateid && setCheckQuote(clientData.quotetemplateid);
      clientData.rightid && setCheckRight(clientData.rightid);
      // if (clientData.role) {
      //   const clientRole = clientData.role;
      //   setCheckTpl(clientRole.templateid);
      //   setCheckRight(clientRole.rightid);
      // } else {
      //   getHasRights(clientData);
      // }
    }

    return () => {
      // 每次切换清除权限选择列表
      clearReset();
    }
  }, [clientData]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <Row gutter={{ sm: 16, md: 24, lg: 32 }}>
      <TransferItem
        label={FormattedMsg('app.menu.db.tradetpl')}
        titles={titles}
        dataSource={tplData}
        targetKeys={checkTpl}
        selectedKeys={selectTpl}
        change={(nextTargetKeys) => {
          // console.log('nextTargetKeys:', nextTargetKeys);
          setCheckTpl(nextTargetKeys);

          if(!isChange) {
            setIsChange(true);
          }
        }}
        selectChange={(sourceSelectedKeys, targetSelectedKeys) => {
          // console.log(sourceSelectedKeys, targetSelectedKeys);
          setSelectTpl([...sourceSelectedKeys, ...targetSelectedKeys]);
        }}
      />
      <TransferItem
        label={FormattedMsg('app.future.quoteid')}
        titles={titles}
        dataSource={tplData}
        targetKeys={checkQuote}
        selectedKeys={selectQuote}
        change={(nextTargetKeys) => {
          // console.log('nextTargetKeys:', nextTargetKeys);
          setCheckQuote(nextTargetKeys);

          if(!isChange) {
            setIsChange(true);
          }
        }}
        selectChange={(sourceSelectedKeys, targetSelectedKeys) => {
          // console.log(sourceSelectedKeys, targetSelectedKeys);
          setSelectQuote([...sourceSelectedKeys, ...targetSelectedKeys]);
        }}
      />
      <TransferItem
        label={FormattedMsg('app.menu.db.traderight')}
        titles={titles}
        dataSource={rightData}
        targetKeys={checkRight}
        selectedKeys={selectRight}
        change={(nextTargetKeys) => {
          // console.log('nextTargetKeys:', nextTargetKeys);
          setCheckRight(nextTargetKeys);

          if(!isChange) {
            setIsChange(true);
          }
        }}
        selectChange={(sourceSelectedKeys, targetSelectedKeys) => {
          // console.log(sourceSelectedKeys, targetSelectedKeys);
          setSelectRight([...sourceSelectedKeys, ...targetSelectedKeys]);
        }}
      />
    </Row>
  );
}

export default forwardRef(RoleTransfer);
