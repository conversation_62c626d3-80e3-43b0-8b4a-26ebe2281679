import { useRef, useState, useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Tabs, Button, Typography, Space, Switch, message } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import useFetch from 'use-http';
import { useIntl } from 'react-intl';

import TiTable from '@components/Table';
import { IndexColumn, TableBadge, TableBadgeDot } from '@components/Table/fields';
import { ModalForm, Confirm } from '@components/form';
import { clientAddForm, fronzenAddForm } from './data';
import ActionLinks from '@components/Table/ActionLinks';
import {
  updateLink,
  deleteLink,
  cancelLink,
} from '@components/actions/links';

import { clientTypeDict, exchangeDict, accountTypeDict, tagNormalColors, DEFAULT_ACCOUNT_TYPE } from '@utils/dicts';
import { MESSAGE_TYPE, SITE_URL } from '@utils/consts';
import { DEFAULT_SESSION_PARAMS } from '@utils/session';
import { formatName, digitUppercase } from '@utils/currency';
import { rebuildIndex } from '@utils/common';
import errorResp from '@utils/error-response';

import RoleTransfer from './RoleTransfer';

const { Text } = Typography;

// 查找在数组中的序号
const dataFindIndex = (data, record, name) => {
  return data.findIndex(item => item[name[0]] + item[name[1]] === record[name[0]] + record[name[1]]);
}

// 交易编码共同方法
const setTmpDataFunc = ({ record, type, setFunc, name, data, isDisabled = false }) => {
  const idx = dataFindIndex(data, record, name);

  if (idx > -1) {
    const newRecord = data[idx];

    data[idx] = {
      ...newRecord,
      ...record,
      type: newRecord?.type || type
    };

    if (isDisabled) {
      data[idx].isDisabled = true;
    }

    setFunc([...data]);
  }
}

// 全局缓存，供修改恢复用
let clientData = [];
let fronzenData = [];

const Client = (props, ref) => {
  const { currentForm, user, segmentList, serverMap } = props;
  const intl = useIntl();

  const [selectKeys, setSelectKeys] = useState([]);
  const navigate = useNavigate();
  // const [selectRecord, setSelectRecord] = useState(null);
  const [tabKey, setTabKey] = useState('0');
  const [importUpdate, setImportUpdate] = useState(false);

  const roleTrans = useRef(null);
  const clientForm = useRef(null);
  const clientNames = ['clientid', 'exchangeid'];
  const [clientTmpData, setClientTmpData] = useState([]);
  const fronzenForm = useRef(null);
  const fronzenNames = ['accountid', 'exchangeid'];
  const [fronzenTmpData, setFronzenTmpData] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);

  // type: 1新增,2修改,3删除, 4启用, 5关闭
  const actionStatus = useMemo(() => {
    return ['', {
      title: $t('app.general.insert'),
      color: '#87d068',
    }, {
      title: $t('app.general.update'),
      color: '#108ee9',
    }, {
      title: $t('app.general.delete'),
      color: '#f50',
    }, {
      title: $t('app.general.open'),
      color: '#2db7f5',
    }, {
      title: $t('app.general.close'),
      color: '#ccc'
    }];
  }, [intl]);

  useImperativeHandle(ref, () => ({
    reset: () => {
      setSelectKeys([]);
    },
    submit: () => {
      // 最后一步权限保存，统一处理
      roleTrans.current?.submit();

      // console.log(clientTmpData, fronzenTmpData);

      // 交易编码提交
      clientTmpData.forEach(item => {
        switch (item.type) {
          case 1:
          case 4:
            clientInsertFunc(item);
            break;
          case 2:
            const idx = dataFindIndex(clientData, item, clientNames);
            // 修改，先删除再新增
            clientDeleteFunc(clientData[idx]);
            clientInsertFunc(item);
            break;
          case 3:
          case 5:
            clientDeleteFunc(item);
            break;
          default:
            break;
        }

        // 权限修改提交
        if (item.role) {
          clientRoleFunc(item, item.role.modify);
        }
      });

      // 资金分配提交
      fronzenTmpData.forEach(item => {
        switch (item.type) {
          case 1:
            fronzenInsertFunc(item);
            break;
          case 2:
            fronzenInsertFunc(item, 1);
            break;
          case 3:
            fronzenDeleteFunc(item);
            break;
          default:
            break;
        }
      })
    },
    // delete: (record) => {
    //   console.log('delete', record);
    // }
  }));

  const fetchFunc = async (type, params, func, msg = true) => {
    const resp = await post(SITE_URL.EXTEND(type), params);

    if (response.ok) {
      func && func(resp.data);
    } else if (msg) {
      errorResp(resp.data, navigate, message);
    }
  }

  const ClientColumns = [{
    dataIndex: 'id',
    width: 48,
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.segmentid'),
    dataIndex: 'segmentid',
    render: (text, record) => (
      <Space align="center">
        {record.type ? TableBadge(actionStatus[record.type].title, actionStatus[record.type].color) : null}
        {text}
      </Space>
    ),
  }, {
    title: $t('app.options.defaultsvrid'),
    dataIndex: 'defaultsvrid',
  }, {
  //   title: TableTitle(
  //     $t('app.options.serverid'),
  //     $t('app.options.serverid.long')
  //   ),
  //   dataIndex: 'serverid',
  //   render: (text, record) => (<Space align="center">
  //     {record.type ? TableBadge(actionStatus[record.type].title, actionStatus[record.type].color) : null}
  //     {serverList.find(item => item.value === text)?.label || text}
  //   </Space>),
  // }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clienttype'),
    dataIndex: 'clienttype',
    render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    algin: 'center',
    fixed: 'right',
    width: 150,
    onCell: () => {
      return { onClick: e => e.stopPropagation() }
    },
    render: (_, record) => record.isSystem ? (
      record.type !== 2 ? <Space>
        <Switch
          checkedChildren={<CheckOutlined />}
          unCheckedChildren={<CloseOutlined />}
          // onChange={value => importClientChange(value, record)}
          onChange={value => record.defaultsvrid ? importClientChange(value, record) : importClientUpdate(value, record)}
          checked={!record.isDisabled}
        />
        {!record.isDisabled && <ActionLinks links={[
          updateLink(toClientUpdate, record),
        ]} />}
      </Space> : <ActionLinks links={[
        cancelLink(toClientCancel, record),
      ]} />
    ) : (
      <ActionLinks links={record.type ? [
        cancelLink(toClientCancel, record),
      ] : [
        updateLink(toClientUpdate, record),
        deleteLink(toClientDelete, record),
      ]} />
    ),
  }];

  const FronzenColumns = [{
    dataIndex: 'id',
    width: 48,
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    render: (text, record) => (<Space align="center">
      {record.type ? TableBadge(actionStatus[record.type].title, actionStatus[record.type].color) : null}
      <Text strong>{text}</Text>
    </Space>),
  }, {
    title: $t('app.options.accounttype'),
    dataIndex: 'accounttype',
    render: text => TableBadgeDot(text, accountTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.frozenfunds'),
    dataIndex: 'frozenfunds',
    align: 'right',
    render: text => formatName(text)
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    algin: 'center',
    fixed: 'right',
    width: 130,
    render: (_, record) => (
      <ActionLinks links={record.type ? [
        cancelLink(toFronzenCancel, record),
      ] : [
        updateLink(toFronzenUpdate, record),
        deleteLink(toFronzenDelete, record),
      ]} />
    )
  }];

  useEffect(() => {
    if (user) {
      getClientTable(user);
      getFronzenTable(user);
    }

    return () => {
      abort();
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  // useEffect(() => {
  //   if (selectKeys.length > 0) {
  //     setCheckClient(clientTmpData[selectKeys[0] - 1]);
  //   }
  // }, [selectKeys]);

  // --------- Client ---------
  const getClientTable = async (userId) => {
    const params = {
      ...defaultParams,
      usertype: 0,
      userid: userId,
    }

    fetchFunc(MESSAGE_TYPE.QryUserClient, params, async (resp) => {
      let combineData = resp.map(item => ({
        ...item,
        defaultsvrid: item.defaultsvr ? item.enablesvrid : '',
      }));
      // 获取导入的交易编码
      await fetchFunc(MESSAGE_TYPE.QryImportClient, {
        ...defaultParams,
        accountid: userId,
      }, (importResp) => {
        // 合并导入和新增的交易编码
        if (importResp.length > 0) {
          const importClient = importResp.map(item => {
            const clientItem = {
              ...item,
              userid: userId,
              isSystem: true,
              isDisabled: true,
            };

            item.serverid && (clientItem.defaultsvrid = item.serverid);

            const idx = dataFindIndex(combineData, item, clientNames);
            if (idx > -1) {
              clientItem.defaultsvrid = combineData[idx].defaultsvrid;
              clientItem.isDisabled = false;
              clientItem.isActive = true;
              combineData.splice(idx, 1);
            }

            return clientItem;
          });

          combineData = combineData.concat(importClient);
        }

        const tableData = rebuildIndex(combineData);
        clientData = tableData;
        setClientTmpData([...tableData]);

        // 默认选择第一个可选项
        if (tableData.length > 0) {
          const firstSelected = tableData.find(i => !i.isDisabled);
          if (firstSelected) {
            setSelectKeys([firstSelected.id]);
          }
        }
      }, false);
    }, false);
  }
  // const onClientRefresh = (user) => {
  //   // console.log('Refresh');
  //   getClientTable(user);
  // }
  const importClientChange = (value, record) => {
    const idx = dataFindIndex(clientTmpData, record, clientNames);

    // 设置类型
    let clientType = value ? 4 : 0;
    if (value && !clientTmpData[idx].isActive) {
      clientType = 4;
    } else if (!value && clientTmpData[idx].isActive) {
      clientType = 5;
    } else {
      clientType = 0;
    }

    clientTmpData[idx] = {
      ...clientTmpData[idx],
      isDisabled: !value,
      type: clientType,
      segmentid: record.segmentid,
      defaultsvrid: record.defaultsvrid,
    }

    // 未提交清除权限
    // if (!value) {
    //   delete clientTmpData[idx].templateid;
    //   delete clientTmpData[idx].rightid;
    // }

    setSelectKeys([]);
    setClientTmpData([...clientTmpData]);
    setImportUpdate(false);
  }
  const importClientUpdate = (value, record) => {
    if (value) {
      setImportUpdate(true);
      toClientUpdate(record);
    } else {
      importClientChange(value, record);
    }
  }

  const [serverList, setServerList] = useState([]);

  const createClientAddForm = useMemo(() => clientAddForm(intl, isModify, segmentList, serverList), [intl, isModify, segmentList, serverList]);
  const toClientInsert = () => {
    // const userId = currentForm.current.get('userid');

    // if (userId) {
    //   clientForm.current.show(0, {
    //     serverid: 0,
    //     userid: userId,
    //   });
    // } else {
    //   message.error('请先填写用户ID');
    // }
    currentForm.current.validate(['userid']).then(() => {
      const userId = currentForm.current.get('userid');

      setIsModify(false);
      clientForm.current.show(0, {
        userid: userId,
        clienttype: 1,
      });
    }).catch((err) => {
      message.error('请先填写用户编号');
    });
  }
  const toClientUpdate = (record) => {
    record.defaultsvr && (record.defaultsvrid = record.enablesvrid);
    if (record.segmentid) {
      createServerList(record.segmentid);
    }

    setIsModify(true);
    clientForm.current.show(1, record);

    // setSelectRecord(record);
  }
  // const roleDeleteFunc = async (record) => {
  //   // 取消勾选
  //   setSelectKeys([]);

  //   const deleteData = {
  //     ...defaultParams,
  //     clientid: record.clientid,
  //     clienttype: record.clienttype,
  //     exchangeid: record.exchangeid,
  //   }

  //   fetchFunc(MESSAGE_TYPE.DelTradeRight, deleteData, () => {
  //     message.success(CURD.Delete + CURD.StatusOkMsg);
  //   });
  // }
  const toClientDelete = (record) => {
    Confirm({
      content: <>{record.clientid}</>,
      onOk: () => {
        // console.log('ok', record);
        // 取消选择
        setSelectKeys([]);

        setTmpDataFunc({
          record: record,
          type: 3,
          setFunc: setClientTmpData,
          name: clientNames,
          data: clientTmpData,
          isDisabled: true,
        });
      }
    });
  }

  const createServerList = value => {
    const servers = serverMap[value];
    if (servers) {
      // 设置默认值
      clientForm.current.set({
        defaultsvrid: servers[0].serverid,
      });

      // 更改服务器列表
      setServerList(servers.map(i => ({
        label: i.servername,
        value: i.serverid,
      })));
    }
  }
  const clientFormValueChange = (changedValues, values) => {
    if ('segmentid' in changedValues) {
      const value = changedValues.segmentid;
      if (value) {
        createServerList(value);
      } else {
        clientForm.current.set({
          defaultsvrid: undefined,
        });

        setServerList([]);
      }
    }
  }

  const clientAddSubmit = (form, type) => {
    // 取消选择
    setSelectKeys([]);

    if (importUpdate) {
      importClientChange(true, form);
      return;
    }

    if (type) {
      // const idx = clientTmpData.findIndex(item => item.clientid === saveData.clientid);
      // clientTmpData[idx] = saveData;

      // setClientTmpData([...clientTmpData]);
      setTmpDataFunc({
        record: form,
        type: 2,
        setFunc: setClientTmpData,
        name: clientNames,
        data: clientTmpData,
      });
    } else {
      // 如果已存在则不做处理，弹出提示
      const isExist = dataFindIndex(clientTmpData, form, clientNames);
      if (isExist !== -1) {
        message.error('已存在交易编码');
        return;
      }

      const saveData = {
        ...form,
        type: 1,
      }

      clientTmpData.unshift(saveData);
      setClientTmpData(rebuildIndex(clientTmpData));
    }


    // 修改操作为先删除再新增
    // if (type) {
    //   // console.log(selectRecord, form);
    //   clientDeleteFunc(selectRecord);
    // }

    // fetchFunc(MESSAGE_TYPE.InsUserClient, postData, () => {
    //   message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
    //   onClientRefresh(form.userid);
    // });
  }
  // 新增、修改、删除 撤销
  const toClientCancel = record => {
    // console.log('client cancel', record);
    const clientIdx = clientTmpData.findIndex(item => item.clientid + item.exchangeid === record.clientid + record.exchangeid);

    switch (record.type) {
      case 1:
        clientTmpData.splice(clientIdx, 1);
        break;
      case 2:
        const oldClient = clientData.find(item => item.clientid + item.exchangeid === record.clientid + record.exchangeid);

        clientTmpData[clientIdx] = oldClient;
        break;
      case 3:
        clientTmpData[clientIdx].type = 0;
        clientTmpData[clientIdx].isDisabled = false;
        break;
      default:
        break;
    }

    setClientTmpData(rebuildIndex(clientTmpData));
  }

  const clientInsertFunc = (record) => {
    const postData = {
      ...defaultParams,
      userid: record.userid,
      segmentid: record.segmentid,
      defaultsvrid: record.defaultsvrid,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
    }

    // console.log('交易编码 Insert', postData);

    fetchFunc(MESSAGE_TYPE.InsUserClient, postData);
  }
  const clientDeleteFunc = (record) => {
    const deleteData = {
      ...defaultParams,
      userid: record.userid,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
    }

    // console.log('交易编码 Delete', deleteData);

    fetchFunc(MESSAGE_TYPE.DelUserClient, deleteData);
  }
  const clientRoleFunc = (record, type) => {
    // console.log('交易权限', record);
    const roleData = {
      ...defaultParams,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
      templateid: record.role.templateid,
      rightid: record.role.rightid,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdTradeRight
      : MESSAGE_TYPE.InsTradeRight, roleData);
  }

  // --------- Fronzen ---------

  const getFronzenTable = async (userId) => {
    const params = {
      ...defaultParams,
      accountid: userId,
    }

    fetchFunc(MESSAGE_TYPE.QryFrozenFunds, params, (resp) => {
      const tableData = rebuildIndex(resp);
      fronzenData = tableData;
      setFronzenTmpData([...tableData]);
    }, false);
  }
  // const onFronzenRefresh = (user) => {
  //   // console.log('Refresh');
  //   getFronzenTable(user);
  // }

  const [digitUpper, setDigitUpper] = useState('');
  const createFronzenAddForm = useMemo(() => fronzenAddForm(intl, isModify, digitUpper), [intl, isModify, digitUpper]);
  const toFronzenInsert = () => {
    currentForm.current.validate(['userid']).then(() => {
      const userId = currentForm.current.get('userid');

      setIsModify(false);
      setDigitUpper('');
      fronzenForm.current.show(0, {
        serverid: 0,
        accountid: userId,
        accounttype: DEFAULT_ACCOUNT_TYPE,
      });
    }).catch((err) => {
      message.error('请先填写用户编号');
    });
  }
  const toFronzenUpdate = (record) => {
    // console.log('fronzen update', record);
    setIsModify(true);
    // 显示大写
    if (record.frozenfunds) {
      setDigitUpper(digitUppercase(record.frozenfunds));
    } else {
      setDigitUpper('');
    }
    fronzenForm.current.show(1, record);
  }
  // const fronzenDeleteFunc = async (record) => {
  //   const deleteData = {
  //     ...defaultParams,
  //     accountid: record.accountid,
  //   }

  //   fetchFunc(MESSAGE_TYPE.DelFrozenFunds, deleteData, () => {
  //     message.success(CURD.Delete + CURD.StatusOkMsg);
  //     onFronzenRefresh(record.accountid);
  //   });
  // }
  const toFronzenDelete = (record) => {
    // console.log('fronzen delete', record);
    Confirm({
      content: <>{record.accountid}</>,
      onOk: () => {
        // console.log('ok', record);
        // fronzenDeleteFunc(record);
        // 取消选择
        setSelectKeys([]);

        setTmpDataFunc({
          record: record,
          type: 3,
          setFunc: setFronzenTmpData,
          name: fronzenNames,
          data: fronzenTmpData,
          isDisabled: true,
        });
      }
    });
  }
  const fronzenFormValueChange = (changedValues, values) => {
    if ('frozenfunds' in changedValues) {
      const value = changedValues.frozenfunds;
      if (value !== '') {
        setDigitUpper(digitUppercase(value));
      } else {
        setDigitUpper('');
      }
    }
  }
  const fronzenAddSubmit = (form, type) => {
    // const postData = {
    //   ...defaultParams,
    //   ...form,
    // }

    // fetchFunc(type
    //   ? MESSAGE_TYPE.UpdFrozenFunds
    //   : MESSAGE_TYPE.InsFrozenFunds, postData, () => {
    //     message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
    //     onFronzenRefresh(form.accountid);
    //   });
    if (type) {
      setTmpDataFunc({
        record: form,
        type: 2,
        setFunc: setFronzenTmpData,
        name: fronzenNames,
        data: fronzenTmpData,
      });
    } else {
      // 如果已存在则不做处理，弹出提示
      const isExist = fronzenTmpData.findIndex(item => item.accountid + item.accounttype + item.exchangeid === form.accountid + form.accounttype + form.exchangeid);
      if (isExist !== -1) {
        message.error('已存在资金分配');
        return;
      }

      const saveData = {
        ...form,
        type: 1,
      }

      fronzenTmpData.unshift(saveData);
      setFronzenTmpData(rebuildIndex(fronzenTmpData))
    }
  }
  const toFronzenCancel = record => {
    // console.log('fronzen cancel', record);
    const fronzenIdx = fronzenTmpData.findIndex(item => item.accountid + item.exchangeid === record.accountid + record.exchangeid);

    switch (record.type) {
      case 1:
        fronzenTmpData.splice(fronzenIdx, 1);
        break;
      case 2:
        const oldFronzen = fronzenData.find(item => item.accountid + item.exchangeid === record.accountid + record.exchangeid);

        fronzenTmpData[fronzenIdx] = oldFronzen;
        break;
      case 3:
        fronzenTmpData[fronzenIdx].type = 0;
        fronzenTmpData[fronzenIdx].isDisabled = false;
        break;
      default:
        break;
    }

    setFronzenTmpData(rebuildIndex(fronzenTmpData));
  }

  const fronzenInsertFunc = (record, type) => {
    // console.log('资金分配 Insert', record);
    const postData = {
      ...defaultParams,
      accountid: record.accountid,
      accounttype: record.accounttype,
      exchangeid: record.exchangeid,
      frozenfunds: record.frozenfunds,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdFrozenFunds
      : MESSAGE_TYPE.InsFrozenFunds, postData);

  }
  const fronzenDeleteFunc = (record) => {
    // console.log('资金分配 Delete', record);
    const deleteData = {
      ...defaultParams,
      accountid: record.accountid,
      accounttype: record.accounttype,
      exchangeid: record.exchangeid,
    }

    fetchFunc(MESSAGE_TYPE.DelFrozenFunds, deleteData);
  }

  const operations = <Button type="primary" ghost onClick={[toClientInsert, toFronzenInsert][tabKey]}>新增</Button>;

  const tabGroups = [{
    label: $t('app.menu.db.userclient'),
    key: '0',
    children: (<>
      <TiTable
        columns={ClientColumns}
        dataSource={clientTmpData}
        isLoading={isLoading}
        hasToolBar={false}
        // checkable={false}
        rowclickable
        footer={() => $t('app.options.db.userinfo.clientid.tip') }
        selectionType="radio"
        selectKeys={selectKeys}
        setSelectKeys={(keys) => {
          setSelectKeys(keys);
          roleTrans.current?.submit();
        }}
      />

      {/* 权限管理 */}
      {selectKeys.length > 0 && <RoleTransfer
        ref={roleTrans}
        clientData={clientTmpData[selectKeys[0] - 1]}
        defaultParams={defaultParams}
        setRoleFunc={(roleData) => {
          clientTmpData[selectKeys[0] - 1] = roleData;
          setClientTmpData([...clientTmpData]);
        }}
      />}
    </>)
  }, {
    label: $t('app.menu.db.frozenfunds'),
    key: '1',
    children: (
      <TiTable
        columns={FronzenColumns}
        dataSource={fronzenTmpData}
        isLoading={isLoading}
        hasToolBar={false}
        checkable={false}
      />
    )
  }];

  return (<>
    <Tabs
      className="ti-modal-tab"
      defaultActiveKey={tabKey}
      tabBarExtraContent={operations}
      onChange={(key) => {
        setTabKey(key);
      }}
      items={tabGroups}
    />

    <ModalForm
      ref={clientForm}
      onOk={clientAddSubmit}
      formData={createClientAddForm}
      onValuesChange={clientFormValueChange}
    />

    <ModalForm
      ref={fronzenForm}
      onOk={fronzenAddSubmit}
      formData={createFronzenAddForm}
      onValuesChange={fronzenFormValueChange}
    />
  </>);

  // return (<>
  //   <Row justify="space-between" align="middle" gutter={16}>
  //     <Col flex="auto">
  //       <Divider orientation="left" plain>交易权限</Divider>
  //     </Col>
  //     <Col>
  //       <Button type="primary" ghost>新增</Button>
  //     </Col>
  //   </Row>
  // </>);
}

export default forwardRef(Client);
