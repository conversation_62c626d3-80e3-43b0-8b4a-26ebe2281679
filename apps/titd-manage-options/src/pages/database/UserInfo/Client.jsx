import { useRef, useState, useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import { useNavigate } from 'react-router';
import { Tabs, Button, Space, Switch, Typography, App } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';

import {
  TiTable,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { clientAddForm, frozenAddForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  currency,
  getList,
  errorResp,
} from '@titd/publics/utils';

import RoleTransfer from './RoleTransfer';

const { Text } = Typography;
const { IndexColumn, TableTitle, TableBadge, TableTag, TableBadgeDot, NegaNumber } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink, cancelLink } = links;
const { clientTypeDict, exchangeDict, exchangeAllDict, alloctTypeDict, assignTypeDict, tagNormalColors } = dicts;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { getFloat, digitUppercase } = currency;
const { rebuildIndex } = getList;

const defaultMainType = 1;
const defaultExchangeType = 1;
const defaultDisable = new Array(exchangeDict.length).fill(false);

// 查找在数组中的序号
const dataFindIndex = (data, record, name) => {
  return data.findIndex(item => item[name[0]] + item[name[1]] === record[name[0]] + record[name[1]]);
}

// 全局缓存，供修改恢复用
let clientData = [];
let frozenData = [];

const Client = (props, ref) => {
  const {
    currentForm,
    user,
    segmentList,
    serverMap
  } = props;

  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const navigate = useNavigate();
  // const [selectRecord, setSelectRecord] = useState(null);
  const [tabKey, setTabKey] = useState('0');
  const [importUpdate, setImportUpdate] = useState(false);

  const roleTrans = useRef(null);
  const clientForm = useRef(null);
  const clientNames = ['clientid', 'exchangeid'];
  const [clientTmpData, setClientTmpData] = useState([]);
  const frozenForm = useRef(null);
  const frozenNames = ['accountid', 'exchangeid'];
  const [frozenTmpData, setFrozenTmpData] = useState([]);
  const [isModify, setIsModify] = useState(false);
  const [oldUserId, setOldUserId] = useState(null);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  // type: 1新增,2修改,3删除, 4启用, 5关闭
  const actionStatus = useMemo(() => {
    return ['', {
      title: $t('app.general.insert'),
      color: '#87d068',
    }, {
      title: $t('app.general.update'),
      color: '#108ee9',
    }, {
      title: $t('app.general.delete'),
      color: '#f50',
    }, {
      title: $t('app.general.open'),
      color: '#2db7f5',
    }, {
      title: $t('app.general.close'),
      color: '#ccc'
    }];
  }, [$t]);

  useImperativeHandle(ref, () => ({
    reset: () => {
      setSelectKeys([]);
    },
    submit: () => {
      // 最后一步权限保存，统一处理
      roleTrans.current?.submit();

      // console.log(clientTmpData, frozenTmpData);

      // 交易编码提交
      clientTmpData.forEach(item => {
        switch (item.type) {
          case 1:
          case 4:
            clientInsertFunc(item);
            break;
          case 2: {
            const idx = dataFindIndex(clientData, item, clientNames);
            // 修改，先删除再新增
            clientDeleteFunc(clientData[idx]);
            clientInsertFunc(item);
            break;
          }
          case 3:
          case 5:
            clientDeleteFunc(item);
            break;
          default:
            break;
        }

        // 权限修改提交
        if (item.role) {
          clientRoleFunc(item, item.role.modify);
        }
      });

      // 资金分配提交
      frozenTmpData.forEach(item => {
        switch (item.type) {
          case 1:
            frozenInsertFunc(item);
            break;
          case 2:
            frozenInsertFunc(item, 1);
            break;
          case 3:
            frozenDeleteFunc(item);
            break;
          default:
            break;
        }
      })
    },
    getValues: () => {
      // 最后一步权限保存，统一处理
      roleTrans.current?.setRole();

      // console.log(clientTmpData, frozenTmpData);
      const clientData = clientTmpData.map(item => {
        if (item.isDisabled) {
          return null;
        }

        return {
          clientid: item.clientid,
          segmentid: item.segmentid,
          defaultsvrid: item.defaultsvrid,
          clienttype: item.clienttype,
          bizpbu: item.bizpbu,
          salesnumb: item.salesnumb,
          usertype: 0, // 普通用户
          exchangeid: item.exchangeid,
          templateid: item.templateid,
          quotetemplateid: item.quotetemplateid,
          rightid: item.rightid,
        }
      }).filter(i => i);

      const frozenData = frozenTmpData.map(item => ({
        accountid: item.accountid,
        allocttype: item.allocttype,
        scalemode1: item.scalemode1,
        scale1: item.scale1,
        scalemode2: item.scalemode2,
        scale2: item.scale2
      }));

      return {
        clientData: clientData,
        frozenData: frozenData && frozenData[0],
      }
    },
    // delete: (record) => {
    //   console.log('delete', record);
    // }
  }));

  // 交易编码共同方法
  const setTmpDataFunc = ({ record, type, setFunc, name, data, isDisabled = false }) => {
    const idx = dataFindIndex(data, record, name);

    if (idx > -1) {
      const newRecord = data[idx];

      data[idx] = {
        ...newRecord,
        ...record,
        type: newRecord?.type || type
      };

      if (isDisabled) {
        data[idx].isDisabled = true;
      }

      if ([1, 2, 4].indexOf(type) > -1) {
        setSelectKeys([data[idx].id]);
      }

      setFunc([...data]);
    }
  }

  const fetchFunc = async (type, params, func, msg = true) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else if (msg) {
      errorResp(resp.data, navigate, message);
    }
  }

  const ClientColumns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.segmentid'),
    dataIndex: 'segmentid',
    render: (text, record) => (
      <Space align="center">
        {record.type ? TableBadge(actionStatus[record.type].title, actionStatus[record.type].color) : null}
        {text}
      </Space>
    ),
  }, {
    title: $t('app.options.defaultsvrid'),
    dataIndex: 'defaultsvrid',
  }, {
  //   title: TableTitle(
  //     $t('app.options.serverid'),
  //     $t('app.options.serverid.long')
  //   ),
  //   dataIndex: 'serverid',
  //   render: (text, record) => (<Space align="center">
  //     {record.type ? TableBadge(actionStatus[record.type].title, actionStatus[record.type].color) : null}
  //     {serverList.find(item => item.value === text)?.label || text}
  //   </Space>),
  // }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clienttype'),
    dataIndex: 'clienttype',
    render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.bizpbu'),
    dataIndex: 'bizpbu',
  }, {
    title: $t('app.options.salesnumb'),
    dataIndex: 'salesnumb',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 180,
    onCell: () => {
      return { onClick: e => e.stopPropagation() }
    },
    render: (_, record) => record.isSystem ? (
      record.type !== 2 ? <Space>
        <Switch
          checkedChildren={<CheckOutlined />}
          unCheckedChildren={<CloseOutlined />}
          // onChange={value => importClientChange(value, record)}
          onChange={value => record.defaultsvrid ? importClientChange(value, record) : importClientUpdate(value, record)}
          checked={!record.isDisabled}
        />
        {!record.isDisabled && <ActionLinks links={[
          updateLink(toClientUpdate, record),
        ]} />}
      </Space> : <ActionLinks links={[
        cancelLink(toClientCancel, record),
      ]} />
    ) : (
      <ActionLinks links={record.type ? [
        cancelLink(toClientCancel, record),
      ] : [
        updateLink(toClientUpdate, record),
        deleteLink(toClientDelete, record),
      ]} />
    ),
  }];

  const FrozenColumns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    render: (text, record) => (<Space align="center">
      {record.type ? TableBadge(actionStatus[record.type].title, actionStatus[record.type].color) : null}
      <Text strong>{text}</Text>
    </Space>),
  }, {
    title: $t('app.options.db.funds.allocttype'),
    dataIndex: 'allocttype',
    render: text => TableTag(text, alloctTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.funds.scalemode1'),
    dataIndex: 'scalemode1',
    render: text => TableBadgeDot(text, assignTypeDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.db.funds.scale1'),
      $t('app.options.db.funds.scalemode1.tip')
    ),
    dataIndex: 'scale1',
    align: 'right',
    render: (text, record) => formatFunds(text, record.scalemode1),
  }, {
    title: $t('app.options.db.funds.scalemode2'),
    dataIndex: 'scalemode2',
    render: text => TableBadgeDot(text, assignTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.funds.scale2'),
    dataIndex: 'exchangeScale',
    // render: text => text.join(', ')
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={record.type ? [
        cancelLink(toFrozenCancel, record),
      ] : [
        updateLink(toFrozenUpdate, record),
        deleteLink(toFrozenDelete, record),
      ]} />
    )
  }];

  useEffect(() => {
    const userId = user?.userid;
    if (userId && userId !== oldUserId) {
      getClientTable(userId, user.clients);
      getFrozenTable(userId);

      setOldUserId(userId);
    }

    // return () => {
    //   cancel();
    // }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  // useEffect(() => {
  //   if (selectKeys.length > 0) {
  //     setCheckClient(clientTmpData[selectKeys[0] - 1]);
  //   }
  // }, [selectKeys]);

  // --------- Client ---------
  const getClientTable = async (userId, clients) => {
    // const params = {
    //   ...defaultParams,
    //   usertype: 0,
    //   userid: userId,
    // }

    // let combineData = clients ? clients.map(item => ({
    //   ...item,
    //   defaultsvrid: item.defaultsvr ? item.enablesvrid : '',
    // })) : [];
    let combineData = [...clients];

    // 获取导入的交易编码
    await fetchFunc(MESSAGE_TYPE.QryImportClient, {
      ...defaultParams,
      accountid: userId,
    }, importResp => {
      // 合并导入和新增的交易编码
      if (importResp.length > 0) {
        const importClient = importResp.map(item => {
          let clientItem = {
            ...item,
            userid: userId,
            isSystem: true,
            isDisabled: true,
          };

          item.serverid && (clientItem.defaultsvrid = item.serverid);

          const idx = dataFindIndex(combineData, item, clientNames);
          if (idx > -1) {
            clientItem = {
              ...clientItem,
              ...combineData[idx],
              isDisabled: false,
              isActive: true,
            }
            combineData.splice(idx, 1);
          }

          return clientItem;
        });

        combineData = combineData.concat(importClient);
      }

      const tableData = rebuildIndex(combineData);
      clientData = tableData;
      setClientTmpData([...tableData]);

      // 默认选择第一个可选项
      if (tableData.length > 0) {
        const firstSelected = tableData.find(i => !i.isDisabled);
        if (firstSelected) {
          setSelectKeys([firstSelected.id]);
        }
      } else {
        setSelectKeys([]);
      }
    }, false);

    // fetchFunc(MESSAGE_TYPE.QryUserClient, params, async (resp) => {
    //   let combineData = resp.map(item => ({
    //     ...item,
    //     defaultsvrid: item.defaultsvr ? item.enablesvrid : '',
    //   }));
    // }, false);
  }
  const getHasRights = async client => {
    // console.log(client);
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryTradeRight), {
      ...defaultParams,
      clientid: client.clientid,
      clienttype: client.clienttype,
      exchangeid: client.exchangeid,
    });

    const { data: respD } = resp;
    if (respD?.length > 0) {
      const respData = respD[0];

      return {
        ...client,
        templateid: respData.templateid,
        quotetemplateid: respData.quotetemplateid,
        rightid: respData.rightid,
      }
    }

    return client;
  }

  // const onClientRefresh = (user) => {
  //   // console.log('Refresh');
  //   getClientTable(user);
  // }
  const importClientChange = async (value, record) => {
    const idx = dataFindIndex(clientTmpData, record, clientNames);

    // 设置类型
    let clientType = value ? 4 : 0;
    if (value && !clientTmpData[idx].isActive) {
      clientType = 4;
    } else if (!value && clientTmpData[idx].isActive) {
      clientType = 5;
    } else {
      clientType = 0;
    }

    // 添加权限
    if (clientType === 4) {
      clientTmpData[idx] = await getHasRights(clientTmpData[idx]);
    }

    clientTmpData[idx] = {
      ...clientTmpData[idx],
      isDisabled: !value,
      type: clientType,
      bizpbu: record.bizpbu,
      salesnumb: record.salesnumb,
      segmentid: record.segmentid,
      defaultsvrid: record.defaultsvrid,
    }

    // 未提交清除权限
    // if (!value) {
    //   delete clientTmpData[idx].templateid;
    //   delete clientTmpData[idx].rightid;
    // }

    setClientTmpData([...clientTmpData]);
    setImportUpdate(false);

    if (value) {
      setSelectKeys([clientTmpData[idx].id]);
    } else {
      if (selectKeys[0] === clientTmpData[idx].id) {
        setSelectKeys([]);
      }
    }
  }
  const importClientUpdate = (value, record) => {
    if (value) {
      setImportUpdate(true);
      toClientUpdate(record);
    } else {
      importClientChange(value, record);
    }
  }

  const [serverList, setServerList] = useState([]);

  const createClientAddForm = useMemo(() => clientAddForm($t, isModify, segmentList, serverList), [$t, isModify, segmentList, serverList]);

  const toClientInsert = () => {
    // const userId = currentForm.current.get('userid');

    // if (userId) {
    //   clientForm.current.show(0, {
    //     serverid: 0,
    //     userid: userId,
    //   });
    // } else {
    //   message.error('请先填写用户ID');
    // }

    // 保存未保存的权限
    roleTrans.current?.setRole();

    currentForm.current.validate(['userid']).then(() => {
      const userId = currentForm.current.get('userid');

      setIsModify(false);
      clientForm.current.show(0, {
        userid: userId,
        clienttype: 1,
      });
    }).catch(() => {
      message.error('请先填写用户编号');
    });
  }
  const toClientUpdate = record => {
    record.defaultsvr && (record.defaultsvrid = record.enablesvrid);
    if (record.segmentid) {
      createServerList(record.segmentid);
    }

    setIsModify(true);
    clientForm.current.show(1, record);

    // setSelectRecord(record);
  }
  // const roleDeleteFunc = async (record) => {
  //   // 取消勾选
  //   setSelectKeys([]);

  //   const deleteData = {
  //     ...defaultParams,
  //     clientid: record.clientid,
  //     clienttype: record.clienttype,
  //     exchangeid: record.exchangeid,
  //   }

  //   fetchFunc(MESSAGE_TYPE.DelTradeRight, deleteData, () => {
  //     message.success(CURD.Delete + CURD.StatusOkMsg);
  //   });
  // }
  const toClientDelete = record => {
    Confirm({
      modal,
      content: <>{record.clientid}</>,
      onOk: () => {
        // console.log('ok', record);
        // 取消选择
        setSelectKeys([]);

        setTmpDataFunc({
          record: record,
          type: 3,
          setFunc: setClientTmpData,
          name: clientNames,
          data: clientTmpData,
          isDisabled: true,
        });
      }
    });
  }

  const createServerList = value => {
    const servers = serverMap[value];
    if (servers) {
      // 设置默认值
      clientForm.current.set({
        defaultsvrid: servers[0].serverid,
      });

      // 更改服务器列表
      setServerList(servers.map(i => ({
        label: i.servername,
        value: i.serverid,
      })));
    }
  }
  const clientFormValueChange = changedValues => {
    if ('segmentid' in changedValues) {
      const value = changedValues.segmentid;
      if (value) {
        createServerList(value);
      } else {
        clientForm.current.set({
          defaultsvrid: undefined,
        });

        setServerList([]);
      }
    }
  }

  const clientAddSubmit = async (form, type) => {
    // 取消选择
    setSelectKeys([]);

    if (importUpdate) {
      importClientChange(true, form);
      return;
    }

    if (type) {
      // const idx = clientTmpData.findIndex(item => item.clientid === saveData.clientid);
      // clientTmpData[idx] = saveData;

      // setClientTmpData([...clientTmpData]);
      setTmpDataFunc({
        record: form,
        type: 2,
        setFunc: setClientTmpData,
        name: clientNames,
        data: clientTmpData,
      });
    } else {
      // 如果已存在则不做处理，弹出提示
      const isExist = dataFindIndex(clientTmpData, form, clientNames);
      if (isExist !== -1) {
        message.error('已存在交易编码');
        return;
      }

      // 查询权限
      const dataHasRight = await getHasRights(form);

      const saveData = {
        ...dataHasRight,
        type: 1,
      }

      clientTmpData.unshift(saveData);
      const rebuildData = rebuildIndex(clientTmpData);
      setClientTmpData(rebuildData);

      setSelectKeys([rebuildData[0].id]);
    }

    // 修改操作为先删除再新增
    // if (type) {
    //   // console.log(selectRecord, form);
    //   clientDeleteFunc(selectRecord);
    // }

    // fetchFunc(MESSAGE_TYPE.InsUserClient, postData, () => {
    //   message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
    //   onClientRefresh(form.userid);
    // });
  }
  // 新增、修改、删除 撤销
  const toClientCancel = record => {
    // console.log('client cancel', record);
    const clientIdx = clientTmpData.findIndex(item => item.clientid + item.exchangeid === record.clientid + record.exchangeid);

    switch (record.type) {
      case 1:
        clientTmpData.splice(clientIdx, 1);
        setSelectKeys([]);
        break;
      case 2: {
        const oldClient = clientData.find(item => item.clientid + item.exchangeid === record.clientid + record.exchangeid);

        clientTmpData[clientIdx] = oldClient;
        break;
      }
      case 3:
        clientTmpData[clientIdx].type = 0;
        clientTmpData[clientIdx].isDisabled = false;
        break;
      default:
        break;
    }

    setClientTmpData(rebuildIndex(clientTmpData));
  }

  const clientInsertFunc = record => {
    const postData = {
      ...defaultParams,
      userid: record.userid,
      clientid: record.clientid,
      clienttype: record.clienttype,
      bizpbu: record.bizpbu,
      salesnumb: record.salesnumb,
      exchangeid: record.exchangeid,
      segmentid: record.segmentid,
      defaultsvrid: record.defaultsvrid,
    }

    // console.log('交易编码 Insert', postData);

    fetchFunc(MESSAGE_TYPE.InsUserClient, postData);
  }
  const clientDeleteFunc = record => {
    const deleteData = {
      ...defaultParams,
      userid: record.userid,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
    }

    // console.log('交易编码 Delete', deleteData);

    fetchFunc(MESSAGE_TYPE.DelUserClient, deleteData);
  }
  const clientRoleFunc = (record, type) => {
    // console.log('交易权限', record);
    const roleData = {
      ...defaultParams,
      clientid: record.clientid,
      clienttype: record.clienttype,
      exchangeid: record.exchangeid,
      templateid: record.role.templateid,
      quotetemplateid: record.role.quotetemplateid,
      rightid: record.role.rightid,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdTradeRight
      : MESSAGE_TYPE.InsTradeRight, roleData);
  }

  // --------- Frozen ---------

  const getFrozenTable = async userId => {
    const params = {
      ...defaultParams,
      accountid: userId,
    }

    fetchFunc(MESSAGE_TYPE.QryAccountAlloct, params, (resp) => {
      const tableData = rebuildIndex(resp).map(item => ({
        ...item,
        exchangeScale: formatExgScale(item.scale2, item.scalemode2),
      }));
      frozenData = tableData;
      setFrozenTmpData([...tableData]);
    }, false);
  }
  const formatExgScale = (scale, mode) => {
    if (scale && scale.length > 0) {
      return scale.map((item, idx) => {
        const exValue = item.isRemaining ? (<Text strong>剩余全部</Text>) : formatFunds(item.alloction, mode);

        return (
          <Text key={idx} style={{ display: 'inline-block', minWidth: '150px' }}>
            {`${exchangeDict[item.exchangeid] || item.exchangeid}: `}
            {exValue}
          </Text>
        );
      });
    } else {
      return [];
    }
  };
  const formatFunds = (text, mode) => mode === 1 ? (<>
    <Text strong>{getFloat(text, 2)}</Text>
    <Text type="secondary"> %</Text>
  </>) : (<>
    <Text type="secondary">¥ </Text>
    <Text strong>{NegaNumber(text, 2)}</Text>
  </>);
  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAccountAlloct), {
      ...defaultParams,
      accountid: record.accountid,
    });

    if (resp && resp.length > 0) {
      return resp[0];
    }

    return null;
  }
  // const onFrozenRefresh = (user) => {
  //   // console.log('Refresh');
  //   getFrozenTable(user);
  // }

  const [maintype, setMainType] = useState(defaultMainType);
  const [exchangetype, setExchangeType] = useState(defaultExchangeType);
  const [digitUpper, setDigitUpper] = useState('');
  const [disableMap, setDisableMap] = useState(defaultDisable);

  const createFrozenAddForm = useMemo(() => frozenAddForm(frozenForm, $t, isModify, digitUpper, maintype, exchangetype, disableMap), [$t, isModify, digitUpper, maintype, exchangetype, disableMap]);
  const toFrozenInsert = () => {
    currentForm.current.validate(['userid']).then(() => {
      const userId = currentForm.current.get('userid');

      setIsModify(false);
      setMainType(defaultMainType);
      setExchangeType(defaultExchangeType);
      setDigitUpper('');

      frozenForm.current.show(0, {
        serverid: 0,
        accountid: userId,
      });
    }).catch(() => {
      message.error('请先填写用户编号');
    });
  }
  const toFrozenUpdate = async record => {
    // console.log('frozen update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      setIsModify(true);
      setMainType(newRecord.scalemode1);
      setExchangeType(newRecord.scalemode2);

      // 显示大写
      if (newRecord.scalemode1 === 2 && newRecord.scale1) {
        setDigitUpper(digitUppercase(newRecord.scale1));
      } else {
        setDigitUpper('');
      }
      // 次席分配
      if (newRecord.scale2?.length > 0) {
        newRecord.scale2.forEach(item => {
          const exIdx = item.exchangeid;
          newRecord['isRemaining' + exIdx] = item.isRemaining;
          !item.isRemaining && (newRecord['alloction' + exIdx] = getFloat(item.alloction, 2));
          disableMap[exIdx] = item.isRemaining;
        });
        setDisableMap([...disableMap]);
      }
      frozenForm.current.show(1, newRecord);

      // 更新本条数据
      // const newData = updateRowData(frozenTmpData, {
      //   ...newRecord,
      //   id: record.id,
      //   exchangeScale: formatExgScale(newRecord.scale2, newRecord.scalemode2),
      // });
      // setDataSource(newData);
    } else {
      message.error($t('app.general.deleted'));
    }
  }
  // const frozenDeleteFunc = async (record) => {
  //   const deleteData = {
  //     ...defaultParams,
  //     accountid: record.accountid,
  //   }

  //   fetchFunc(MESSAGE_TYPE.DelFrozenFunds, deleteData, () => {
  //     message.success(CURD.Delete + CURD.StatusOkMsg);
  //     onFrozenRefresh(record.accountid);
  //   });
  // }
  const toFrozenDelete = record => {
    // console.log('frozen delete', record);
    Confirm({
      modal,
      content: <>{record.accountid}</>,
      onOk: () => {
        // console.log('ok', record);
        // frozenDeleteFunc(record);
        // 取消选择
        setSelectKeys([]);

        setTmpDataFunc({
          record: record,
          type: 3,
          setFunc: setFrozenTmpData,
          name: frozenNames,
          data: frozenTmpData,
          isDisabled: true,
        });
      }
    });
  }
  const frozenFormValueChange = (changedValues, values) => {
    if ('scalemode1' in changedValues) {
      const value = changedValues.scalemode1;
      setMainType(value);

      frozenForm.current.set({
        scale1: value === 1 ? 100 : null
      });
      setDigitUpper('');
    }
    if ('scalemode2' in changedValues) {
      const value = changedValues.scalemode2;
      setExchangeType(value);

      exchangeDict.forEach((item, idx) => {
        if (item !== '' && item !== '-') {
          frozenForm.current.set({
            ['alloction' + idx]: value === 1 ? 0 : null,
            ['isRemaining' + idx]: false,
          });
          disableMap[idx] = false;
        }
      });
      setDisableMap([...disableMap]);
    }
    if ('scale1' in changedValues) {
      const value = changedValues.scale1;
      if (value !== '') {
        setDigitUpper(digitUppercase(value));
      } else {
        setDigitUpper('');
      }
    }

    exchangeDict.forEach((item, idx) => {
      if ((item !== '' && item !== '-') && `isRemaining${idx}` in changedValues) {
        const value = changedValues['isRemaining' + idx];
        if (value) {
          const isSwitched = exchangeDict.findIndex((_, i) => values['isRemaining' + i] && idx !== i);
          if (isSwitched > -1) {
            message.error($t('app.options.db.funds.scalemode2.tip'));
            frozenForm.current.set({
              ['isRemaining' + idx]: false
            });
            return false;
          }

          frozenForm.current.set({
            ['alloction' + idx]: values.scalemode2 === 1 ? 0 : null
          });
        }
        disableMap[idx] = value;
        setDisableMap([...disableMap]);
      }
    });
  }
  const frozenAddSubmit = (form, type) => {
    // const postData = {
    //   ...defaultParams,
    //   ...form,
    // }

    // fetchFunc(type
    //   ? MESSAGE_TYPE.UpdFrozenFunds
    //   : MESSAGE_TYPE.InsFrozenFunds, postData, () => {
    //     message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
    //     onFrozenRefresh(form.accountid);
    //   });
    const exchangeScaleGroup = exchangeDict.map((item, idx) => {
      if (item === '' || item === '-') {
        return null;
      }

      const isRemain = form['isRemaining' + idx];

      const scale = {
        exchangeid: idx,
        isRemaining: isRemain ? 1 : 0,
        alloction: isRemain ? 0 : form['alloction' + idx],
      }

      delete form['isRemaining' + idx];
      delete form['alloction' + idx];

      return scale;
    }).filter(i => i);

    const saveForm = {
      ...form,
      scale2: exchangeScaleGroup,
      exchangeScale: formatExgScale(exchangeScaleGroup, form.scalemode2),
    }

    if (type) {
      setTmpDataFunc({
        record: saveForm,
        type: 2,
        setFunc: setFrozenTmpData,
        name: frozenNames,
        data: frozenTmpData,
      });
    } else {
      // 如果已存在则不做处理，弹出提示
      const isExist = frozenTmpData.findIndex(item => item.accountid === form.accountid);
      if (isExist !== -1) {
        message.error('已存在资金账户');
        return;
      }

      const saveData = {
        ...saveForm,
        type: 1,
      }

      frozenTmpData.unshift(saveData);
      setFrozenTmpData(rebuildIndex(frozenTmpData))
    }
  }
  const toFrozenCancel = record => {
    // console.log('frozen cancel', record);
    const frozenIdx = frozenTmpData.findIndex(item => item.accountid === record.accountid);

    switch (record.type) {
      case 1:
        frozenTmpData.splice(frozenIdx, 1);
        break;
      case 2: {
        const oldFrozen = frozenData.find(item => item.accountid === record.accountid);

        frozenTmpData[frozenIdx] = oldFrozen;
        break;
      }
      case 3:
        frozenTmpData[frozenIdx].type = 0;
        frozenTmpData[frozenIdx].isDisabled = false;
        break;
      default:
        break;
    }

    setFrozenTmpData(rebuildIndex(frozenTmpData));
  }

  const frozenInsertFunc = (record, type) => {
    // console.log('资金分配 Insert', record);
    const postData = {
      ...defaultParams,
      accountid: record.accountid,
      allocttype: record.allocttype,
      scalemode1: record.scalemode1,
      scale1: record.scale1,
      scalemode2: record.scalemode2,
      scale2: record.scale2,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdAccountAlloct
      : MESSAGE_TYPE.AddAccountAlloct, postData);

  }
  const frozenDeleteFunc = record => {
    // console.log('资金分配 Delete', record);
    const deleteData = {
      ...defaultParams,
      accountid: record.accountid,
    }

    fetchFunc(MESSAGE_TYPE.DelAccountAlloct, deleteData);
  }

  const operations = <Button type="primary" ghost onClick={[toClientInsert, toFrozenInsert][tabKey]}>新增</Button>;

  const tabGroups = [{
    label: $t('app.menu.db.userclient'),
    key: '0',
    children: (<>
      <TiTable
        columns={ClientColumns}
        dataSource={clientTmpData}
        // isLoading={isLoading}
        hasToolBar={false}
        // checkable={false}
        rowClickable
        footer={() => $t('app.options.db.userinfo.clientid.tip') }
        selectionType="radio"
        selectKeys={selectKeys}
        setSelectKeys={(keys) => {
          setSelectKeys(keys);
          // roleTrans.current?.submit();
          roleTrans.current?.setRole();
        }}
      />

      {/* 权限管理 */}
      {selectKeys.length > 0 && <RoleTransfer
        ref={roleTrans}
        clientData={clientTmpData[selectKeys[0] - 1]}
        defaultParams={defaultParams}
        setRoleFunc={(roleData) => {
          clientTmpData[selectKeys[0] - 1] = roleData;
          setClientTmpData([...clientTmpData]);
        }}
      />}
    </>)
  }, {
    label: $t('app.menu.db.assignfunds'),
    key: '1',
    children: (
      <TiTable
        columns={FrozenColumns}
        dataSource={frozenTmpData}
        // isLoading={isLoading}
        hasToolBar={false}
        checkable={false}
      />
    )
  }];

  return (<>
    <Tabs
      className="ti-modal-tab"
      defaultActiveKey={tabKey}
      tabBarExtraContent={operations}
      onChange={(key) => {
        setTabKey(key);
      }}
      items={tabGroups}
    />

    <ModalForm
      ref={clientForm}
      onOk={clientAddSubmit}
      formData={createClientAddForm}
      onValuesChange={clientFormValueChange}
    />

    <ModalForm
      ref={frozenForm}
      onOk={frozenAddSubmit}
      formData={createFrozenAddForm}
      initValues={{
        allocttype: 1,
        scalemode1: defaultMainType,
        scale1: 100,
        scalemode2: defaultExchangeType,
      }}
      onValuesChange={frozenFormValueChange}
    />
  </>);

  // return (<>
  //   <Row justify="space-between" align="middle" gutter={16}>
  //     <Col flex="auto">
  //       <Divider orientation="left" plain>交易权限</Divider>
  //     </Col>
  //     <Col>
  //       <Button type="primary" ghost>新增</Button>
  //     </Col>
  //   </Row>
  // </>);
}

export default forwardRef(Client);
