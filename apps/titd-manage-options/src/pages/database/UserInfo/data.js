// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { consts, dicts, formOptions, iconDom } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { DEFAULT_CONFIGS } = consts;
const { userStatusDict, clientTypeDict, exchangeDict, alloctTypeDict, assignTypeDict } = dicts;
const { selectOptions } = formOptions;
const { keyIcon, copyIcon, hideIcon, showIcon } = iconDom;

export const searchForm = ($t, userOptions) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.userid'),
  fieldProps: {
    options: userOptions
  }
}, {
  valueType: 'input',
  name: 'name',
  label: $t('app.options.db.userinfo.name'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.options.db.userinfo.status'),
  fieldProps: {
    options: selectOptions(userStatusDict),
  }
}];

export const addForm = ($t, isModify, userChange, appFocus, appOptions) => [{
  valueType: 'hidden',
  name: 'usertype',
  label: $t('app.options.db.userinfo.usertype'),
  noStyle: true
}, {
  valueType: 'input',
  name: 'userid',
  label: $t('app.options.userid'),
  rules: !isModify ? [
    { required: true },
    { min: 3 },
    { max: 15 },
    { pattern: new RegExp(/^[a-z0-9_]*$/, 'g'), message: $t('app.options.userid.msg') },
    { validator: userChange }
  ] : null,
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'password',
  name: 'password',
  label: $t('app.auth.password'),
  rules: [{ required: !isModify }],
  fieldProps: {
    disabled: isModify,
  }
}, {
  valueType: 'input',
  name: 'name',
  label: $t('app.options.db.userinfo.name'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.options.db.userinfo.status'),
  fieldProps: {
    allowClear: false,
    options: selectOptions(userStatusDict),
  }
}, {
//   valueType: 'select',
//   name: 'usertype',
//   label: '用户类型',
//   fieldProps: {
//     allowClear: false,
//     options: selectOptions(userTypeDict),
//   }
// }, {
  valueType: 'number',
  name: 'tradeflow',
  label: $t('app.options.db.userinfo.tradeflow'),
}, {
  valueType: 'number',
  name: 'logincount',
  label: $t('app.options.db.userinfo.logincount'),
}, {
  valueType: 'number',
  name: 'loginsuccess',
  label: TableTitle(
    $t('app.options.db.userinfo.loginsuccess'),
    $t('app.options.db.userinfo.loginsuccess.long')
  ),
}, {
  valueType: 'number',
  name: 'loginfailed',
  label: TableTitle(
    $t('app.options.db.userinfo.loginfailed'),
    $t('app.options.db.userinfo.loginfailed.long')
  ),
}, {
  valueType: 'number',
  name: 'maxerrordernum',
  label: $t('app.options.mem.userinfo.cancelmax'),
}, {
  valueType: 'select',
  name: 'appid',
  label: $t('app.menu.db.appid'),
  rules: [{ required: true }],
  fieldProps: {
    mode: 'multiple',
    maxTagCount: 'responsive',
    onFocus: appFocus,
    options: appOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  },
  span: 1,
// }, {
//   valueType: 'number',
//   name: 'minlocalid',
//   label: $t('app.options.db.userinfo.minlocalid')
// }, {
//   valueType: 'number',
//   name: 'maxlocalid',
//   label: $t('app.options.db.userinfo.maxlocalid')
}];

export const clientAddForm = ($t, isModify, segmentList, serverList) => [{
  valueType: 'hidden',
  name: 'userid',
  label: $t('app.options.userid'),
  noStyle: true
}, {
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  rules: [
    { required: true },
    { min: 5 },
    { max: 10 },
  ],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'select',
  name: 'clienttype',
  label: $t('app.options.clienttype'),
  fieldProps: {
    disabled: isModify,
    options: selectOptions(clientTypeDict)
  },
  rules: [{ required: true }]
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict)
  },
  rules: [{ required: true }]
}, {
  valueType: 'input',
  name: 'bizpbu',
  label: $t('app.options.bizpbu'),
  rules: [{ required: DEFAULT_CONFIGS.PUB_REQUIRED }]
}, {
  valueType: 'input',
  name: 'salesnumb',
  label: $t('app.options.salesnumb'),
  rules: [{ required: DEFAULT_CONFIGS.PUB_REQUIRED }]
}, {
//   valueType: 'select',
//   name: 'serverid',
//   label: TableTitle(
//     $t('app.options.serverid'),
//     $t('app.options.serverid.long')
//   ),
//   // tooltip: '如果为0，则所有服务均可交易',
//   rules: [{ required: true }],
//   fieldProps: {
//     options: serverList
//   }
// }, {
  valueType: 'select',
  name: 'segmentid',
  label: $t('app.options.segmentid'),
  rules: [{ required: true }],
  fieldProps: {
    options: segmentList
  }
}, {
  valueType: 'select',
  name: 'defaultsvrid',
  label: $t('app.options.defaultsvrid'),
  rules: [{ required: true }],
  fieldProps: {
    options: serverList
  }
}];

const scaleSliderOptions = disabled => ({
  min: 0,
  max: 100,
  step: 1,
  marks: {
    0: '0',
    100: '100%',
  },
  disabled: disabled,
});
export const frozenAddForm = (formRef, $t, isModify, digitUpper, mainType, exchangeType, disableMap) => {
  const partOne = [{
    valueType: 'input',
    name: 'accountid',
    label: $t('app.options.accountid'),
    rules: [{ required: true }],
    fieldProps: {
      disabled: isModify,
    }
  }, {
    valueType: 'radio',
    name: 'allocttype',
    label: $t('app.options.db.funds.allocttype'),
    rules: [{ required: true }],
    fieldProps: {
      optionType: 'button',
      buttonStyle: 'solid',
      options: selectOptions(alloctTypeDict),
    }
  }, {
    valueType: 'radio',
    name: 'scalemode1',
    label: $t('app.options.db.funds.scalemode1'),
    extra: $t('app.options.db.funds.scalemode1.tip'),
    rules: [{ required: true }],
    fieldProps: {
      optionType: 'button',
      buttonStyle: 'solid',
      options: selectOptions(assignTypeDict),
    }
  }];
  const partTwo = {
    valueType: 'radio',
    name: 'scalemode2',
    label: $t('app.options.db.funds.scalemode2'),
    extra: $t('app.options.db.funds.scalemode2.tip'),
    rules: [{ required: true }],
    fieldProps: {
      optionType: 'button',
      buttonStyle: 'solid',
      options: selectOptions(assignTypeDict),
    }
  };

  const assignPart = [null, ({
    valueType: 'slider',
    label: $t('app.options.db.funds.scale1'),
    fieldProps: scaleSliderOptions(false),
    otherProps: {
      formref: formRef,
      name: 'scale1',
      rules: [{ required: true }],
    }
  }), ({
    valueType: 'number',
    name: 'scale1',
    label: $t('app.options.db.funds.scale1'),
    extra: digitUpper,
    rules: [{ required: true }],
    fieldProps: {
      min: -1000000000000000,
      max: 1000000000000000,
      step: 0.01,
      precision: 2,
      prefix: $t('app.options.db.funds.symbol'),
      addonAfter: $t('app.options.db.funds.unit'),
    }
  })][mainType];

  let exchangePart = [];
  exchangeDict.forEach((item, idx) => {
    if (item !== '' && item !== '-') {
      const exSlider = [null, ({
        valueType: 'slider',
        label: item,
        fieldProps: scaleSliderOptions(disableMap[idx]),
        otherProps: {
          formref: formRef,
          name: 'alloction' + idx,
          style: { marginBottom: 0 },
        }
      }), ({
        valueType: 'number',
        name: 'alloction' + idx,
        label: item,
        fieldProps: {
          max: 1000000000000000,
          step: 0.01,
          precision: 2,
          prefix: $t('app.options.db.funds.symbol'),
          addonAfter: $t('app.options.db.funds.unit'),
          disabled: disableMap[idx],
        }
      })][exchangeType];

      const partGroup = [exSlider, {
        valueType: 'switch',
        name: 'isRemaining' + idx,
        label: $t('app.options.db.funds.isremaining'),
        otherProps: {
          valuePropName: 'checked',
          style: (idx < exchangeDict.length - 1) ? {
            paddingBottom: '15px',
            borderBottom: '1px solid #eee',
          } : null
        }
      }];

      exchangePart = [
        ...exchangePart,
        ...partGroup,
      ];
    }
  });

  return [
    ...partOne,
    assignPart,
    partTwo,
    ...exchangePart,
  ];
}

export const resetPwdForm = $t => [{
  valueType: 'text',
  name: 'userid',
  label: $t('app.options.userid'),
}, {
  valueType: 'password',
  name: 'newpassword',
  label: $t('app.options.newpassword'),
  rules: [{ required: true }],
  fieldProps: {
    prefix: keyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}, {
  valueType: 'password',
  name: 'repassword',
  label: $t('app.options.repassword'),
  rules: [
    { required: true },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newpassword') === value) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error(
            $t('app.options.repassword.error')
          ));
        }
      }
    }),
  ],
  fieldProps: {
    prefix: copyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}];
