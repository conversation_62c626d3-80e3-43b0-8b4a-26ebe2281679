import { Spin } from 'antd';
import {
  KeyOutlined,
  CopyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';

import TableTitle from '@components/Table/fields/Title';
import { userStatusDict, clientTypeDict, exchangeDict, accountTypeDict } from '@utils/dicts';
import formOptions from '@utils/form-options';

export const searchForm = (intl, userOptions) => [{
  valueType: 'select',
  name: 'userid',
  label: $t('app.options.userid'),
  fieldProps: {
    showSearch: true,
    options: userOptions
  }
}, {
  valueType: 'input',
  name: 'name',
  label: $t('app.options.db.userinfo.name'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.options.db.userinfo.status'),
  fieldProps: {
    options: userStatusDict.map((item, idx) => ({
      label: item,
      value: idx,
    }))
  }
}];

export const addForm = (intl, isModify, userChange, appFocus, appOptions, loading) => [{
  valueType: 'hidden',
  name: 'usertype',
  label: $t('app.options.db.userinfo.usertype'),
  noStyle: true
}, {
  valueType: 'input',
  name: 'userid',
  label: $t('app.options.userid'),
  rules: !isModify ? [
    { required: true },
    { min: 3 },
    { pattern: new RegExp(/^[a-z0-9_]*$/, 'g'), message: $t('app.options.userid.msg') },
    { validator: userChange }
  ] : null,
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'password',
  name: 'password',
  label: $t('app.auth.password'),
  rules: [{ required: !isModify }],
  fieldProps: {
    disabled: isModify,
  }
}, {
  valueType: 'input',
  name: 'name',
  label: $t('app.options.db.userinfo.name'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.options.db.userinfo.status'),
  fieldProps: {
    allowClear: false,
    options: userStatusDict.map((item, idx) => ({
      label: item,
      value: idx,
    }))
  }
}, {
//   valueType: 'select',
//   name: 'usertype',
//   label: '用户类型',
//   fieldProps: {
//     allowClear: false,
//     options: userTypeDict.map((item, idx) => ({
//       label: item,
//       value: idx,
//     }))
//   }
// }, {
  valueType: 'number',
  name: 'tradeflow',
  label: $t('app.options.db.userinfo.tradeflow'),
}, {
  valueType: 'number',
  name: 'logincount',
  label: $t('app.options.db.userinfo.logincount'),
}, {
  valueType: 'number',
  name: 'loginsuccess',
  label: TableTitle(
    $t('app.options.db.userinfo.loginsuccess'),
    $t('app.options.db.userinfo.loginsuccess.long')
  ),
}, {
  valueType: 'number',
  name: 'loginfailed',
  label: TableTitle(
    $t('app.options.db.userinfo.loginfailed'),
    $t('app.options.db.userinfo.loginfailed.long')
  ),
}, {
  valueType: 'select',
  name: 'appid',
  label: $t('app.menu.db.appid'),
  rules: [{ required: true }],
  fieldProps: {
    mode: 'multiple',
    maxTagCount: 'responsive',
    onFocus: appFocus,
    options: appOptions,
    notFoundContent: loading && (<Spin size="small" />),
  }
}];

export const clientAddForm = (intl, isModify, segmentList, serverList) => [{
  valueType: 'hidden',
  name: 'userid',
  label: $t('app.options.userid'),
  noStyle: true
}, {
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  rules: [
    { required: true },
    { min: 5 }
  ],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'select',
  name: 'clienttype',
  label: $t('app.options.clienttype'),
  fieldProps: {
    disabled: isModify,
    options: formOptions(clientTypeDict)
  },
  rules: [{ required: true }]
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    disabled: isModify,
    options: formOptions(exchangeDict)
  },
  rules: [{ required: true }]
}, {
//   valueType: 'select',
//   name: 'serverid',
//   label: TableTitle(
//     $t('app.options.serverid'),
//     $t('app.options.serverid.long')
//   ),
//   // tooltip: '如果为0，则所有服务均可交易',
//   rules: [{ required: true }],
//   fieldProps: {
//     options: serverList
//   }
// }, {
  valueType: 'select',
  name: 'segmentid',
  label: $t('app.options.segmentid'),
  rules: [{ required: true }],
  fieldProps: {
    options: segmentList
  }
}, {
  valueType: 'select',
  name: 'defaultsvrid',
  label: $t('app.options.defaultsvrid'),
  rules: [{ required: true }],
  fieldProps: {
    options: serverList
  }
}];

export const fronzenAddForm = (intl, isModify, digitUpper) => [{
  valueType: 'input',
  name: 'accountid',
  label: $t('app.options.accountid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
  }
}, {
  valueType: 'radio',
  name: 'accounttype',
  label: $t('app.options.accounttype'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: formOptions(accountTypeDict)
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: formOptions(exchangeDict)
  }
}, {
  valueType: 'number',
  name: 'frozenfunds',
  label: $t('app.options.frozenfunds'),
  extra: digitUpper,
  rules: [{ required: true }],
  fieldProps: {
    max: ****************,
    prefix: $t('app.options.db.funds.symbol'),
    addonAfter: $t('app.options.db.funds.unit'),
  }
}];

export const resetPwdForm = intl => [{
  valueType: 'text',
  name: 'userid',
  label: $t('app.options.userid'),
}, {
  valueType: 'password',
  name: 'newpassword',
  label: $t('app.options.newpassword'),
  rules: [{ required: true }],
  fieldProps: {
    prefix: <KeyOutlined className="ti-form-item-icon" />,
    iconRender: visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />),
  }
}, {
  valueType: 'password',
  name: 'repassword',
  label: $t('app.options.repassword'),
  rules: [
    { required: true },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newpassword') === value) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error(
            $t('app.options.repassword.error')
          ));
        }
      }
    }),
  ],
  fieldProps: {
    prefix: <CopyOutlined className="ti-form-item-icon" />,
    iconRender: visible => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />),
  }
}];
