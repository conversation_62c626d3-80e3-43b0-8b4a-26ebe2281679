import { useRef, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Button, App } from 'antd';
import dayjs from 'dayjs';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  exportDownModal,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn } = TableFields;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;

const ExSubmitBase = () => {
  const queryForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t, FormattedMsg } = useFormattedMessage();

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.db.exsubmit.serverid'),
    dataIndex: 'serverid',
  }, {
    title: $t('app.options.tradingday'),
    dataIndex: 'tradingday',
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.db.exsubmit.loginid'),
    dataIndex: 'loginid',
  }, {
    title: $t('app.options.db.exsubmit.submitinfo'),
    dataIndex: 'submitinfo',
    render: text => <div style={{ maxWidth: '800px' }}>{text}</div>,
  }];

  useUnmount(() => cancel());

  const getTableData = async params => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QrySubmitInfo), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const createSearchForm = useMemo(() => searchForm($t), [$t]);

  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
      tradingday: dayjs(values.tradingday).format('YYYYMMDD'),
    });
  }
  const onReset = () => {
    run(null);
  }
  const toExport = values => {
    fetchFunc(MESSAGE_TYPE.DownloadSubmitInfo, {
      ...defaultParams,
      ...values,
      tradingday: dayjs(values.tradingday).format('YYYYMMDD'),
    }, resp => {
      // console.log(resp);
      if (resp.errmessage) {
        exportDownModal(resp.errmessage);
      } else {
        message.error('导出失败');
      }
    });
  }

  return (
    <PageContent
      title={$t('app.menu.db.exsubmit', {
        defaultMsg: '看穿式监管',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        onSearch: onSearch,
        onReset: onReset,
        isLoading: loading,
        initValues: {
          tradingday: dayjs(),
        },
        otherBtns: (
          <Button
            className="ti-btn-warning"
            onClick={() => queryForm.current?.submit(toExport)}
          >
            {FormattedMsg('app.general.export')}
          </Button>
        ),
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        checkable: false,
        onRefresh: refresh,
      }}
    />
  );
}

// const ExSubmitAlive = ({ keepName, ...props }) => (
const ExSubmit = props => (
  // <KeepAlive name={keepName}>
  <ExSubmitBase {...props} />
  // </KeepAlive>
);

export { ExSubmit };
