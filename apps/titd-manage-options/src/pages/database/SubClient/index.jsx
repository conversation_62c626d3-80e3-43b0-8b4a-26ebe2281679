import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Space, Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  formOptions,
  errorResp,
} from '@titd/publics/utils';

const { Text, Link } = Typography;
const { IndexColumn, TableBadgeDot, TableTag } = TableFields;
const { clientTypeDict, exchangeDict, exchangeAllDict, clientRightDict, tagNormalColors } = dicts;
const { sortByName, objFilter } = tools;
const { checkOptions } = formOptions;
const { SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;

const SubClientBase = () => {
  const queryForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.future.clientid'),
    dataIndex: 'clientid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'mainclientid',
    key: 'mainclientid',
    sorter: sortByName('mainclientid'),
    sortOrder: sortedInfo.columnKey === 'mainclientid' ? sortedInfo.order : '',
  }, {
    title: $t('app.options.clienttype'),
    dataIndex: 'clienttype',
    filters: checkOptions(clientTypeDict),
    filteredValue: filteredInfo.clienttype || null,
    onFilter: (value, record) => record.clienttype === value,
    render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    filters: checkOptions(exchangeDict),
    filteredValue: filteredInfo.exchangeid || null,
    onFilter: (value, record) => record.exchangeid === value,
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.future.accountid'),
    dataIndex: 'accountid',
  // }, {
  //   title: $t('app.options.accounttype'),
  //   dataIndex: 'accounttype',
  //   render: text => TableBadgeDot(text, accountTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'mainuserid',
  }, {
    title: $t('app.options.templateid'),
    dataIndex: 'templateid',
    render: text => templateFunc(text),
  }, {
    title: $t('app.future.quoteid'),
    dataIndex: 'quotetemplateid',
    render: text => templateFunc(text),
  }, {
    title: $t('app.options.rightid'),
    dataIndex: 'rightid',
    render: text => rightFunc(text)
  }];

  const templateFunc = text => {
    return text?.length > 0 && (<Space>
      {text.map((item, idx) => (
        <Link underline key={idx} onClick={() => navigate('/db/tradetpl', { state: { datas: item } })}>{item}</Link>
      ))}
    </Space>);
  }
  const rightFunc = text => {
    return text?.length > 0 && text?.map((item, idx) => (
      <span key={idx}>
        {TableTag(item, clientRightDict, tagNormalColors, true)}
      </span>
    ));
  }

  const [accountOptions, setAccountOptions] = useState([]);

  const accountFocus = useCallback(async () => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryMainUserInfo), defaultParams);

    if (resp?.length > 0) {
      const accountGroup = resp.map(item => ({
        label: item.accountname ? `${item.mainaccountid} (${item.accountname})` : item.mainaccountid,
        value: item.mainaccountid,
      }));
      setAccountOptions(accountGroup);
    }
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, accountFocus, accountOptions), [$t, accountFocus, accountOptions]);

  useUnmount(() => cancel());

  const getTableData = async params => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QrySubClient), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        // 隐藏检查自成交
        rightid: item.rightid?.filter(i => i !== 1),
      }));

      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const tableChange = (pagination, filters, sorter) => {
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (
    <PageContent
      title={$t('app.menu.db.sub.sclient', {
        defaultMsg: '系统交易编码',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: refresh,
        checkable: false,
        handleChange: tableChange,
      }}
    />
  );
}

const SubClient = props => (
  // <KeepAlive name={keepName}>
  <SubClientBase {...props} />
  // </KeepAlive>
);

export { SubClient };
