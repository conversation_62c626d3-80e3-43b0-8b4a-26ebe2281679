import { dicts, formOptions } from '@titd/publics/utils';

const { clientTypeDict, exchangeDict, clientRightDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, clientFocus, clientGroup) => [{
  valueType: 'autocomplete',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    onFocus: clientFocus,
    options: clientGroup,
  }
}, {
  valueType: 'select',
  name: 'clienttype',
  label: $t('app.options.clienttype'),
  fieldProps: {
    options: selectOptions(clientTypeDict)
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}];

export const addForm = ($t, isModify, tplFocus, tplOptions) => [{
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  rules: [
    { required: true },
    { min: 5 },
  ],
  fieldProps: {
    disabled: isModify,
  }
}, {
  valueType: 'select',
  name: 'clienttype',
  label: $t('app.options.clienttype'),
  rules: [{ required: true }],
  fieldProps: {
    options: selectOptions(clientTypeDict)
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'select',
  name: 'templateid',
  label: $t('app.options.templateid'),
  rules: [{ required: true }],
  fieldProps: {
    mode: 'multiple',
    onFocus: tplFocus,
    options: tplOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'select',
  name: 'quotetemplateid',
  label: $t('app.future.quoteid'),
  fieldProps: {
    mode: 'multiple',
    onFocus: tplFocus,
    options: tplOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'checkbox',
  name: 'rightid',
  label: $t('app.options.rightid'),
  fieldProps: {
    options: selectOptions(clientRightDict),
  }
}];
