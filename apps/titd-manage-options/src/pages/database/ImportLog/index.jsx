import { useRef, useState, useMemo } from 'react';
import { Card, List, Button, Typography, Divider, App } from 'antd';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  TableFields,
  forms,
  HeadTitle,
} from '@titd/publics/components';
import { addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dateFormat,
} from '@titd/publics/utils';
// import { IMPORTLOG, TiLocalStorage } from '@utils/storage';

import { useStyles } from './style';

const { IndexColumn } = TableFields;
const { ModalForm } = forms;
const { SITE_URL, MESSAGE_TYPE, PUBLIC_PATH, CURD } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { formatDate, formatDateFull } = dateFormat;

dayjs.extend(isToday);

const { Title, Text } = Typography;

const ImportLogBase = () => {
  const modalForm = useRef(null);
  const { message } = App.useApp();
  const { $t, FormattedMsg } = useFormattedMessage();

  const { styles } = useStyles();

  const [textList, setTextList] = useState([]);
  const [textDate, setTextDate] = useState('');
  const [localDate, setLocalDate] = useState('');
  const [isNotify, setIsNotify] = useState(false);

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const getLogs = async () => {
    const resp = await Request.get(`${PUBLIC_PATH.IMPORT_LOG}?t=${+new Date()}`, {
      baseURL: '',
    });

    if (resp.status === 200) {
      const { data: logText } = resp;
      const logTextGroup = logText.split('\n').filter(i => i);
      // console.log(response, logText, logTextGroup);

      // 取最后一行是否成功
      const isSuccess = (/完成/g).test(logTextGroup[logTextGroup.length - 1]);
      setIsNotify(!isSuccess);

      const logTextFormat = logTextGroup.map(item => {
        const logArr = item.split('|');

        if (logArr.length === 1) {
          return {
            date: null,
            msg: item,
          };
        }
        return {
          date: logArr[0],
          msg: logArr[1],
        };
      });
      setTextList(logTextFormat);

      // 取第2行日期，若第2行是“结算日”取第7行
      // const logName = (/结算日/g).test(logTextGroup[1]);
      // const logDate = logName ? logTextGroup[6].match(/\d{8}/g) : logTextGroup[1].match(/\d{8}/g);
      // 匹配含有“交易日”和8位数字的行
      const logRow = logTextGroup.find(item => (/交易日:\d{8}/g).test(item));
      const logDate = logRow.match(/\d{8}/g) || [];
      if (logDate.length > 0) {
        setTextDate(logDate[logDate.length - 1]);
      }
    }
  }

  useMount(() => {
    getConfirmDate();
  });
  useUnmount(() => cancel());

  const { loading, cancel } = useRequest(getLogs);

  const createAddForm = useMemo(() => addForm($t), [$t]);

  const getConfirmDate = async () => {
    const dateResp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.OwTdSvr), {
      ...defaultParams,
      svrid: undefined,
    });

    dateResp.confirtime && setLocalDate(dateResp.confirtime);
  }

  const toSure = () => {
    modalForm.current.show($t('app.options.db.log.sure'), {
      importDate: formatDate(textDate),
      // sureDate: dayjs(),
    });
  }

  const addSubmit = async form => {
    // console.log(form);
    const saveData = form.sureDate.format('YYYYMMDD');
    const logParams = {
      ...defaultParams,
      id: 2,
      savestr: saveData,
    }

    const respData = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.ImportLog), logParams);

    if (respData.status === 200) {
      message.success($t('app.options.db.log.sure.success'));

      getConfirmDate();
      // TiLocalStorage.set(IMPORTLOG, saveData);
    } else {
      message.error(respData?.errmessage || CURD.ServerError);
    }
  }

  return (<>
    <HeadTitle title={$t('app.menu.db.importlog', {
      defaultMsg: '上场确认',
    })} />

    <Card
      styles={{
        body: {
          paddingTop: '5px',
          paddingBottom: '5px',
        },
      }}
      title={textDate ? <>
          <Title level={3}>{formatDate(textDate)}</Title>
          {localDate ? <Text type="secondary">
            {FormattedMsg('app.options.db.log.lastdate')}
            {`：${formatDate(localDate)}`}
          </Text> : null}
        </> : ''}
      extra={<Button
        type="primary"
        danger={isNotify}
        disabled={localDate === ''}
        onClick={toSure}
        loading={loading}
      >{
        dayjs(localDate).isToday()
          ? $t('app.options.db.log.sure.again')
          : $t('app.options.db.log.sure')
      }</Button>}
    >
      <List
        loading={loading}
        dataSource={textList}
        renderItem={(item, index) => <List.Item>
          <span className={styles.listIndex}>
            <IndexColumn border>{index + 1}</IndexColumn>
          </span>
          {item.date ? (<>
            <Text type="secondary">{formatDateFull(item.date)}</Text>
            <Divider type="vertical" />
          </>) : null}
          {item.msg}
        </List.Item>}
      />
    </Card>

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    >
      {isNotify && <div style={{ textAlign: 'right' }}><Text type="danger">导入日志有异常，请确认</Text></div>}
    </ModalForm>
  </>);
}

const ImportLog = props => (
  // <KeepAlive name={keepName}>
  <ImportLogBase {...props} />
  // </KeepAlive>
);

export { ImportLog };
