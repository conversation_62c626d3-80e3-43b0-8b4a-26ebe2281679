import {
  consts,
  dicts,
  formOptions,
} from '@titd/publics/utils';

const { DEFAULT_CONFIGS } = consts;
const { clientTypeDict, exchangeDict, userTypeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, userFocus, userGroup, clientOptions) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.userid'),
  fieldProps: {
    onFocus: userFocus,
    options: userGroup,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'autocomplete',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    options: clientOptions
  }
}, {
  valueType: 'select',
  name: 'clienttype',
  label: $t('app.options.clienttype'),
  fieldProps: {
    options: selectOptions(clientTypeDict)
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'select',
  name: 'usertype',
  label: $t('app.options.usertype'),
  fieldProps: {
    allowClear: false,
    options: selectOptions(userTypeDict),
  }
}];

export const addForm = ($t, isModify, userGroup, segmentList, serverList) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.userid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: userGroup,
  }
}, {
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  rules: [
    { required: true },
    { min: 5 }
  ],
  fieldProps: {
    readOnly: isModify,
  }
}, {
  valueType: 'select',
  name: 'clienttype',
  label: $t('app.options.clienttype'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(clientTypeDict)
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'input',
  name: 'bizpbu',
  label: $t('app.options.bizpbu'),
  rules: [{ required: DEFAULT_CONFIGS.PUB_REQUIRED }]
}, {
  valueType: 'input',
  name: 'salesnumb',
  label: $t('app.options.salesnumb'),
  rules: [{ required: DEFAULT_CONFIGS.PUB_REQUIRED }]
}, {
//   valueType: 'select',
//   name: 'serverid',
//   label: TableTitle(
//     $t('app.options.serverid'),
//     $t('app.options.serverid.long')
//   ),
//   // tooltip: '如果为0，则所有服务均可交易',
//   rules: [{ required: true }],
//   fieldProps: {
//     options: serverList
//   }
// }, {
  valueType: 'select',
  name: 'segmentid',
  label: $t('app.options.segmentid'),
  rules: [{ required: true }],
  fieldProps: {
    options: segmentList
  }
}, {
  valueType: 'select',
  name: 'defaultsvrid',
  label: $t('app.options.defaultsvrid'),
  rules: [{ required: true }],
  fieldProps: {
    options: serverList
  }
}];
