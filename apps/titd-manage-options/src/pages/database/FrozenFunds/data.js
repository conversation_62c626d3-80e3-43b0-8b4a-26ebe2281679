import { dicts, formOptions } from '@titd/publics/utils';

const { accountTypeDict, exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, accountOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions
  }
}, {
  valueType: 'select',
  name: 'accounttype',
  label: $t('app.options.accounttype'),
  fieldProps: {
    options: selectOptions(accountTypeDict)
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}];

export const addForm = ($t, isModify, accountOptions, digitUpper) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: accountOptions,
  }
}, {
  valueType: 'radio',
  name: 'accounttype',
  label: $t('app.options.accounttype'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(accountTypeDict)
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'number',
  name: 'frozenfunds',
  label: $t('app.options.frozenfunds'),
  extra: digitUpper,
  rules: [{ required: true }],
  fieldProps: {
    max: ****************,
    step: 0.01,
    precision: 2,
    prefix: $t('app.options.db.funds.symbol'),
    addonAfter: $t('app.options.db.funds.unit'),
  }
}];
