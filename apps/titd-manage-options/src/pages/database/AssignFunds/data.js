import { dicts, formOptions } from '@titd/publics/utils';

const { alloctTypeDict, assignTypeDict, exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, accountOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions
  }
}];

const scaleSliderOptions = disabled => ({
  min: 0,
  max: 100,
  step: 1,
  marks: {
    0: '0',
    100: '100%',
  },
  disabled: disabled,
  // tooltip: {
  //   formatter: value => `${value * 100}%`,
  // },
});

export const addForm = (formRef, $t, isModify, accountOptions, digitUpper, mainType, exchangeType, disableMap) => {
  const partOne = [{
    valueType: 'autocomplete',
    name: 'accountid',
    label: $t('app.options.accountid'),
    rules: [{ required: true }],
    fieldProps: {
      disabled: isModify,
      options: accountOptions,
    }
  }, {
    valueType: 'radio',
    name: 'allocttype',
    label: $t('app.options.db.funds.allocttype'),
    rules: [{ required: true }],
    fieldProps: {
      optionType: 'button',
      buttonStyle: 'solid',
      // options: selectOptions(alloctTypeDict.slice(0, alloctTypeDict.length - 1)),
      options: selectOptions(alloctTypeDict),
    }
  }, {
    valueType: 'plain',
    name: 'prebalance',
    label: $t('app.options.mem.account.prebalance'),
    fieldProps: {
      style: {
        fontSize: '20px',
        lineHeight: '20px',
        color: '#0081cc',
      }
    }
  }, {
    valueType: 'radio',
    name: 'scalemode1',
    label: $t('app.options.db.funds.scalemode1'),
    extra: $t('app.options.db.funds.scalemode1.tip'),
    rules: [{ required: true }],
    fieldProps: {
      optionType: 'button',
      buttonStyle: 'solid',
      options: selectOptions(assignTypeDict),
    }
  }];
  const partTwo = {
    valueType: 'radio',
    name: 'scalemode2',
    label: $t('app.options.db.funds.scalemode2'),
    extra: $t('app.options.db.funds.scalemode2.tip'),
    rules: [{ required: true }],
    fieldProps: {
      optionType: 'button',
      buttonStyle: 'solid',
      options: selectOptions(assignTypeDict),
    }
  };

  const assignPart = [null, ({
    valueType: 'slider',
    label: $t('app.options.db.funds.scale1'),
    fieldProps: scaleSliderOptions(false),
    otherProps: {
      formref: formRef,
      name: 'scale1',
      rules: [{ required: true }],
    }
  }), ({
    valueType: 'number',
    name: 'scale1',
    label: $t('app.options.db.funds.scale1'),
    extra: digitUpper,
    rules: [{ required: true }],
    fieldProps: {
      min: -1000000000000000,
      max: 1000000000000000,
      // step: 0.01,
      precision: 2,
      prefix: $t('app.options.db.funds.symbol'),
      addonAfter: $t('app.options.db.funds.unit'),
    }
  })][mainType];

  let exchangePart = [];
  exchangeDict.forEach((item, idx) => {
    if (item !== '' && item !== '-') {
      const exSlider = [null, ({
        valueType: 'slider',
        label: item,
        fieldProps: scaleSliderOptions(disableMap[idx]),
        otherProps: {
          formref: formRef,
          name: 'alloction' + idx,
          style: { marginBottom: 0 },
        }
      }), ({
        valueType: 'number',
        name: 'alloction' + idx,
        label: item,
        fieldProps: {
          max: 1000000000000000,
          // step: 0.01,
          precision: 2,
          prefix: $t('app.options.db.funds.symbol'),
          addonAfter: $t('app.options.db.funds.unit'),
          disabled: disableMap[idx],
        }
      })][exchangeType];

      const partGroup = [exSlider, {
        valueType: 'switch',
        name: 'isRemaining' + idx,
        label: $t('app.options.db.funds.isremaining'),
        otherProps: {
          valuePropName: 'checked',
          style: (idx < exchangeDict.length - 1) ? {
            paddingBottom: '15px',
            borderBottom: '1px solid #eee',
          } : null
        }
      }];

      exchangePart = [
        ...exchangePart,
        ...partGroup,
      ];
    }
  });

  return [
    ...partOne,
    assignPart,
    partTwo,
    ...exchangePart,
  ];
}
