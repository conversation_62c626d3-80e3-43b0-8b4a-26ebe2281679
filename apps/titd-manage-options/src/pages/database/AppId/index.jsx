import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
import { RedoOutlined } from '@ant-design/icons';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  errorResp,
  tools,
  updateRowData
} from '@titd/publics/utils';

import randomWords from './random-words';

const { Text } = Typography;
const { IndexColumn } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;

const hideCenter = text => text.slice(0,3) + '****' + text.slice(-3);

const AppIdBase = () => {
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);

  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.menu.db.appid'),
    dataIndex: 'appid',
    key: 'appid',
    sorter: sortByName('appid'),
    sortOrder: sortedInfo.columnKey === 'appid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.menu.db.appid') + $t('app.options.db.appid.identify'),
    dataIndex: 'identify',
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ACTION,
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
      ]} />
    )
  }];

  const [autoOptions, setAutoOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);
  // const [randomLoading, setRandomLoading] = useState(false);

  // const { run } = useDebounceFn((rules, value, callback) => {
  //   const isExist = autoOptions.find(item => item.value === value);
  //   if (isExist) {
  //     callback(new Error($t('app.options.db.appid.error')));
  //   } else {
  //     callback();
  //   }
  // }, { wait: 500 });

  const randomBtn = useCallback(() => (
    <Text style={{ cursor: 'pointer' }} onClick={() => modalForm.current.set({
      'identify': randomWords(false, 10)
    })}><RedoOutlined /></Text>
  ), []);

  const createSearchForm = useMemo(() => searchForm($t, autoOptions), [$t, autoOptions]);
  // const createAddForm = useMemo(() => addForm(intl, isModify, run, randomBtn), [intl, isModify, run, randomBtn]);
  const createAddForm = useMemo(() => addForm($t, isModify, randomBtn), [$t, isModify, randomBtn]);

  useUnmount(() => cancel());

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAppId), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        const autoId = respData.map(item => ({
          label: item.appid,
          value: item.appid,
        }));
        setAutoOptions(autoId);
      } else {
        setAutoOptions([]);
      }
    }

    // 重新载入，选择项清空
    if (selectKeys.length > 0) {
      setSelectKeys([]);
    }

    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => ({
        ...item,
        id: idx + 1,
        identify: hideCenter(item.identify),
      }));
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryAppId), {
      ...defaultParams,
      appid: record.appid,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }

    return null;
  }

  const onSearch = values => {
    run({
      ...defaultParams,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultParams);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    modalForm.current.show(0, {
      'identify': randomWords(false, 10)
    });
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      // 隐藏部分代码
      newRecord.identify = hideCenter(newRecord.identify);

      setIsModify(true);
      modalForm.current.show(1, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
      });
      mutate(newData);
    } else {
      message.error($t('app.general.deleted'));
      refresh();
    }
  }
  const deleteFunc = async (record) => {
    const deleteData = {
      ...defaultParams,
      appid: record.appid,
    }

    fetchFunc(MESSAGE_TYPE.DelAppId, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      refresh();
    });
  }
  const toDeleteMore = (records) => {
    // console.log('Delete More');
    const ids = records.map(i => i.appid);
    Confirm({
      modal,
      content: ids.join(', '),
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record);
        });
      }
    });
  }
  const toDelete = (record) => {
    // console.log('Delete');
    Confirm({
      modal,
      content: record.appid,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }
  const addSubmit = async (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdAppId
      : MESSAGE_TYPE.InsAppId, postData, () => {
      message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);
      refresh();
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.appid', {
        defaultMsg: 'APPID',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: refresh,
        handleChange: tableChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={addSubmit}
      formData={createAddForm}
    />
  </>);
}

// const AppIdAlive = ({ keepName, ...props }) => (
const AppId = props => (
  // <KeepAlive name={keepName}>
  <AppIdBase {...props} />
  // </KeepAlive>
);

export { AppId };
