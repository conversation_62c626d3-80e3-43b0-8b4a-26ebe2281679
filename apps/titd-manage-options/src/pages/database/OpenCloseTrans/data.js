// import { Spin } from 'antd';
import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict, openCloseDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, productFocus, productOptions) => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'select',
  name: 'productid',
  label: $t('app.options.productid'),
  fieldProps: {
    showSearch: true,
    onFocus: productFocus,
    options: productOptions,
    // notFoundContent: loading ? <Spin size="small" /> : '无数据',
  }
}];

export const addForm = ($t, isModify, productFocus, productOptions) => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  rules: [{ required: true }],
  fieldProps: {
    disabled: isModify,
    options: selectOptions(exchangeDict),
  }
}, {
  valueType: 'hidden',
  name: 'producttype',
  noStyle: true,
}, {
  valueType: 'select',
  name: 'productid',
  label: $t('app.options.productid'),
  fieldProps: {
    disabled: isModify,
    onFocus: productFocus,
    options: productOptions,
    // notFoundContent: loading ? <Spin size="small" /> : '无数据',
  }
}, {
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
}, {
  valueType: 'select',
  name: 'open1',
  label: $t('app.future.openclosetrans.openclosetype'),
  fieldProps: {
    options: selectOptions(openCloseDict),
  }
}, {
  valueType: 'number',
  name: 'open2',
  label: $t('app.future.openclosetrans.historyvolume'),
}];
