import { useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount, useDebounceFn } from 'ahooks';

import {
  PageContent,
  TiTable,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
} from '@titd/publics/components';
import { searchForm, addForm, resetPwdForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  formOptions,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';
// import { USER, TiSessionStorage } from '@utils/storage';

import RiskClient from './RiskClient';

const { Text } = Typography;
const { IndexColumn, TableTag, TableBadgeDot } = TableFields;
const { ModalForm, Confirm } = forms;
const { updateLink, deleteLink, resetPwdLink } = links;
const { insertBtn, updateBtn, deleteBtn } = btns;
const { userStatusDict, yesNoDict, clientTypeDict, exchangeAllDict, tagColors, tagNormalColors, yesNoColor } = dicts;
const { SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { sortByName, objFilter } = tools;
const { checkOptions } = formOptions;
const { formatDateFull } = dateFormat;

const defaultForm = {
  status: 0,
  tradeflow: 20,
  logincount: 50,
  loginsuccess: 50,
  loginfailed: 50,
  isglobal: 0,
}

let userGroup = [];
let riskUserGroup = [];

const UserRiskBase = () => {
  const modalForm = useRef(null);
  const resetForm = useRef(null);
  const riskClient = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([]);
  const [checkUser, setCheckUser] = useState(null);
  const [clientData, setClientData] = useState({});
  const [expandRows, setExpandRows] = useState([]);

  const [filteredInfo, setFilteredInfo] = useState({});
  const [sortedInfo, setSortedInfo] = useState({});

  const defaultParams = DEFAULT_SESSION_PARAMS();
  const defaultSearch = {
    ...defaultParams,
    status: -1,
    isglobal: -1,
  }

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.db.userrisk.userid'),
    dataIndex: 'userid',
    key: 'userid',
    sorter: sortByName('userid'),
    sortOrder: sortedInfo.columnKey === 'userid' ? sortedInfo.order : null,
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.db.userinfo.name'),
    dataIndex: 'name',
  }, {
    title: $t('app.options.db.userinfo.status'),
    dataIndex: 'status',
    filters: checkOptions(userStatusDict),
    filteredValue: filteredInfo.status || null,
    onFilter: (value, record) => record.status === value,
    render: text => TableTag(text, userStatusDict, tagColors),
  }, {
    title: $t('app.options.db.userrisk.isglobal'),
    dataIndex: 'isglobal',
    filters: checkOptions(yesNoDict),
    filteredValue: filteredInfo.isglobal || null,
    onFilter: (value, record) => record.isglobal === value,
    render: text => TableTag(text || 0, yesNoDict, yesNoColor),
  }, {
    title: $t('app.options.db.userrisk.ipmac'),
    dataIndex: 'ipmac',
  }, {
  //   title: '最后登录的IP地址',
  //   dataIndex: 'lastloginip',
  // }, {
    title: $t('app.options.db.userrisk.lastlogintime'),
    dataIndex: 'lastlogintime',
    render: text => formatDateFull(text),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.USER,
    // onCell: () => {
    //   return { onClick: e => e.stopPropagation() }
    // },
    render: (_, record) => (
      <ActionLinks links={[
        updateLink(toUpdate, record),
        deleteLink(toDelete, record),
        resetPwdLink(toResetPwd, record),
      ]} />
    )
  }];

  const [userOptions, setUserOptions] = useState([]);
  const [isModify, setIsModify] = useState(false);
  const [isGlobal, setIsGlobal] = useState(0);

  const { run: checkUserRun } = useDebounceFn((rules, value, callback) => {
    const allUser = [...userGroup, ...riskUserGroup];
    // console.log(userGroup, riskUserGroup, allUser);
    const isExist = allUser.indexOf(value);
    if (isExist > -1 && !isModify) {
      callback(new Error($t('app.options.userid.error')));
    } else {
      if (value !== '') {
        setCheckUser(value);
      }

      callback();
    }
  }, { wait: 500 });

  const createSearchForm = useMemo(() => searchForm($t, userOptions), [$t, userOptions]);
  // const createAddForm = useMemo(() => {
  //   const userForm = addForm(isModify, run);
  //   return [{
  //     content: userForm,
  //   }];
  // }, [isModify, run]);
  const createAddForm = useMemo(() => addForm($t, isModify, checkUserRun), [$t, isModify, checkUserRun]);
  const createResetPwdForm = useMemo(() => resetPwdForm($t), [$t]);

  useMount(() => {
    getUserInfo();
  });
  useUnmount(() => cancel());

  const getTableData = async ({ showAuto, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserRisk), params);

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        // 搜索遍历用
        const userIds = respData.map(item => ({
          label: item.name ? `${item.userid} (${item.name})` : item.userid,
          value: item.userid,
          status: item.status,
        }));
        setUserOptions(userIds);

        // 新增判断用
        riskUserGroup = respData.map(i => i.userid);
      } else {
        setUserOptions([]);
        riskUserGroup = [];
      }
    }

    // 重新载入，选择项、展开项清空
    setSelectKeys([]);
    setExpandRows([]);

    setFilteredInfo({});
    setSortedInfo({});

    if (respData?.length > 0) {
      const tableData = respData.map((item, idx) => {
        if (item.usertype && item.isglobal) {
          item.isglobal = Number(item.isglobal);
        }

        return {
          ...item,
          id: idx + 1,
        }
      });
      return tableData;
    } else {
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    run,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserRisk), {
      ...defaultSearch,
      userid: record.userid,
      isglobal: record.isglobal,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    }

    return null;
  }
  const getUserInfo = () => {
    fetchFunc(MESSAGE_TYPE.QryUserInfo, defaultParams, (resp) => {
      if (resp?.length > 0) {
        userGroup = resp.map(i => i.userid);
      } else {
        userGroup = [];
      }
    });
  }

  const onRefresh = showAuto => {
    run({
      ...defaultSearch,
      showAuto: showAuto,
    });
    setClientData({});
  }
  const onSearch = values => {
    if (values.status === undefined) {
      delete values.status;
    }
    if (values.isglobal === undefined) {
      delete values.isglobal;
    }

    run({
      ...defaultSearch,
      ...objFilter(values),
    });
  }
  const onReset = () => {
    run(defaultSearch);
  }

  const toInsert = () => {
    // console.log('Insert');
    setIsModify(false);
    setCheckUser(null);

    setIsGlobal(0);
    modalForm.current.show(0, defaultForm);
  }
  const toUpdate = async record => {
    // console.log('Update', record);
    // 更新前获取最新数据
    const newRecord = await getNewestData(record);

    if (newRecord) {
      setIsModify(true);
      setCheckUser(newRecord.userid);

      if (!newRecord.isglobal) {newRecord.isglobal = 0; }
      setIsGlobal(newRecord.isglobal);
      modalForm.current.show(1, newRecord);

      // 更新本条数据
      // if (record.usertype && record.isglobal) {
      //   newRecord.isglobal = Number(record.isglobal);
      // }
      // const newData = updateRowData(dataSource, {
      //   ...newRecord,
      //   id: record.id,
      // });
      // setDataSource(newData);
    } else {
      message.error($t('app.general.deleted'));
      onRefresh();
    }
  }
  // const deleteClientFunc = async (record) => {
  //   const deleteData = {
  //     ...defaultParams,
  //     userid: record.userid,
  //     clientid: record.clientid,
  //     clienttype: record.clienttype,
  //     exchangeid: record.exchangeid,
  //   }

  //   fetchFunc(MESSAGE_TYPE.DelUserClient, deleteData, () => {
  //     // message.success(CURD.Delete + CURD.StatusOkMsg);
  //   });
  // }
  // const deleteClient = async (userId) => {
  //   const clientList = await post(SITE_URL.EXTEND(MESSAGE_TYPE.QryUserClient), {
  //     ...defaultParams,
  //     userid: userId,
  //     usertype: 1,
  //   });

  //   if (clientList.length > 0) {
  //     clientList.forEach((record) => {
  //       deleteClientFunc(record);
  //     });
  //   }
  // }
  const deleteFunc = async (record) => {
    // 先删除用户关联的交易编码信息
    // deleteClient(record.userid);

    const deleteData = {
      ...defaultParams,
      userid: record.userid,
    }

    fetchFunc(MESSAGE_TYPE.DelUserRisk, deleteData, () => {
      message.success(CURD.Delete + CURD.StatusOkMsg);
      onRefresh(true);
    });
  }
  const toDeleteMore = (records) => {
    // console.log('Delete More', records);
    const ids = records.map(i => i.userid);
    Confirm({
      modal,
      content: <>{ids.join(', ')}</>,
      onOk: () => {
        // console.log('ok', records);
        records.forEach((record) => {
          deleteFunc(record);
        });
      }
    });
  }
  const toDelete = (record) => {
    // console.log('Delete', record);
    Confirm({
      modal,
      content: <>{record.userid}</>,
      onOk: async () => {
        // console.log('ok', record);
        deleteFunc(record);
      }
    });
  }
  const toResetPwd = (record) => {
    // console.log('ResetPwd', record);
    resetForm.current.show(2, record);
  }

  const addSubmit = (form, type) => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(type
      ? MESSAGE_TYPE.UpdUserRisk
      : MESSAGE_TYPE.InsUserRisk, postData, async () => {
        message.success((type ? CURD.Update : CURD.Insert) + CURD.StatusOkMsg);

        // 提交绑定的交易编码
        await riskClient.current?.submit();

        onRefresh(!type);
        getClient(form.userid);
      });
  }
  const formValueChange = changedValues => {
    if ('isglobal' in changedValues) {
      setIsGlobal(changedValues.isglobal);
    }
  }

  const clientColumns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clienttype'),
    dataIndex: 'clienttype',
    render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }];

  // 展开表格
  const expandable = {
    expandedRowRender: (record) => {
      return <TiTable
        columns={clientColumns}
        dataSource={clientData[record.userid] || []}
        hasToolBar={false}
        checkable={false}
        bordered={false}
      />
    },
    onExpand: (expanded, record) => {
      // if (clientData[record.userid]) {
      //   return;
      // }
      if (expanded) {
        getClient(record.userid);
      }
    },
    expandedRowKeys: expandRows,
    onExpandedRowsChange: (expandedRows) => {
      setExpandRows(expandedRows);
    }
  }

  const getClient = async userId => {
    const params = {
      ...defaultParams,
      usertype: 1,
      userid: userId,
    }

    fetchFunc(MESSAGE_TYPE.QryUserClient, params, (resp) => {
      const clientTableData = resp.map((item, idx) => ({
        ...item,
        id: idx + 1
      })) || [];
      setClientData({
        ...clientData,
        [userId]: clientTableData
      });
    });
  }

  const resetPwdSubmit = form => {
    const postData = {
      ...defaultParams,
      userid: form.userid,
      newpassword: form.newpassword,
    }

    fetchFunc(MESSAGE_TYPE.UpdPassword, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const tableChange = (pagination, filters, sorter) => {
    // console.log('Various parameters', pagination, filters, sorter);
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.userrisk', {
        defaultMsg: '风控用户',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          insertBtn(toInsert),
          updateBtn(toUpdate, dataSource, selectKeys),
          deleteBtn(toDeleteMore, dataSource, selectKeys),
        ],
        selectKeys: selectKeys,
        setSelectKeys: setSelectKeys,
        onRefresh: onRefresh,
        expandable: expandable,
        handleChange: tableChange,
      }}
    />

    {/* 新增修改 - 风控用户 */}
    <ModalForm
      ref={modalForm}
      formData={createAddForm}
      formGroup
      spanDefault={3}
      width={'80%'}
      onOk={addSubmit}
      onValuesChange={formValueChange}
    >
      {!isGlobal && (
        <RiskClient
          ref={riskClient}
          user={checkUser}
          defaultParams={defaultParams}
        />
      )}
    </ModalForm>

    {/* 重置密码 */}
    <ModalForm
      ref={resetForm}
      onOk={resetPwdSubmit}
      formData={createResetPwdForm}
    />
  </>);
}

const UserRisk = props => (
  // <KeepAlive name={keepName}>
  <UserRiskBase {...props} />
  // </KeepAlive>
);

export { UserRisk };
