import { dicts, formOptions } from '@titd/publics/utils';

const { alloctTypeDict, assignTypeDict, exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, accountOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.future.accountid'),
  fieldProps: {
    options: accountOptions
  }
}];

const scaleSliderOptions = disabled => ({
  min: 0,
  max: 100,
  step: 1,
  marks: {
    0: '0',
    100: '100%',
  },
  disabled: disabled,
  // tooltip: {
  //   formatter: value => `${value * 100}%`,
  // },
});

export const addForm = (formRef, $t, isModify, accountOptions, exchangeType, disableMap) => {
  const partOne = [{
    valueType: 'autocomplete',
    name: 'accountid',
    label: $t('app.future.accountid'),
    rules: [{ required: true }],
    fieldProps: {
      disabled: isModify,
      options: accountOptions,
    }
  }, {
    valueType: 'radio',
    name: 'allocttype',
    label: $t('app.options.db.funds.allocttype'),
    rules: [{ required: true }],
    fieldProps: {
      optionType: 'button',
      buttonStyle: 'solid',
      options: selectOptions(alloctTypeDict),
    }
  }];
  const partTwo = {
    valueType: 'radio',
    name: 'scalemode2',
    label: $t('app.options.db.funds.allocttype.child'),
    extra: $t('app.options.db.funds.scalemode2.tip'),
    rules: [{ required: true }],
    fieldProps: {
      optionType: 'button',
      buttonStyle: 'solid',
      options: selectOptions(assignTypeDict),
    }
  };

  let exchangePart = [];
  exchangeDict.forEach((item, idx) => {
    if (item !== '' && item !== '-') {
      const exSlider = [null, ({
        valueType: 'slider',
        label: item,
        fieldProps: scaleSliderOptions(disableMap[idx]),
        otherProps: {
          formref: formRef,
          name: 'alloction' + idx,
          style: { marginBottom: 0 },
        }
      }), ({
        valueType: 'number',
        name: 'alloction' + idx,
        label: item,
        fieldProps: {
          max: 1000000000000000,
          // step: 0.01,
          precision: 2,
          prefix: $t('app.options.db.funds.symbol'),
          addonAfter: $t('app.options.db.funds.unit'),
          disabled: disableMap[idx],
        }
      })][exchangeType];

      const partGroup = [exSlider, {
        valueType: 'switch',
        name: 'isRemaining' + idx,
        label: $t('app.options.db.funds.isremaining'),
        otherProps: {
          valuePropName: 'checked',
          style: (idx < exchangeDict.length - 1) ? {
            paddingBottom: '15px',
            borderBottom: '1px solid #eee',
          } : null
        }
      }];

      exchangePart = [
        ...exchangePart,
        ...partGroup,
      ];
    }
  });

  return [
    ...partOne,
    partTwo,
    ...exchangePart,
  ];
}
