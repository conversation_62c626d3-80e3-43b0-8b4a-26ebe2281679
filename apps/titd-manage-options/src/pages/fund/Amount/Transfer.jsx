import { useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { Card, Spin, App } from 'antd';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  TableSimple,
  TableFields,
  ActionBtns,
  forms,
  btns,
} from '@titd/publics/components';
import { amountForm } from './data';

import {
  consts,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  currency,
  getList,
  formOptions,
  errorResp,
} from '@titd/publics/utils';

const { IndexColumn, TableTag } = TableFields;
const { SimpleForm } = forms;
const { transferBtn } = btns;
const { seatDict, transferStatusDict, transferStatusColor } = dicts;
const { SITE_URL, MESSAGE_TYPE } = consts;
const { deepCopy } = tools;
const { digitUppercase, formatDot } = currency;
const { getClusterList } = getList;
const { selectObjOptions } = formOptions;

const Transfer = props => {
  const {
    params,
    record,
    onFinish,
  } = props;

  const aForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const seatList = selectObjOptions(seatDict);
  const [clusterList, setClusterList] = useState([]);
  const [serverMap, setServerMap] = useState({});
  const [digitUpper, setDigitUpper] = useState('');

  const [oldAccountid, setOldAccountid] = useState();
  const [respData, setRespData] = useState([]);


  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const respColumns = [{
    dataIndex: 'transferno',
    title: $t('app.stock.account.transferno'),
    width: 80,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    dataIndex: 'sourceclusterid',
    title: $t('app.stock.account.source'),
    render: text => seatDict[text] || serverMap[text] || text,
  }, {
    dataIndex: 'destclusterid',
    title: $t('app.stock.account.dest'),
    render: text => seatDict[text] || serverMap[text] || text,
  }, {
  //   dataIndex: 'amount',
  //   title: $t('app.options.mem.account.amount'),
  //   align: 'right',
  //   render: text => NegaNumber(text),
  // }, {
    dataIndex: 'status',
    title: $t('app.stock.account.status'),
    render: text => TableTag(text + 1, transferStatusDict, transferStatusColor),
  }, {
    dataIndex: 'responsestr',
    title: $t('app.stock.account.logmessage'),
  }];

  const allList = useMemo(() => {
    // return seatList.concat(clusterList);
    const clusterGroup = {
      label: $t('app.stock.account.localsys'),
      options: clusterList,
    };

    if (seatList.length > 0) {
      return [{
        label: $t('app.stock.account.seat'),
        options: seatList,
      }, clusterGroup];
    } else {
      return [clusterGroup];
    }
  }, [$t, seatList, clusterList]);
  const [destList, setDestList] = useState([]);

  const createAmountForm = useMemo(() => amountForm($t, digitUpper, allList, destList), [$t, digitUpper, allList, destList]);

  useMount(() => {
    getClusterList(Request.post, params, setClusterList, navigate, setServerMap);
  });
  useUnmount(() => cancel());

  useEffect(() => {
    if (record) {
      if (record.accountid !== oldAccountid) {
        setOldAccountid(record.accountid);
        setRespData([]);
      }

      transferReset();

      const newRecord = {
        ...record,
        availusedfund: formatDot(record.availusedfund, 2, 2),
        deposit: formatDot(record.deposit, 2),
        withdraw: formatDot(record.withdraw, 2),
      }

      aForm.current.set(newRecord);
    }
  }, [record]); // eslint-disable-line react-hooks/exhaustive-deps

  const transferReset = () => {
    aForm.current.reset();
    setDigitUpper('');
  }

  // 主席为0， 本系统为-1
  const chkSeat = chkId => {
    return seatDict[chkId] ? 0 : -1;
  }

  const formValueChange = (changedValues, values) => {
    if ('sourceclusterid' in changedValues) {
      const sourceValue = changedValues.sourceclusterid;

      if (sourceValue) {
        // 出金列表
        const copyAllList = deepCopy(allList);

        copyAllList.forEach(list => {
          const idx = list.options.findIndex(item => item.value === sourceValue);

          if (idx > -1) {
            list.options[idx].disabled = true;
          }
        });

        setDestList(copyAllList);

        // 重复清空
        const destValue = values.destclusterid;
        if (destValue && sourceValue === destValue) {
          aForm.current.set({
            destclusterid: undefined
          });
        }
      } else {
        setDestList([]);
      }
    }
    if ('amount' in changedValues) {
      const value = changedValues.amount;
      if (value !== '') {
        setDigitUpper(digitUppercase(value));
      } else {
        setDigitUpper('');
      }
    }
  }

  const amountSubmit = () => {
    aForm.current.validate().then(async values => {
      // console.log(values);
      const postData = {
        ...params,
        accountid: values.accountid,
        sourceclusterid: values.sourceclusterid,
        sourceexchangeid: chkSeat(values.sourceclusterid),
        destclusterid: values.destclusterid,
        destexchangeid: chkSeat(values.destclusterid),
        amount: values.amount,
      }

      fetchFunc(MESSAGE_TYPE.InsUpdAccount, postData, resp => {
        // message.success(CURD.StatusOkMsg);
        // onFinish();
        if (resp.errcode) {
          message.error(resp.errmessage);
        } else {
          if (resp?.length > 0) {
            const tableData = resp.map((item, idx) => ({
              ...item,
              id: idx + 1,
            }));
            setRespData(tableData);
          } else {
            setRespData([]);
          }
          onFinish();
        }
      });
    });
  }

  const { loading, run, cancel } = useRequest(amountSubmit, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  return (<>
    <Spin spinning={loading} tip="划转中……">
      <Card className="ti-table-content">
        <SimpleForm
          ref={aForm}
          formGroup
          formData={createAmountForm}
          onValuesChange={formValueChange}
          formStyle={{ maxWidth: '850px' }}
          customBtn={
            <ActionBtns btns={[
              transferBtn(run),
            ]} />
          }
        />
      </Card>
    </Spin>

    {respData.length > 0 && (
      <Card className="ti-table-content">
        <TableSimple
          columns={respColumns}
          dataSource={respData}
          isLoading={loading}
        />
      </Card>
    )}
  </>);
}

export default Transfer;
