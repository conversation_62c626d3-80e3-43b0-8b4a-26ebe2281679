export const searchForm = ($t, accountOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  rules: [{ required: true }],
  fieldProps: {
    options: accountOptions,
  }
}];

export const amountForm = ($t, digitUpper, allList, destList) => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.options.accountid'),
}, {
  valueType: 'plain',
  name: 'availusedfund',
  label: $t('app.options.mem.account.availusedfund'),
  fieldProps: {
    style: {
      fontSize: '20px',
      lineHeight: '20px',
      color: '#0081cc',
    }
  }
}, {
  valueType: 'plain',
  name: 'deposit',
  label: $t('app.options.mem.account.deposited'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#52c41a',
    }
  }
}, {
  valueType: 'plain',
  name: 'withdraw',
  label: $t('app.options.mem.account.withdrawed'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#ff4d4f',
    }
  }
}, {
  valueType: 'select',
  name: 'sourceclusterid',
  label: $t('app.stock.account.source'),
  // rules: [
  //   { required: true },
  //   // ({ getFieldValue }) => ({
  //   //   validator(_, value) {
  //   //     if (!value || getFieldValue('destclusterid') !== value) {
  //   //       return Promise.resolve();
  //   //     } else {
  //   //       return Promise.reject(new Error(
  //   //         $t('app.stock.account.err')
  //   //       ));
  //   //     }
  //   //   }
  //   // }),
  // ],
  fieldProps: {
    options: allList,
  }
}, {
  valueType: 'select',
  name: 'destclusterid',
  label: $t('app.stock.account.dest'),
  // rules: [{ required: true }],
  fieldProps: {
    options: destList.length === 0 ? allList : destList,
  }
}, {
  valueType: 'number',
  name: 'amount',
  label: $t('app.options.mem.account.amount'),
  span: 1,
  extra: digitUpper,
  rules: [{ required: true }],
  fieldProps: {
    min: 0.01,
    precision: 2,
  }
}];
