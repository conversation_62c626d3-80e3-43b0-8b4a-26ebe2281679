import { dicts, formOptions } from '@titd/publics/utils';

const { transferStatusDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, accountOptions) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountOptions,
  }
}, {
  valueType: 'picker',
  name: 'tradeday',
  label: $t('app.stock.account.tradeday'),
}, {
  valueType: 'input',
  name: 'transferno',
  label: $t('app.stock.account.transferno'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.stock.account.status'),
  fieldProps: {
    options: selectOptions(transferStatusDict),
  }
}];
