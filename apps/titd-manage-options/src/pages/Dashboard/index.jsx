import { HeadTitle } from '@titd/publics/components';
import { useFormattedMessage } from '@titd/publics/utils';

import IndexTitle from './IndexTitle';
import Summary from './Summary';
import SystemLog from './SystemLog';

const Dashboard = () => {
  const { $t } = useFormattedMessage();

  return (<>
    <HeadTitle title={$t('app.menu.dashboard', {
      defaultMsg: '首页',
    })} />

    {/* 标题 */}
    <IndexTitle />

    {/* 归总 */}
    <Summary />

    {/* 操作详情 */}
    <SystemLog />
  </>);
}

export { Dashboard };
