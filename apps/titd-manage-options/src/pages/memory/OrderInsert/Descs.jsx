import { Descriptions } from 'antd';

const { Item } = Descriptions;

const Descs = ({ title, list }) => {
  return (
    <Descriptions
      title={title}
      bordered
      size="middle"
      column={{ sm: 2, xs: 1 }}
      style={{ marginBottom: '25px' }}
    >
      {list && list.map((item, idx) => <Item label={item.label} key={idx}>{item.value}</Item>)}
    </Descriptions>
  );
}

export default Descs;
