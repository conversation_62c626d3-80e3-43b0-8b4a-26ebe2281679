import { TableFields } from '@titd/publics/components';
import { dicts } from '@titd/publics/utils';

const { TableTitle, TableBadgeDot, TableTag } = TableFields;
const { exchangeDict, sideDict, priceTypeDict, timeInforceDict, ownerTypeDict, offsetFlagDict, clientTypeDict, coveredDict, volumeDict, trigDict, tagColors, tagNormalColors } = dicts;
// import { getFloat } from '@utils/currency';

const value2Text = (dict, text) => dict[text] || text;

export const baseMap = ($t, record) => [{
  label: $t('app.options.clientid'),
  value: `${record.clientid} [${record.clientno}]`
}, {
  label: $t('app.options.instrumentid'),
  value: `${record.instrumentid} [${record.instrumentno}]`
}, {
  label: $t('app.options.exchangeid'),
  value: TableBadgeDot(record.exchangeid, exchangeDict, tagNormalColors, true),
}, {
  label: $t('app.options.order.ordersysid.long'),
  value: record.ordersysid
}, {
  label: TableTitle(
    $t('app.options.order.pricetype'),
    $t('app.options.order.pricetype.long')
  ),
  value: value2Text(priceTypeDict, record.pricetype)
}, {
  label: $t('app.options.postion.side'),
  value: TableTag(record.side, sideDict, tagColors, true),
}, {
  label: $t('app.options.order.timeinforce'),
  value: value2Text(timeInforceDict, record.timeinforce)
}, {
  label: $t('app.options.order.ownertype'),
  value: value2Text(ownerTypeDict, record.ownertype)
}, {
  label: $t('app.options.order.offsetflag'),
  value: value2Text(offsetFlagDict, record.offsetflag),
}, {
  label: $t('app.options.order.hedgeflag'),
  value: value2Text(clientTypeDict, record.hedgeflag),
}, {
  label: $t('app.options.postion.covereduncovered'),
  value: value2Text(coveredDict, record.coveredoruncovered),
}, {
  label: $t('app.options.order.volumecondition'),
  value: value2Text(volumeDict, record.volumecondition),
}, {
  label: $t('app.options.order.trigcondition'),
  value: value2Text(trigDict, record.trigcondition),
}, {
  label: TableTitle(
    $t('app.options.userid'),
    $t('app.options.rtn.userid.long')
  ),
  value: record.userid
}];

// export const rtnMap = record => {
//   const rmap = [{
//     label: '回报序号',
//     value: record.sequenceno
//   // }, {
//   //   label: '交易编码',
//   //   value: record.clientid
//   // }, {
//   //   label: '合约代码',
//   //   value: record.instrumentid
//   // }, {
//   //   label: '交易所',
//   //   value: value2Text(exchangeDict, record.exchangeid)
//   // }, {
//   //   label: TableTitle('报单价格条件', '报单价格条件限价单市价单等'),
//   //   value: value2Text(priceTypeDict, record.pricetype)
//   // }, {
//   //   label: '买卖方向',
//   //   value: value2Text(sideDict, record.side)
//   // }, {
//   //   label: '有效期类型',
//   //   value: value2Text(timeInforceDict, record.timeinforce)
//   // }, {
//   //   label: '订单所有类型',
//   //   value: value2Text(ownerTypeDict, record.ownertype)
//   }, {
//     label: '订单状态',
//     value: value2Text(ordStatusDict, record.ordstatus)
//   }, {
//     label: '价格',
//     value: record.price
//   }, {
//     label: '数量',
//     value: record.volume
//   }, {
//     label: '发生时间',
//     value: dayjs(record.transtime).format('hh:mm:ss'),
//   // }, {
//   //   label: '交易所报单编号',
//   //   value: record.ordersysid
//   }, {
//     label: '订单剩余数量',
//     value: record.leavesvolume
//   }, {
//     label: '订单撤销数量',
//     value: record.cancelvolume
//   }, {
//     label: '最小成交量',
//     value: record.minvolume,
//   }, {
//     label: '止损价',
//     value: record.stopprice,
//   // }, {
//   //   label: '开平标记',
//   //   value: value2Text(offsetFlagDict, record.offsetflag)
//   // }, {
//   //   label: '投机套保标记',
//   //   value: value2Text(clientTypeDict, record.hedgeflag)
//   // }, {
//   //   label: '备兑标签',
//   //   value: value2Text(coveredDict, record.coveredoruncovered)
//   // }, {
//   //   label: '成交量类型',
//   //   value: value2Text(volumeDict, record.volumecondition)
//   // }, {
//   //   label: '触发条件',
//   //   value: value2Text(trigDict, record.trigcondition)
//   // }, {
//   //   label: TableTitle('交易用户代码', '原始报单交易用户代码'),
//   //   value: record.userid
//   }];

//   return createColumns(rmap);
// }

// export const infoMap = record => {
//   const imap = [{
//     label: '回报序号',
//     value: record.sequenceno
//   }, {
//     label: '本地报单编号',
//     value: record.localorderno
//   // }, {
//   //   label: '交易编码',
//   //   value: record.clientid
//   // }, {
//   //   label: '合约代码',
//   //   value: record.instrumentid
//   // }, {
//   //   label: '交易所',
//   //   value: value2Text(exchangeDict, record.exchangeid)
//   // }, {
//   //   label: TableTitle('报单价格条件', '报单价格条件限价单市价单等'),
//   //   value: value2Text(priceTypeDict, record.pricetype)
//   // }, {
//   //   label: '买卖方向',
//   //   value: value2Text(sideDict, record.side)
//   // }, {
//   //   label: '开平标记',
//   //   value: value2Text(offsetFlagDict, record.offsetflag)
//   // }, {
//   //   label: '投机套保标记',
//   //   value: value2Text(clientTypeDict, record.hedgeflag)
//   // }, {
//   //   label: '有效期类型',
//   //   value: value2Text(timeInforceDict, record.timeinforce)
//   // }, {
//   //   label: '备兑标签',
//   //   value: value2Text(coveredDict, record.coveredoruncovered)
//   // }, {
//   //   label: '成交量类型',
//   //   value: value2Text(volumeDict, record.volumecondition)
//   // }, {
//   //   label: '触发条件',
//   //   value: value2Text(trigDict, record.trigcondition)
//   // }, {
//   //   label: '订单所有类型',
//   //   value: value2Text(ownerTypeDict, record.ownertype)
//   }, {
//     label: '成交价格',
//     value: record.tradeprice
//   }, {
//     label: '成交数量',
//     value: record.tradevolume
//   }, {
//     label: TableTitle('申报余量', '本次成交后申报余额数量'),
//     value: record.leavesvolume
//   }, {
//     label: '成交时间',
//     value: dayjs(record.transtime).format('hh:mm:ss'),
//   // }, {
//   //   label: '交易所报单编号',
//   //   value: record.ordersysid
//   }, {
//     label: '成交编号',
//     value: record.tradeid
//   // }, {
//   //   label: TableTitle('交易用户代码', '原始报单交易用户代码'),
//   //   value: record.userid
//   }, {
//     label: '资金账户',
//     value: record.accountid
//   }, {
//     label: '平仓盈亏',
//     value: record.closeprofit
//   }, {
//     label: '保证金',
//     value: getFloat(record.currmargin, 4)
//   }, {
//     label: '手续费',
//     value: record.commi
//   }, {
//     label: '权利金',
//     value: getFloat(record.premium, 4)
//   }];

//   return createColumns(imap);
// }
