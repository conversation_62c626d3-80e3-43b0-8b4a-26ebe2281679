import { useEffect, useState } from 'react';

import { consts, Request } from '@titd/publics/utils';

import Descs from './Descs';
import { baseMap, rtnMap, infoMap } from './map';

const { SITE_URL, MESSAGE_TYPE } = consts;

const DescGroup = props => {
  const {
    order,
    params,
  } = props;

  const [base, setBase] = useState([]);
  const [rtn, setRtn] = useState([]);
  const [info, setInfo] = useState([]);

  const getGroupData = async (record) => {
    // 基础消息
    const baseData = baseMap(record);
    setBase(baseData);

    const postData = {
      ...params,
      clientid: record.clientid,
      ordersysid: record.ordersysid,
      userid: record.userid
    };

    // 委托
    const rtnResp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryRtnOrder, params.svrid), postData);
    const { data: rtnData } = rtnResp;
    if (rtnData.length > 0) {
      const rtnData = rtnData.map(item => rtnMap(item));
      // console.log(rtnData);
      setRtn(rtnData);
    }

    // 成交
    const infoResp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryTradeInfo, params.svrid), postData);
    const { data: infoData } = infoResp;
    if (infoData.length > 0) {
      const infoData = infoData.map(item => infoMap(item));
      setInfo(infoData);
    }
  }

  useEffect(() => {
    getGroupData(order);

    return () => {
      setBase([]);
      setRtn([]);
      setInfo([]);
    }
  }, [order]); // eslint-disable-line react-hooks/exhaustive-deps

  return (<>
    <Descs title='基础消息' list={base} />
    {rtn.length > 0 && rtn?.map((item, idx) => {
      const title = idx === 0 ? '委托通知' : null;

      return <Descs title={title} list={item} key={idx} />
    })}
    {info.length > 0 && info?.map((item, idx) => {
      const title = idx === 0 ? '成交信息' : null;

      return <Descs title={title} list={item} key={idx} />
    })}
  </>);
}

export default DescGroup;
