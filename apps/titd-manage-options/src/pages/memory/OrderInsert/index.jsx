import { useRef, useState, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, App } from 'antd';
import dayjs from 'dayjs';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  currency,
  getList,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

import OrderDetail from './OrderDetail';
import TradeDetail from './TradeDetail';

const { Text } = Typography;
const { IndexColumn, TableTag, NegaNumber, TableBadgeDot, TableTitle, ErrorMsg } = TableFields;
const { DrawerForm } = forms;
const { rtnLink, infoLink } = links;
const { exchangeAllDict, sideDict, ordStatusDict, priceTypeDict, timeInforceDict, ownerTypeDict, offsetFlagDict, clientTypeDict, coveredDict, volumeDict, trigDict, ordActionTypeDict, ordStatusColor, tagNormalColors, tagColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList, getErrorCodeMsg } = getList;
const { getFloat } = currency;
const { formatTime } = dateFormat;

const OrderInsertBase = props => {
  const { paramId } = props;
  const navigate = useNavigate();
  const queryForm = useRef(null);
  const drawerRtnForm = useRef(null);
  const drawerInfoForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectRecord, setSelectRecord] = useState({});
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [errorCodeMsg, setErrorCodeMsg] = useState([]);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);
  // const { get } = useFetch('');

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.order.cmdtype'),
    dataIndex: 'cmdtype',
    render: text => ordActionTypeDict[text] || text,
  }, {
    title: $t('app.options.order.ordtime'),
    dataIndex: 'ordtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.order.exrequestid'),
    dataIndex: 'exrequestid',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      <Text>{` [${record.responsestr}]`}</Text>
    </>,
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno',
  // }, {
  //   title: '',
  //   dataIndex: 'ordsessionno',
  // }, {
  //   title: '',
  //   dataIndex: 'ordclientno',
  // }, {
  //   title: '',
  //   dataIndex: 'ordinstrumentno',
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
  }, {
    title: $t('app.options.order.ordresponsecode'),
    dataIndex: 'ordresponsecode',
    render: (text, record) => <ErrorMsg titdErrorCode={errorCodeMsg} errcode={text} exchangeid={record.exchangeid} />,
  }, {
  //   title: '交易类型',
  //   dataIndex: 'clienttype',
  //   render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  // }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.rec2send'),
    dataIndex: 'rec2send',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.rec2exrsp'),
    dataIndex: 'rec2exrsp',
    render: text => getFloat(text, 4),
  }, {
  //   title: '',
  //   dataIndex: 'ordstatus1',
  // }, {
  //   title: '',
  //   dataIndex: 'sseexreqnum',
  // }, {
    title: $t('app.options.order.cpuid'),
    dataIndex: 'cpuid',
  }, {
  //   title: '原始报单请求编号',
  //   dataIndex: 'oriexrequestid',
  // }, {
    title: $t('app.options.order.cancelvolume'),
    dataIndex: 'cancelvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.ssepartyid'),
    dataIndex: 'ssepartyid',
  }, {
    title: $t('app.options.mem.account.frozenmargin'),
    dataIndex: 'bfrozenmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'bfrozencommi',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.mem.account.frozenpremium'),
    dataIndex: 'bfrozenpremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.order.bfrozenvolume'),
    dataIndex: 'bfrozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.bthawvolume'),
    dataIndex: 'bthawvolume',
    align: 'right',
    render: text => NegaNumber(text),
  // }, {
  //   title: '买方冻结',
  //   children: [{
  //     title: '冻结保证金',
  //     dataIndex: 'bfrozenmargin',
  //   }, {
  //     title: '冻结手续费',
  //     dataIndex: 'bfrozencommi',
  //   }, {
  //     title: '冻结权利金',
  //     dataIndex: 'bfrozenpremium',
  //   }, {
  //     title: '冻结数量',
  //     dataIndex: 'bfrozenvolume',
  //   }, {
  //     title: '已解冻数量',
  //     dataIndex: 'bthawvolume',
  //   }]
  // }, {
  //   title: '卖方冻结',
  //   children: [{
  //     title: '冻结保证金',
  //     dataIndex: 'sfrozenmargin',
  //   }, {
  //     title: '冻结手续费',
  //     dataIndex: 'sfrozencommi',
  //   }, {
  //     title: '冻结权利金',
  //     dataIndex: 'sfrozenpremium',
  //   }, {
  //     title: '冻结数量',
  //     dataIndex: 'sfrozenvolume',
  //   }, {
  //     title: '已解冻数量',
  //     dataIndex: 'sthawvolume',
  //   }]
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.order.frontno'),
      $t('app.options.order.frontno.long')
    ),
    dataIndex: 'frontno',
  }, {
    title: TableTitle(
      $t('app.options.order.pricetype'),
      $t('app.options.order.pricetype.long')
    ),
    dataIndex: 'pricetype',
    render: text => priceTypeDict[text] || text,
  }, {
    title: $t('app.options.postion.side'),
    dataIndex: 'side',
    render: text => TableTag(text, sideDict, tagColors, true),
  }, {
    title: $t('app.options.order.offsetflag'),
    dataIndex: 'offsetflag',
    render: (text, record) => <>
      <Text>{offsetFlagDict[text] || text}</Text>
      {DEFAULT_CONFIGS.SHOW_CHILD && <Text type="secondary">{` (${offsetFlagDict[record.bexoffsetflag] || record.bexoffsetflag})`}</Text>}
    </>,
  }, {
    title: $t('app.options.order.hedgeflag'),
    dataIndex: 'hedgeflag',
    render: text => clientTypeDict[text] || text,
  }, {
    title: $t('app.options.order.timeinforce'),
    dataIndex: 'timeinforce',
    render: text => timeInforceDict[text] || text,
  }, {
    title: $t('app.options.postion.covereduncovered'),
    dataIndex: 'coveredoruncovered',
    render: text => coveredDict[text] || text,
  }, {
    title: $t('app.options.order.volumecondition'),
    dataIndex: 'volumecondition',
    render: text => volumeDict[text] || text,
  }, {
    title: $t('app.options.order.trigcondition'),
    dataIndex: 'trigcondition',
    render: text => trigDict[text] || text,
  }, {
    title: $t('app.options.order.volume'),
    dataIndex: 'volume',
    align: 'right',
    render: (text, record) => <>
      <Text>{NegaNumber(text)}</Text>
      {DEFAULT_CONFIGS.SHOW_CHILD && <Text type="secondary">{` (${record.bexvolume})`}</Text>}
    </>,
  }, {
    title: $t('app.options.order.minvolume'),
    dataIndex: 'minvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.price'),
    dataIndex: 'price',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.order.stopprice'),
    dataIndex: 'stopprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.order.ownertype'),
    dataIndex: 'ownertype',
    render: text => ownerTypeDict[text] || text,
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 180,
    render: (_, record) => record.ordersysid && record.ordersysid !== ' ' ? (
      <ActionLinks links={[
        rtnLink(toRtnDetail, record),
        infoLink(toInfoDetail, record),
      ]} />
    ) : null
  }];

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false), [$t, userFocus, userClientGroup]);

  useMount(() => {
    getErrorCodeMsg(Request.get, setErrorCodeMsg);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryOrderInsert, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: params.beginno + item.rspseqno - 1,
        rec2send: getFloat(item.rec2send, 4),
        rec2exrsp: getFloat(item.rec2exrsp, 4),
        bfrozenmargin: getFloat(item.bfrozenmargin, 4),
        bfrozenpremium: getFloat(item.bfrozenpremium, 4),
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0)
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
      // instrumentid: values.instrumentid?.trim(),
      begtime: values.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
        !searchParams.userrange && (searchParams.userid = userclient[1]);
      } else {
        searchParams.clientid = values.userclient;
      }

      delete searchParams.userclient;
      delete searchParams.userrange;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const toRtnDetail = record => {
    drawerRtnForm.current.show(`${$t('app.menu.mem.rtn.rtnorder')}: ${record.ordersysid}`);
    setSelectRecord(record);
  }

  const toInfoDetail = record => {
    drawerInfoForm.current.show(`${$t('app.menu.mem.rtn.rtntrade')}: ${record.ordersysid}`);
    setSelectRecord(record);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.ord.orderinsert', {
        defaultMsg: '期权报单',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        initValues: {
          userrange: 1,
          bssystemflag: 0,
        },
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    {/* 委托回报 */}
    <DrawerForm
      ref={drawerRtnForm}
      formData={[]}
    >
      <OrderDetail order={selectRecord} params={defaultParams} />
    </DrawerForm>

    {/* 成交回报 */}
    <DrawerForm
      ref={drawerInfoForm}
      formData={[]}
    >
      <TradeDetail order={selectRecord} params={defaultParams} />
    </DrawerForm>
  </>);
}

const OrderInsert = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <OrderInsertBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { OrderInsert };
