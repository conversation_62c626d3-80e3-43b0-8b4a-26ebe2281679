// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

import { keyIcon, copyIcon, hideIcon, showIcon } from './data-dom';

const { TableTitle } = TableFields;
const { userStatusDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, userFocus, userOptions) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.userid'),
  fieldProps: {
    onFocus: userFocus,
    options: userOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}];

export const addForm = $t => [{
  valueType: 'text',
  name: 'userid',
  label: $t('app.options.userid'),
}, {
  valueType: 'select',
  name: 'status',
  label: $t('app.options.db.userinfo.status'),
  fieldProps: {
    allowClear: false,
    options: selectOptions(userStatusDict),
  }
}, {
  valueType: 'number',
  name: 'tradeflow',
  label: TableTitle(
    $t('app.options.db.userinfo.tradeflow'),
    $t('app.options.db.userinfo.tradeflow.long')
  ),
}, {
  valueType: 'number',
  name: 'maxloginsuccess',
  label: TableTitle(
    $t('app.options.mem.userinfo.maxloginsuccess'),
    $t('app.options.mem.userinfo.maxloginsuccess.long')
  ),
}, {
  valueType: 'number',
  name: 'maxloginfailed',
  label: TableTitle(
    $t('app.options.mem.userinfo.maxloginfailed'),
    $t('app.options.mem.userinfo.maxloginfailed.long')
  ),
}, {
  valueType: 'number',
  name: 'endno',
  label: $t('app.options.mem.userinfo.cancelmax'),
}];

export const resetPwdForm = $t => [{
  valueType: 'text',
  name: 'userid',
  label: $t('app.options.userid'),
}, {
  valueType: 'password',
  name: 'newpassword',
  label: $t('app.options.newpassword'),
  rules: [{ required: true }],
  fieldProps: {
    prefix: keyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}, {
  valueType: 'password',
  name: 'repassword',
  label: $t('app.options.repassword'),
  rules: [
    { required: true },
    ({ getFieldValue }) => ({
      validator(_, value) {
        if (!value || getFieldValue('newpassword') === value) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error(
            $t('app.options.repassword.error')
          ));
        }
      }
    }),
  ],
  fieldProps: {
    prefix: copyIcon,
    iconRender: visible => (visible ? showIcon : hideIcon),
  }
}];
