// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { exchangeDict, clientChgStatusDict } = dicts;
const { selectOptions, selectExchange } = formOptions;

export const searchForm = ($t, userFocus, userClientOptions, loading, exchanges) => [{
  valueType: 'autocomplete',
  name: 'userclient',
  label: TableTitle(
    $t('app.options.userclient'),
    $t('app.options.userclient.tip'),
  ),
  fieldProps: {
    onFocus: userFocus,
    options: userClientOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'input',
  name: 'productid',
  label: $t('app.options.productid'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}];

export const chgStatusForm = $t => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    open: false,
    bordered: false,
    allowClear: false,
    showArrow: false,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'text',
  name: 'clientid',
  label: $t('app.options.clientid'),
}, {
  valueType: 'text',
  name: 'productid',
  label: $t('app.options.productid'),
}, {
  valueType: 'radio',
  name: 'tdstatus',
  label: $t('app.options.risk.tdstatus'),
  rules: [{ required: true }],
  fieldProps: {
    optionType: 'button',
    buttonStyle: 'solid',
    options: selectOptions(clientChgStatusDict)
  }
}];
