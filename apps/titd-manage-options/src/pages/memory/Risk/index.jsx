import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  links,
} from '@titd/publics/components';
import { searchForm, chgStatusForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  currency,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTag, TableBadgeDot } = TableFields;
const { ModalForm } = forms;
const { chgStatusLink } = links;
const { exchangeAllDict, clientStatusDict, tagNormalColors, tagColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList } = getList;
const { formatDot } = currency;

const RiskBase = props => {
  const { paramId } = props;

  const queryForm = useRef(null);
  const modalForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = exchangeList && exchangeList[paramId];

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
  }, {
  //   title: $t('app.options.instrumentid'),
  //   dataIndex: 'instrumentid',
  //   render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  // }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.risk.tdstatus'),
    dataIndex: 'tdstatus',
    render: text => TableTag(text, clientStatusDict, tagColors),
  }, {
    title: $t('app.options.risk.buyhold'),
    dataIndex: 'buyhold',
    align: 'right',
    render: text => volumeShow(text)
  }, {
    title: $t('app.options.risk.tothold'),
    dataIndex: 'tothold',
    align: 'right',
    render: text => volumeShow(text)
  }, {
    title: $t('app.options.risk.today'),
    dataIndex: 'today',
    align: 'right',
    render: text => volumeShow(text)
  }, {
    title: $t('app.options.risk.amount'),
    dataIndex: 'amount',
    align: 'right',
    render: text => volumeShow(text)
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 100,
    render: (_, record) => record.tdstatus !== 3 && (
      <ActionLinks links={[
        chgStatusLink(toChgStatus, record),
      ]} />
    )
  }];

  const volumeShow = (text) => {
    return (<>
      <Text strong>{text[0]}</Text>
      {' / '}
      <Text type="secondary">{text[1]}</Text>
    </>);
  }

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false, exchanges), [$t, userFocus, userClientGroup, exchanges]);
  const createChgStatusForm = useMemo(() => chgStatusForm($t), [$t]);

  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryRisk, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
        buyhold: [item.buyholdvolume, item.mbuyholdvolume],
        tothold: [item.totholdvolume, item.mtotholdvolume],
        today: [item.todayvolume, item.mtodayvolume],
        amount: [formatDot(item.buypremium), formatDot(item.longamount)]
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0)
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    }
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const toChgStatus = (record) => {
    // console.log(record);
    modalForm.current.show('更改交易状态', record);
  }

  const chgSubmit = (form) => {
    // console.log(form);
    const postData = {
      ...defaultParams,
      ...form,
      status: form.tdstatus,
    }

    delete postData.tdstatus;

    fetchFunc(MESSAGE_TYPE.MemUpdClientStatus, postData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.user.risk', {
        defaultMsg: '风控信息',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          exchangeid: exchanges?.length === 1 ? Number(exchanges[0]) : null,
        },
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />

    <ModalForm
      ref={modalForm}
      onOk={chgSubmit}
      formData={createChgStatusForm}
    />
  </>);
}

const Risk = props => {
  const param = useParams();
  const paramId = param?.id || '';
  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <RiskBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Risk };
