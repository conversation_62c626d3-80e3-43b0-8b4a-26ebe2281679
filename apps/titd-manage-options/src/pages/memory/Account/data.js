import { TableFields } from '@titd/publics/components';

const { TableTitle } = TableFields;

export const searchForm = ($t, accountGroup) => [{
  valueType: 'autocomplete',
  name: 'accountid',
  label: $t('app.options.accountid'),
  fieldProps: {
    options: accountGroup,
  }
}];

export const addForm = ($t, isModify) => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.options.accountid'),
}, {
  valueType: 'text',
  name: 'prebalance',
  label: $t('app.options.mem.account.prebalance'),
}, {
  valueType: 'text',
  name: 'distribfund',
  label: $t('app.options.mem.account.distribfund'),
}, {
  valueType: 'text',
  name: 'availusedfund',
  label: $t('app.options.mem.account.availusedfund'),
}, {
  valueType: 'number',
  name: 'commi',
  label: $t('app.options.mem.account.commi'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify
  }
}, {
  valueType: 'number',
  name: 'futmargin',
  label: TableTitle(
    $t('app.options.mem.account.futmargin'),
    $t('app.options.mem.account.futmargin.long')
  ),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify
  }
}, {
  valueType: 'number',
  name: 'optmargin',
  label: TableTitle(
    $t('app.options.mem.account.optmargin'),
    $t('app.options.mem.account.optmargin.long')
  ),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify
  }
}, {
  valueType: 'number',
  name: 'combmargin',
  label: TableTitle(
    $t('app.options.mem.account.combmargin'),
    $t('app.options.mem.account.combmargin.long')
  ),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify
  }
}, {
  valueType: 'number',
  name: 'closeprofit',
  label: $t('app.options.mem.account.closeprofit'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify,
    min: Number.MIN_SAFE_INTEGER
  }
}, {
  valueType: 'number',
  name: 'posiprofit',
  label: $t('app.options.mem.account.posiprofit'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify,
    min: Number.MIN_SAFE_INTEGER
  }
}, {
  valueType: 'number',
  name: 'premium',
  label: $t('app.options.mem.account.premium'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify,
    min: Number.MIN_SAFE_INTEGER
  }
}, {
  valueType: 'text',
  name: 'deposit',
  label: $t('app.options.mem.account.deposit'),
}, {
  valueType: 'text',
  name: 'withdraw',
  label: $t('app.options.mem.account.withdraw'),
}, {
  valueType: 'number',
  name: 'frozenmargin',
  label: $t('app.options.mem.account.frozenmargin'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify
  }
}, {
  valueType: 'number',
  name: 'frozenpremium',
  label: $t('app.options.mem.account.frozenpremium'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify,
    min: Number.MIN_SAFE_INTEGER
  }
}, {
  valueType: 'number',
  name: 'frozencommi',
  label: $t('app.options.mem.account.frozencommi'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify,
    min: Number.MIN_SAFE_INTEGER
  }
}, {
  valueType: 'number',
  name: 'entryfees',
  label: $t('app.options.mem.account.entryfees'),
  fieldProps: {
    readOnly: !isModify,
    bordered: isModify
  }
}, {
  valueType: 'number',
  name: 'buypremium',
  label: TableTitle(
    $t('app.options.mem.account.buypremium'),
    $t('app.options.mem.account.buypremium.long')
  ),
  fieldProps: {
    readOnly: true,
    bordered: false
  }
}];

export const amountForm = ($t, digitUpper) => [{
  valueType: 'text',
  name: 'accountid',
  label: $t('app.options.accountid'),
}, {
  valueType: 'plain',
  name: 'availusedfund',
  label: $t('app.options.mem.account.availusedfund'),
  fieldProps: {
    style: {
      fontSize: '20px',
      lineHeight: '20px',
      color: '#0081cc',
    }
  }
}, {
  valueType: 'plain',
  name: 'deposit',
  label: $t('app.options.mem.account.deposited'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#52c41a',
    }
  }
}, {
  valueType: 'plain',
  name: 'withdraw',
  label: $t('app.options.mem.account.withdrawed'),
  fieldProps: {
    style: {
      fontSize: '18px',
      lineHeight: '18px',
      color: '#ff4d4f',
    }
  }
}, {
  valueType: 'number',
  name: 'amount',
  label: $t('app.options.mem.account.amount'),
  extra: digitUpper,
  rules: [{ required: true }],
}];
