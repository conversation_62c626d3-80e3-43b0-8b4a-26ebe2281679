import { useRef, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, Space, Button, App } from 'antd';
import {
  EditOutlined,
  CloseOutlined,
  CheckOutlined,
} from '@ant-design/icons';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
  exportDownModal,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  currency,
  updateRowData,
  errorResp,
} from '@titd/publics/utils';

import Amount from './Amount';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableTitle } = TableFields;
const { DrawerForm } = forms;
const { detailLink } = links;
const { exportAllBtn } = btns;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getFloat } = currency;

const AccountBase = props => {
  const { paramId } = props;

  const drawerForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectKeys, setSelectKeys] = useState([1]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.mem.account.prebalance'),
    dataIndex: 'prebalance',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.distribfund'),
    dataIndex: 'distribfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.availusedfund'),
    dataIndex: 'availusedfund',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.dailyprofit'),
    dataIndex: 'dailyprofit',
    align: 'right',
    render: text => <Text strong>{NegaNumber(text, 2, 2)}</Text>,
  }, {
    title: $t('app.options.mem.account.commi'),
    dataIndex: 'commi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
  //   title: TableTitle(
  //     $t('app.options.mem.account.futmargin'),
  //     $t('app.options.mem.account.futmargin.long')
  //   ),
  //   dataIndex: 'futmargin',
  //   align: 'right',
  //   render: text => NegaNumber(text, 2, 2),
  // }, {
    title: TableTitle(
      $t('app.options.mem.account.optmargin'),
      $t('app.options.mem.account.optmargin.long')
    ),
    dataIndex: 'optmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.options.mem.account.combmargin'),
      $t('app.options.mem.account.combmargin.long')
    ),
    dataIndex: 'combmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.options.mem.account.recvmargin'),
      $t('app.options.mem.account.recvmargin.long')
    ),
    dataIndex: 'recvmargin',
    align: 'right',
    render: text => <Text strong>{NegaNumber(text, 2, 2)}</Text>,
  }, {
    title: $t('app.options.mem.account.closeprofit'),
    dataIndex: 'closeprofit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.posiprofit'),
    dataIndex: 'posiprofit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.premium'),
    dataIndex: 'premium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.deposit'),
    dataIndex: 'deposit',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.withdraw'),
    dataIndex: 'withdraw',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozenmargin'),
    dataIndex: 'frozenmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozenpremium'),
    dataIndex: 'frozenpremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'frozencommi',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.entryfees'),
    dataIndex: 'entryfees',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: TableTitle(
      $t('app.options.mem.account.buypremium'),
      $t('app.options.mem.account.buypremium.long')
    ),
    dataIndex: 'buypremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: TABLE_WIDTH.ID,
    render: (_, record) => (
      <ActionLinks links={[
        detailLink(toDetail, record),
      ]} />
    )
  }];

  const [accountGroup, setAccountGroup] = useState([]);
  const [isModify, setIsModify] = useState(false);

  const createSearchForm = useMemo(() => searchForm($t, accountGroup), [$t, accountGroup]);
  const createAddForm = useMemo(() => addForm($t, isModify), [$t, isModify]);

  useUnmount(() => cancel());

  const caclProfit = item => {
    // 盈亏 = 结算准备金<可用资金> + 总保证金<当前期货保证金总额+当前期权保证金总额-当前保证金优惠总额> - 期权权利金收支<权利金收入-权利金支出> - (入金 - 出金) + 总冻结<冻结保证金+冻结权利金+冻结手续费> - 本系统分配资金
    // 可用资金 + 实收保证金 - (入金 - 出金) + 总冻结<冻结保证金+冻结权利金+冻结手续费> - 本系统分配资金

    return item.availusedfund + (item.optmargin - item.combmargin) - (item.deposit - item.withdraw) + (item.frozenmargin + item.frozenpremium + item.frozencommi) - item.distribfund;
  }

  const getTableData = async ({ showAuto, noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryAccount, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (showAuto) {
      if (respData?.length > 0) {
        const accountIds = respData.map(item => ({
          label: item.accountid,
          value: item.accountid,
        }));
        setAccountGroup(accountIds);
      } else {
        setAccountGroup([]);
      }
    }

    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
        // 实收保证金
        recvmargin: getFloat(item.optmargin - item.combmargin, 4),
        // 每日盈亏
        dailyprofit: caclProfit(item),
      }));

      setTotal(respData[0].rsptotnum);
      // setSelectKeys([1]);
      return tableData;
    } else {
      setTotal(0);
      setSelectKeys([]);
      return [];
    }
  }

  const {
    data: dataSource = [],
    params: prevParams,
    loading,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      showAuto: true,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    }
  });

  const selectRecord = useMemo(() => {
    if (selectKeys.length > 0 && dataSource.length > 0) {
      return dataSource.find(i => i.id === selectKeys[0]);
    }

    return null;
  }, [selectKeys, dataSource]);

  const getNewestData = async record => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryAccount), {
      ...defaultParams,
      accountid: record.accountid,
    });

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const item = respData[0];
      return {
        ...item,
        id: item.rspseqno,
        prebalance: getFloat(item.prebalance, 4),
        distribfund: getFloat(item.distribfund, 4),
        availusedfund: getFloat(item.availusedfund, 4),
        commi: getFloat(item.commi, 4),
        futmargin: getFloat(item.futmargin, 4),
        optmargin: getFloat(item.optmargin, 4),
        combmargin: getFloat(item.combmargin, 4),
        closeprofit: getFloat(item.closeprofit, 4),
        posiprofit: getFloat(item.posiprofit, 4),
        premium: getFloat(item.premium, 4),
        deposit: getFloat(item.deposit, 4),
        withdraw: getFloat(item.withdraw, 4),
        frozenmargin: getFloat(item.frozenmargin, 4),
        frozenpremium: getFloat(item.frozenpremium, 4),
        frozencommi: getFloat(item.frozencommi, 4),
        entryfees: getFloat(item.entryfees, 4),
        buypremium: getFloat(item.buypremium, 4),
        // 实收保证金
        recvmargin: getFloat(item.optmargin - item.combmargin, 4),
        // 每日盈亏
        dailyprofit: caclProfit(item),
      };
    }

    return null;
  }

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const toDetail = async record => {
    // console.log('Detail', record);
    const newRecord = await getNewestData(record);

    if (newRecord) {
      drawerForm.current.show(newRecord.accountid, newRecord);

      // 更新本条数据
      const newData = updateRowData(dataSource, {
        ...newRecord,
        id: record.id,
      });
      mutate(newData);
    }
  }
  const toCancel = () => {
    drawerForm.current.set(selectRecord);
    setIsModify(false);
  }
  const toSure = () => {
    drawerForm.current.submit(addSubmit);
    setIsModify(false);
  }

  const toExport = () => {
    fetchFunc(MESSAGE_TYPE.DownloadAccount, defaultParams, resp => {
      // console.log(resp);
      if (resp.errmessage) {
        exportDownModal(resp.errmessage);
      } else {
        message.error('导出失败');
      }
    });
  }

  const addSubmit = async form => {
    const postData = {
      ...defaultParams,
      ...form,
    }

    fetchFunc(MESSAGE_TYPE.MemUpdAccount, postData, () => {
        message.success(CURD.Update + CURD.StatusOkMsg);
        onRefresh();
      });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.user.account', {
        defaultMsg: '资金信息',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        actionBtns: [
          exportAllBtn(toExport),
        ],
        onRefresh: onRefresh,
        page: page,
        total: total,
        pageChange: pageChange,
        rowClickable: true,
        selectionType: 'radio',
        selectKeys: selectKeys,
        setSelectKeys: (keys) => {
          setSelectKeys(keys);
        },
      }}
    />

    {/* 出入金 */}
    {selectRecord && (
      <Amount
        params={defaultParams}
        record={selectRecord}
        onSuccess={onRefresh}
      />
    )}

    {/* 资金信息修改 */}
    <DrawerForm
      ref={drawerForm}
      formData={createAddForm}
      onClose={() => setIsModify(false)}
    >
      <div style={{
        borderTop: '1px solid #eee',
        paddingTop: '20px',
        textAlign: 'center'
      }}>
        {isModify ? (
          <Space>
            <Button danger onClick={toCancel}><CloseOutlined /> {$t('app.general.cancel')}</Button>
            <Button type="primary" onClick={toSure}><CheckOutlined /> {$t('app.general.sure')}</Button>
          </Space>
        ) : (
          <Button type="primary" ghost onClick={() => setIsModify(true)}><EditOutlined /> {$t('app.general.update')}</Button>
        )}
      </div>
    </DrawerForm>
  </>);
}

const Account = props => {
  const param = useParams();

  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <AccountBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Account };
