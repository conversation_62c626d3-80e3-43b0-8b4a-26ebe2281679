import { useRef, useState, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Card, Row, Col, Typography, Spin, Empty, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  TableSimple,
  TableFields,
  forms,
  HeadTitle,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  tools,
  getList,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

const { Title, Text } = Typography;
const { IndexColumn, NegaNumber } = TableFields;
const { QueryFilter } = forms;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList } = getList;
const { formatTime } = dateFormat;

const SelfTradeBase = props => {
  const { paramId } = props;

  const queryForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = exchangeList && exchangeList[paramId];

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: DEFAULT_CONFIGS.LIMIT,
    svrid: paramId,
  }), [paramId]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  // const columns = [{
  //   dataIndex: 'rspseqno',
  //   width: TABLE_WIDTH.ID,
  //   align: 'center',
  //   render: text => <IndexColumn border>{text}</IndexColumn>,
  // }, {
  //   title: '交易编码',
  //   dataIndex: 'clientid',
  //   render: text => <Text strong>{text}</Text>
  // }, {
  //   title: '合约代码',
  //   dataIndex: 'instrumentid',
  // }, {
  //   title: '交易所',
  //   dataIndex: 'exchangeid',
  //   render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  // }];

  const columns = [{
    title: $t('app.options.order.exrequestid'),
    dataIndex: 'exrequestid',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.self.price'),
    dataIndex: 'price',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderid',
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.self.ordertime'),
    dataIndex: 'ordertime',
    render: text => formatTime(text),

  }];

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false, exchanges), [$t, userFocus, userClientGroup, exchanges]);

  useUnmount(() => cancel());

  const getTableData = async params => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemSelfTrade, params.svrid), params);

    const { data: respData } = resp;
    if (respData?.length > 0) {
      return respData[0];
    } else {
      return null;
    }
  }

  const {
    data: dataMap = null,
    loading: isLoading,
    run,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onSearch = values => {
    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    run(null);
  }

  return (<>
    <HeadTitle title={$t('app.menu.mem.err.selftrade', {
      defaultMsg: '自成交信息',
    })} />

    <div style={{
      padding: '24px 24px 0',
      marginBottom: '16px',
      backgroundColor: 'white',
    }}>
      <QueryFilter
        ref={queryForm}
        formData={createSearchForm}
        isLoading={isLoading}
        onSearch={onSearch}
        onReset={onReset}
        initValues={{
          exchangeid: exchanges?.length === 1 ? Number(exchanges[0]) : null,
        }}
      />
    </div>
    <Spin spinning={isLoading}>
      <Card>
        {dataMap ? (
          <Row gutter={16}>
            <Col sm={24} md={12}>
              <TableSimple
                title={() => (<Title level={4}>
                  {$t('app.options.self.buymax') + '：'}
                  {NegaNumber(dataMap.buymax)}
                </Title>)}
                customKey={'exrequestid'}
                columns={columns}
                dataSource={dataMap.buyitem || []}
              />
            </Col>
            <Col sm={24} md={12}>
              <TableSimple
                title={() => (<Title level={4}>
                  {$t('app.options.self.sellmin') + '：'}
                  {NegaNumber(dataMap.sellmin)}
                </Title>)}
                customKey={'exrequestid'}
                columns={columns}
                dataSource={dataMap.sellitem || []}
              />
            </Col>
          </Row>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
      </Card>
    </Spin>
  </>);
}

const SelfTrade = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <SelfTradeBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { SelfTrade };
