import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { exchangeDict } = dicts;
const { selectExchange } = formOptions;

export const searchForm = ($t, userFocus, userClientOptions, loading, exchanges, isRequired = true) => [{
//   valueType: 'select',
//   name: 'userid',
//   label: '交易用户代码',
//   tooltip: '筛选“交易编码”用，不做实际查询',
//   fieldProps: {
//     showSearch: true,
//     onFocus: userFocus,
//     options: userOptions,
//   }
// }, {
//   valueType: 'select',
//   name: 'clientid',
//   label: '交易编码',
//   rules: [{ required: true }],
//   fieldProps: {
//     showSearch: true,
//     options: clientOptions,
//   }
  valueType: 'autocomplete',
  name: 'userclient',
  label: TableTitle(
    $t('app.options.userclient'),
    $t('app.options.userclient.tip'),
  ),
  rules: [{
    required: isRequired,
    message: $t('app.general.please') + $t('app.options.userclient'),
  }],
  fieldProps: {
    onFocus: userFocus,
    options: userClientOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}];

// export const addForm = [{
//   children: [{
//     valueType: 'input',
//     name: 'clientid',
//     label: '交易编码',
//     fieldProps: {
//       readOnly: true,
//       bordered: false,
//     }
//   }, {
//     valueType: 'select',
//     name: 'exchangeid',
//     label: '交易所',
//     fieldProps: {
//       open: false,
//       bordered: false,
//       allowClear: false,
//       showArrow: false,
//       options: selectOptions(exchangeDict)
//     }
//   }, {
//     valueType: 'input',
//     name: 'instrumentid',
//     label: '合约代码',
//     fieldProps: {
//       readOnly: true,
//       bordered: false,
//     }
//   }]
// }, {
//   children: [{
//     valueType: 'number',
//     name: 'openbymoney',
//     label: '开仓手续费率'
//   }, {
//     valueType: 'number',
//     name: 'openbyvolume',
//     label: '开仓手续费'
//   }, {
//     valueType: 'number',
//     name: 'closebymoney',
//     label: '平仓手续费率'
//   }, {
//     valueType: 'number',
//     name: 'closebyvolume',
//     label: '平仓手续费'
//   }, {
//     valueType: 'number',
//     name: 'closetodaybymoney',
//     label: '平今手续费率'
//   }, {
//     valueType: 'number',
//     name: 'closetodaybyvolume',
//     label: '平今手续费'
//   }, {
//     valueType: 'number',
//     name: 'openmax',
//     label: '开仓单笔最高手续费'
//   }, {
//     valueType: 'number',
//     name: 'openmin',
//     label: '开仓单笔最低手续费'
//   }, {
//     valueType: 'number',
//     name: 'closemax',
//     label: '平仓单笔最高手续费'
//   }, {
//     valueType: 'number',
//     name: 'closemin',
//     label: '平仓单笔最低手续费'
//   }, {
//     valueType: 'number',
//     name: 'margin',
//     label: '保证金'
//   }, {
//     valueType: 'number',
//     name: 'marginratioparam1',
//     label: '保证金参数1'
//   }, {
//     valueType: 'number',
//     name: 'marginratioparam2',
//     label: '保证金参数2'
//   }, {
//     valueType: 'number',
//     name: 'marginrate',
//     label: TableTitle('收取比例', '在交易所基础上收取的比例')
//   }, {
//     valueType: 'number',
//     name: 'longbymoney',
//     label: '多头保证金率'
//   }, {
//     valueType: 'number',
//     name: 'longbyvolume',
//     label: '多头保证金费'
//   }, {
//     valueType: 'number',
//     name: 'shortbymoney',
//     label: '空头保证金率'
//   }, {
//     valueType: 'number',
//     name: 'shortbyvolume',
//     label: '空头保证金费'
//   }]
// }];
