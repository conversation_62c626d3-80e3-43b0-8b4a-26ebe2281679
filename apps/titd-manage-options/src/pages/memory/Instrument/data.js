import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict, productShortDict, optionsDict } = dicts;
const { selectObjOptions, selectExchange } = formOptions;

export const searchForm = ($t, exchanges) => [{
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}, {
  valueType: 'input',
  name: 'productid',
  label: $t('app.options.productid'),
}, {
  valueType: 'input',
  name: 'underlyinginstrid',
  label: $t('app.options.underlyinginstrid'),
}, {
  valueType: 'select',
  name: 'producttype',
  label: $t('app.options.instrument.producttype'),
  fieldProps: {
    options: selectObjOptions(productShortDict, {isCode: true})
  }
}, {
  valueType: 'select',
  name: 'optionstype',
  label: $t('app.options.instrument.optionstype'),
  fieldProps: {
    options: selectObjOptions(optionsDict, {isCode: true})
  }
}, {
//   valueType: 'select',
//   name: 'tradingright',
//   label: '交易权限',
//   fieldProps: {
//     options: selectOptions(tradingRightDict)
//   }
// }, {
  valueType: 'picker',
  name: 'expiredate',
  label: $t('app.options.instrument.expiredate'),
}];
