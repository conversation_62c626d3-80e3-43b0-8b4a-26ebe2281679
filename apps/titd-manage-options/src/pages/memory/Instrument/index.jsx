import { useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, App } from 'antd';
import dayjs from 'dayjs';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { TableTag, IndexColumn, NegaNumber, TableBadgeDot, TableTitle } = TableFields;
const { exchangeAllDict, productDict, optionsDict, yesNoDict, tagNormalColors, yesNoColor } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { formatDate } = dateFormat;

const InstrumentBase = props => {
  const { paramId } = props;
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = useMemo(() => exchangeList && exchangeList[paramId], [exchangeList, paramId]);
  const defaultExchange = exchanges?.length === 1 ? Number(exchanges[0]) : undefined;

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);

  const columns = [{
    dataIndex: 'id',
    fixed: 'left',
    width: TABLE_WIDTH.ID,
    align: 'center',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    fixed: 'left',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.instrumentno}]`}
    </>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.instrument.instrumentname'),
    dataIndex: 'instrumentname',
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
  }, {
    title: $t('app.options.underlyinginstrid'),
    dataIndex: 'underlyinginstrid',
  }, {
    title: $t('app.options.instrument.producttype'),
    dataIndex: 'producttype',
    render: text => productDict[String.fromCharCode(text)] || text,
  }, {
    title: $t('app.options.instrument.optionstype'),
    dataIndex: 'optionstype',
    render: text => text === 0 ? '-' : optionsDict[String.fromCharCode(text)] || text,
  // }, {
  //   title: '合约数量乘数',
  //   dataIndex: 'volumemultiple',
  }, {
    title: $t('app.options.instrument.lastprice'),
    dataIndex: 'lastprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.settlementprice'),
    dataIndex: 'settlementprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: TableTitle(
      $t('app.options.instrument.marginprice'),
      $t('app.options.instrument.marginprice.long')
    ),
    dataIndex: 'marginprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: TableTitle(
      $t('app.options.instrument.undermarginprice'),
      $t('app.options.instrument.undermarginprice.long')
    ),
    dataIndex: 'undermarginprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.upperlimitprice'),
    dataIndex: 'upperlimitprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.lowerlimitprice'),
    dataIndex: 'lowerlimitprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.volumemultiple'),
    dataIndex: 'volumemultiple',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.stockprice'),
    dataIndex: 'stockprice',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.deliveryyear'),
    dataIndex: 'deliveryyear',
    align: 'right',
    render: text => text || '-',
  }, {
    title: $t('app.options.instrument.deliverymonth'),
    dataIndex: 'deliverymonth',
    align: 'right',
    render: text => text || '-',
  }, {
    title: $t('app.options.instrument.advancemonth'),
    dataIndex: 'advancemonth',
    align: 'right',
    render: text => text || '-',
  // }, {
  //   title: '交易权限',
  //   dataIndex: 'tradingright',
  //   render: text => TableTag(text, yesNoColor),
  }, {
    title: $t('app.options.instrument.createdate'),
    dataIndex: 'createdate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.opendate'),
    dataIndex: 'opendate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.expiredate'),
    dataIndex: 'expiredate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.startdelivdate'),
    dataIndex: 'startdelivdate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.enddelivdate'),
    dataIndex: 'enddelivdate',
    render: text => formatDate(text),
  }, {
    title: $t('app.options.instrument.basisprice'),
    dataIndex: 'basisprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.strikeprice'),
    dataIndex: 'strikeprice',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.maxmarketordervolume'),
    dataIndex: 'maxmarketordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.minmarketordervolume'),
    dataIndex: 'minmarketordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.maxlimitordervolume'),
    dataIndex: 'maxlimitordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.minlimitordervolume'),
    dataIndex: 'minlimitordervolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.instrument.pricetick'),
    dataIndex: 'pricetick',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.instrument.allowdelivpersonopen'),
    dataIndex: 'allowdelivpersonopen',
  }, {
    title: TableTitle(
      $t('app.options.instrument.tradedays'),
      $t('app.options.instrument.tradedays.long')
    ),
    dataIndex: 'tradedays',
    align: 'right',
  }, {
    title: $t('app.options.instrument.tplus1'),
    dataIndex: 'tplus1',
    render: text => TableTag(text, yesNoDict, yesNoColor),
  }];

  const createSearchForm = useMemo(() => searchForm($t, exchanges), [$t, exchanges]);

  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryInstrument, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [{
      ...defaultParams,
      exchangeid: defaultExchange,
    }],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
      expiredate: values?.expiredate ? dayjs(values.expiredate).format('YYYYMMDD') : '',
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.instrument', {
        defaultMsg: '合约信息',
      })}
      filterProps={{
        formData: createSearchForm,
        isLoading: loading,
        initValues: {
          exchangeid: defaultExchange,
        },
        resetSearch: false,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const Instrument = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <InstrumentBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Instrument };
