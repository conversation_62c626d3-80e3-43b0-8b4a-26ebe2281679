// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { consts, dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { ordAcitonStatusDict, ordActionExtraDict } = dicts;
const { selectOptions, selectObjOptions } = formOptions;
const { DEFAULT_CONFIGS } = consts;

export const searchForm = ($t, userFocus, userOptions, loading, isRequired = true) => {
  const extraOptions = DEFAULT_CONFIGS.SHOW_CHILD ? selectObjOptions(ordActionExtraDict, { isNum: true }) : [];

  return [{
    valueType: 'autocomplete',
    name: 'userid',
    label: $t('app.options.userid'),
    rules: [{ required: isRequired }],
    fieldProps: {
      onFocus: userFocus,
      options: userOptions,
      // notFoundContent: loading && (<Spin size="small" />),
    }
  }, {
  //   valueType: 'select',
  //   name: 'clientid',
  //   label: '交易编码',
  //   fieldProps: {
  //     options: clientOptions
  //   }
  // }, {
  //   valueType: 'input',
  //   name: 'instrumentid',
  //   label: '合约代码'
  // }, {
  //   valueType: 'input',
  //   name: 'ordersysid',
  //   label: TableTitle('报单编号', '交易所报单编号'),
  // }, {
    valueType: 'select',
    name: 'ordstatus',
    label: $t('app.options.order.cancel.ordstatus'),
    fieldProps: {
      options: selectOptions(ordAcitonStatusDict).concat(extraOptions)
    }
  }, {
    valueType: 'number',
    name: 'orilocalorderno',
    label: $t('app.options.order.orilocalorderno'),
  }, {
    valueType: 'number',
    name: 'orisessionno',
    label: TableTitle(
      $t('app.options.order.orisessionno'),
      $t('app.options.order.orisessionno.long'),
    )
  }, {
  //   valueType: 'number',
  //   name: 'oriexrequestid',
  //   label: TableTitle(
  //     $t('app.options.order.oriexrequestid'),
  //     $t('app.options.order.oriexrequestid.long'),
  //   )
  // }, {
    valueType: 'input',
    name: 'ordersysid',
    label: $t('app.options.order.oriordersysid'),
  }, {
    valueType: 'picker',
    name: 'begtime',
    label: $t('app.options.order.begtime'),
    fieldProps: {
      type: 'time',
    }
  }, {
    valueType: 'picker',
    name: 'endtime',
    label: $t('app.options.order.endtime'),
    fieldProps: {
      type: 'time'
    }
  }];
}
