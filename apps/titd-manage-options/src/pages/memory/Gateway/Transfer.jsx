import { useState, useMemo, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Transfer, Divider, Typography } from 'antd';

import { useFormattedMessage } from '@titd/publics/utils';

const { Text } = Typography;

const UserTransfer = (props, ref) => {
  const {
    userOptions,
    record
  } = props;

  const { $t } = useFormattedMessage();

  const [checkUsers, setCheckUsers] = useState([]);
  const [selectUsers, setSelectUsers] = useState([]);

  const userData = useMemo(() => userOptions.map(item => ({
    key: item.value,
    title: item.label,
    disabled: record.userid?.includes(item.value),
  })), [userOptions, record]);

  useEffect(() => {
    if (record) {
      setCheckUsers(record.userid);
    }
  }, [record]);

  useImperativeHandle(ref, () => ({
    getValues: () => {
      // console.log('values', checkUsers);
      return checkUsers;
    },
    // setRole: () => {
    //   console.log('setRole');
    // },
  }));

  const filterOption = (inputValue, option) =>
    option.title?.indexOf(inputValue) > -1;

  return (<>
    <Divider orientation="left" plain>{$t('app.options.userid')}</Divider>
    <Transfer
      oneWay
      showSearch
      titles={[
        <Text type="secondary" key="0">{$t('app.options.db.userinfo.right.none')}</Text>,
        <Text type="secondary" key="1">{$t('app.options.db.userinfo.right.have')}</Text>,
      ]}
      listStyle={{ width: 360, height: 360 }}
      dataSource={userData}
      filterOption={filterOption}
      targetKeys={checkUsers}
      selectedKeys={selectUsers}
      onChange={nextTargetKeys => {
        // console.log('nextTargetKeys:', nextTargetKeys);
        setCheckUsers(nextTargetKeys);
      }}
      onSelectChange={(sourceSelectedKeys, targetSelectedKeys) => {
        // console.log(sourceSelectedKeys, targetSelectedKeys);
        setSelectUsers([...sourceSelectedKeys, ...targetSelectedKeys]);
      }}
      render={item => item.title}
    />
  </>);
}

export default forwardRef(UserTransfer);
