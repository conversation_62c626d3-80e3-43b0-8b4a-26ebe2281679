import { useRef, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, Space, Tag, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
} from '@titd/publics/components';
import { searchForm, updateForm, assignForm, updAddrLink, assignUserLink } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

import UserTransfer from './Transfer';

const { Text } = Typography;
const { IndexColumn, TableBadgeDot, TableSwitch } = TableFields;
const { ModalForm } = forms;
const { exchangeAllDict, frontTypeDict, gatewayStatusDict, gatewayActionDict, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, CURD, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getMemUserList } = getList;

const MemGatewayBase = props => {
  const { paramId } = props;

  const queryForm = useRef(null);
  const updateRef = useRef(null);
  const assignRef = useRef(null);
  const userRef = useRef(null);
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.db.gateway.id'),
    dataIndex: 'gatewayid',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.db.gateway.front'),
    dataIndex: 'gatewaytype',
    render: (text, record) => frontTypeDict[record.exchangeid] ? frontTypeDict[record.exchangeid][text] : text,
  }, {
    title: $t('app.options.db.gateway.address'),
    dataIndex: 'gatewayaddr',
  }, {
    title: $t('app.options.db.gateway.status'),
    dataIndex: 'gatewaystatus',
    render: text => gatewayStatusDict[text] || text,
  }, {
    title: $t('app.options.db.gateway.action'),
    dataIndex: 'gatewayaction',
    render: (text, record) => TableSwitch(modal, text, record, 'gatewayid', toChgStatus, gatewayActionDict, 1)
  }, {
  //   title: $t('app.options.userid'),
  //   dataIndex: 'userid',
  //   render: text => formatUser(text),
  // }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 210,
    render: (_, record) => (
      <ActionLinks links={[
        updAddrLink(toUpdate, record),
        assignUserLink(toAssign, record),
      ]} />
    )
  }];

  const formatUser = text => (<>
    {$t('app.options.userid') + '：'}
    <Space size={[0, 16]} wrap>
      {text?.map((item, idx) => {
        const userItem = userOptions?.find(i => i.value === item);
        return (<Tag key={idx}>{userItem?.label || item}</Tag>);
      })}
    </Space>
  </>);

  const [forntOptions, setFrontOptions] = useState([]);
  const [userOptions, setUserOptions] = useState([]);
  const [oldRecord, setOldRecord] = useState({});

  const createSearchForm = useMemo(() => searchForm($t, forntOptions), [$t, forntOptions]);
  const createUpdateForm = useMemo(() => updateForm($t), [$t]);
  const createAssignForm = useMemo(() => assignForm($t, userOptions), [$t, userOptions]);

  useMount(() => {
    getMemUserList(Request.post, defaultParams, setUserOptions);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryGateway, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => {
        let status, action;
        if (item.gatewaystatus) {
          status = item.gatewaystatus % 10;
          action = Math.floor((item.gatewaystatus / 10) % 10);
        }

        return {
          ...item,
          id: params.beginno + item.rspseqno - 1,
          userid: item.userid?.split('|').filter(i => i) || [],
          gatewaystatus: status,
          // 1运行,2暂停
          gatewayaction: action === 1 ? 1 : 0,
        };
      });

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    }
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run({
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    });
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const updateFunc = async paramData => {
    fetchFunc(MESSAGE_TYPE.MemUpdGateway, paramData, () => {
      message.success(CURD.Update + CURD.StatusOkMsg);
      onRefresh();
    });
  }

  // operatetype: 1暂停,2启动,3修改地址,4增加用户
  const toChgStatus = (record, checked) => {
    // console.log('chgStatus', record, checked);
    const postData = {
      ...defaultParams,
      gatewayid: record.gatewayid,
      gatewaytype: record.gatewaytype,
      exchangeid: record.exchangeid,
      operatetype: checked ? 2 : 1,
    }

    updateFunc(postData);
  }
  const toUpdate = record => {
    // console.log('Update', record);
    setOldRecord(record);
    updateRef.current.show(1, record);
  }
  const toAssign = record => {
    // console.log('AssignUser', record);
    setOldRecord(record);
    assignRef.current.show(0, record);
  }

  const updateSubmit = form => {
    if (oldRecord && oldRecord.gatewayaddr !== form.gatewayaddr) {
      const postData = {
        ...defaultParams,
        ...form,
        operatetype: 3,
      }

      updateFunc(postData);
    }
  }
  const assignSubmit = form => {
    // 最后一步权限保存，统一处理
    const newUsers = userRef.current?.getValues();

    if (oldRecord && oldRecord.userid?.length !==  newUsers?.length) {
      const addUsers = newUsers.filter(item => !oldRecord.userid.includes(item));
      const postData = {
        ...defaultParams,
        ...form,
        operatetype: 4,
        userid: addUsers.join('|'),
      }

      updateFunc(postData);
    }
  }

  const formValueChange = changedValues => {
    if ('exchangeid' in changedValues) {
      const value = changedValues.exchangeid;

      if (value) {
        setFrontOptions(frontTypeDict[value] || []);
      } else {
        setFrontOptions([]);
      }

      queryForm.current?.set({
        'gatewaytype': undefined,
      });
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.db.gateway', {
        defaultMsg: '网关管理',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
        onValuesChange: formValueChange,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
        expandable: {
          expandedRowRender: record => formatUser(record.userid),
          rowExpandable: record => record.userid?.length > 0,
        }
      }}
    />

    <ModalForm
      ref={updateRef}
      onOk={updateSubmit}
      formData={createUpdateForm}
    />

    <ModalForm
      ref={assignRef}
      onOk={assignSubmit}
      formData={createAssignForm}
    >
      <UserTransfer
        ref={userRef}
        userOptions={userOptions}
        record={oldRecord}
      />
    </ModalForm>
  </>);
}

const MemGateway = props => {
  const param = useParams();

  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <MemGatewayBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { MemGateway };
