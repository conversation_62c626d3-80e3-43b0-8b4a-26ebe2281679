// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { exchangeDict } = dicts;
const { selectOptions, selectExchange } = formOptions;

export const searchForm = ($t, userFocus, userClientOptions, loading, exchanges) => [{
  valueType: 'autocomplete',
  name: 'userclient',
  label: TableTitle(
    $t('app.options.userclient'),
    $t('app.options.userclient.tip'),
  ),
  fieldProps: {
    onFocus: userFocus,
    options: userClientOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'input',
  name: 'productid',
  label: $t('app.options.productid'),
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}];

export const addForm = $t => [{
  valueType: 'input',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    readOnly: true,
    bordered: false,
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    open: false,
    bordered: false,
    allowClear: false,
    showArrow: false,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
  fieldProps: {
    readOnly: true,
    bordered: false,
  }
}];

export const updateForm = $t => [{
  valueType: 'number',
  name: 'position',
  label: $t('app.options.postion.position'),
}, {
  valueType: 'number',
  name: 'ydposition',
  label: $t('app.options.postion.ydposition'),
}, {
  valueType: 'number',
  name: 'frozen',
  label: $t('app.options.postion.frozen'),
}, {
  valueType: 'number',
  name: 'buy',
  label: $t('app.options.postion.buy'),
}, {
  valueType: 'number',
  name: 'sell',
  label: $t('app.options.postion.sell'),
}, {
  valueType: 'number',
  name: 'holdprice',
  label: $t('app.options.postion.holdprice'),
}, {
  valueType: 'input',
  name: 'positionall',
  label: $t('app.options.postion.total'),
  fieldProps: {
    readOnly: true,
    bordered: false,
  }
}];

export const updateFormExtra = $t => [{
  valueType: 'input',
  name: 'margin',
  label: TableTitle(
    $t('app.options.postion.margin'),
    $t('app.options.postion.margin.long')
  ),
  fieldProps: {
    readOnly: true,
    bordered: false,
  }
}];
