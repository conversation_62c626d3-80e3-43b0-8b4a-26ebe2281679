import { useRef, useEffect, useMemo } from 'react';
import { Card, Button, App } from 'antd';

import {
  forms,
} from '@titd/publics/components';
import {
  consts,
  useFormattedMessage,
  Request,
} from '@titd/publics/utils';

import { updateForm, updateFormExtra } from './data';

const { NormalForm } = forms;
const { SITE_URL, MESSAGE_TYPE, CURD } = consts;

const TabForm = props => {
  const {
    record,
    params,
    afterSubmit,
  } = props;

  const lForm = useRef(null);
  const sForm = useRef(null);
  const cForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const CardItem = ({ title, refForm, formData, func }) => {
    return (
      <Card
        title={title}
        size="small"
        bordered={false}
        extra={<Button
          type="primary"
          onClick={func}
        >{$t('app.general.update')}</Button>}
      >
        <NormalForm
          ref={refForm}
          formData={formData}
        />
      </Card>
    );
  }

  useEffect(() => {
    if (record) {
      lForm.current?.set({
        position: record.lposition,
        ydposition: record.lydposition,
        frozen: record.lfrozen,
        buy: record.lbuy,
        sell: record.lsell,
        holdprice: record.lholdprice,
        positionall: record.lpositionall,
      });

      sForm.current?.set({
        position: record.sposition,
        ydposition: record.sydposition,
        frozen: record.sfrozen,
        buy: record.sbuy,
        sell: record.ssell,
        holdprice: record.sholdprice,
        positionall: record.spositionall,
        margin: record.smargin || 0,
      });

      cForm.current?.set({
        position: record.cposition,
        ydposition: record.cydposition,
        frozen: record.cfrozen,
        buy: record.cbuy,
        sell: record.csell,
        holdprice: record.choldprice,
        positionall: record.cpositionall,
      });
    }

    // return () => {
    //   abort();
    // }
  }, [record]);

  const createUpdateForm = useMemo(() => updateForm($t), [$t]);
  const createUpdateFormExtra = useMemo(() => updateFormExtra($t), [$t]);

  const formSubmit = async (form, type) => {
    const postData = {
      ...params,
      ...form,
      exchangeid: record.exchangeid,
      clientid: record.clientid,
      instrumentid: record.instrumentid,
      postype: type,
    }

    delete postData.positionall;

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemUpdPosition, params.svrid), postData);
    if (resp.length > 0 && resp[0].responsecode < 1) {
      message.success(CURD.Update + CURD.StatusOkMsg);
      afterSubmit();
    } else {
      message.error(resp[0]?.responsestr || CURD.ServerError);
    }
  }

  return (<>
    <CardItem
      title={$t('app.options.postion.l')}
      refForm={lForm}
      formData={createUpdateForm}
      func={() => lForm.current?.submit((form) => formSubmit(form, 1))}
    />

    <CardItem
      title={$t('app.options.postion.s')}
      refForm={sForm}
      formData={[...createUpdateForm, ...createUpdateFormExtra]}
      func={() => sForm.current?.submit((form) => formSubmit(form, 2))}
    />

    <CardItem
      title={$t('app.options.postion.c')}
      refForm={cForm}
      formData={createUpdateForm}
      func={() => cForm.current?.submit((form) => formSubmit(form, 3))}
    />
  </>);
}

export default TabForm;
