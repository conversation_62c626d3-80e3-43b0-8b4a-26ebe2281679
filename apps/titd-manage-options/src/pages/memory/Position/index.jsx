import { useRef, useState, useMemo, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
  ActionLinks,
  forms,
  btns,
  links,
  exportDownModal,
  callOptionModal,
  putOptionModal,
} from '@titd/publics/components';
import { searchForm, addForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  currency,
  errorResp,
} from '@titd/publics/utils';

import TabForm from './TabForm';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableBadgeDot, TableTitle } = TableFields;
const { DrawerForm } = forms;
const { detailLink, showCalcLink } = links;
const { exportAllBtn } = btns;
const { exchangeAllDict, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList } = getList;
const { getFloat } = currency;

const PositionBase = props => {
  const { paramId } = props;

  const navigate = useNavigate();
  const queryForm = useRef(null);
  const drawerForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [selectRecord, setSelectRecord] = useState({});
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = exchangeList && exchangeList[paramId];

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type, params.svrid), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>,
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.productid'),
    dataIndex: 'productid',
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.postion.l'),
    className: 'ti-group-border',
    children: [{
      title: TableTitle(
        $t('app.options.postion.position'),
        $t('app.options.postion.position.long')
      ),
      dataIndex: 'lposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.ydposition'),
        $t('app.options.postion.ydposition.long')
      ),
      dataIndex: 'lydposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.total'),
        $t('app.options.postion.tips')
      ),
      dataIndex: 'lpositionall',
      align: 'right',
      render: (text, record) => <>
        <Text strong>{NegaNumber(text)}</Text>
        {DEFAULT_CONFIGS.SHOW_CHILD && <Text type="secondary">{` (${record.lvirposition})`}</Text>}
      </>
    }, {
      title: $t('app.options.postion.frozen'),
      dataIndex: 'lfrozen',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.buy'),
      dataIndex: 'lbuy',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.sell'),
      dataIndex: 'lsell',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.holdprice'),
        $t('app.options.postion.holdprice.long')
      ),
      dataIndex: 'lholdprice',
      align: 'right',
      className: 'ti-group-border',
      render: text => NegaNumber(text, 4, 4),
    // }, {
    //   title: TableTitle('持仓保证金', '期权会随着行情的变化而变化, 期货的保证金买入后不变'),
    //   dataIndex: 'lmargin',
    //   align: 'right',
    //   className: 'ti-group-border',
    //   render: text => NegaNumber(text)
    }]
  }, {
    title: $t('app.options.postion.s'),
    className: 'ti-group-border',
    children: [{
      title: TableTitle(
        $t('app.options.postion.position'),
        $t('app.options.postion.position.long')
      ),
      dataIndex: 'sposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.ydposition'),
        $t('app.options.postion.ydposition.long')
      ),
      dataIndex: 'sydposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.total'),
        $t('app.options.postion.tips')
      ),
      dataIndex: 'spositionall',
      align: 'right',
      render: (text, record) => <>
        <Text strong>{NegaNumber(text)}</Text>
        {DEFAULT_CONFIGS.SHOW_CHILD && <Text type="secondary">{` (${record.svirposition})`}</Text>}
      </>
    }, {
      title: $t('app.options.postion.frozen'),
      dataIndex: 'sfrozen',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.buy'),
      dataIndex: 'sbuy',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.sell'),
      dataIndex: 'ssell',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.holdprice'),
        $t('app.options.postion.holdprice.long')
      ),
      dataIndex: 'sholdprice',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }, {
      title: TableTitle(
        $t('app.options.postion.margin'),
        $t('app.options.postion.margin.long')
      ),
      dataIndex: 'smargin',
      align: 'right',
      className: 'ti-group-border',
      render: text => NegaNumber(text, 2, 2),
    }]
  }, {
    title: $t('app.options.postion.c'),
    children: [{
      title: TableTitle(
        $t('app.options.postion.position'),
        $t('app.options.postion.position.long')
      ),
      dataIndex: 'cposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.ydposition'),
        $t('app.options.postion.ydposition.long')
      ),
      dataIndex: 'cydposition',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.total'),
        $t('app.options.postion.tips')
      ),
      dataIndex: 'cpositionall',
      align: 'right',
      render: text => <Text strong>{NegaNumber(text)}</Text>
    }, {
      title: $t('app.options.postion.frozen'),
      dataIndex: 'cfrozen',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.buy'),
      dataIndex: 'cbuy',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.postion.sell'),
      dataIndex: 'csell',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: TableTitle(
        $t('app.options.postion.holdprice'),
        $t('app.options.postion.holdprice.long')
      ),
      dataIndex: 'choldprice',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    // }, {
    //   title: TableTitle('持仓保证金', '期权会随着行情的变化而变化, 期货的保证金买入后不变'),
    //   dataIndex: 'cmargin',
    //   align: 'right',
    //   render: text => NegaNumber(text)
    }]
  }, {
    title: $t('app.options.action'),
    dataIndex: 'actions',
    align: 'center',
    fixed: 'right',
    width: 190,
    render: (_, record) => (
      <ActionLinks links={[
        detailLink(toDetail, record),
        showCalcLink(toShowCalc, record),
      ]} />
    )
  }];

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false, exchanges), [$t, userFocus, userClientGroup, exchanges]);
  const createAddForm = useMemo(() => addForm($t), [$t]);

  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryPosition, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
        lmargin: getFloat(item.lmargin, 4),
        smargin: getFloat(item.smargin, 4),
        cmargin: getFloat(item.cmargin, 4),
        lpositionall: item.lposition + item.lydposition,
        spositionall: item.sposition + item.sydposition,
        cpositionall: item.cposition + item.cydposition,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    defaultParams: [defaultParams],
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(defaultParams);
  }

  const toDetail = (record) => {
    // console.log('Detail', record);
    drawerForm.current.show(`${record.clientid} - ${record.instrumentid}`, record);
    setSelectRecord(record);
  }

  const afterSubmit = () => {
    drawerForm.current.close(onRefresh);
  }

  const toExport = () => {
    // 用户代码、交易编码
    const userclient = queryForm.current?.get('userclient');
    let exClientid = '';
    if (userclient) {
      const clientArr = userclient.split(':');
      if (clientArr.length > 0) {
        exClientid = clientArr[1];
      }
    }

    fetchFunc(MESSAGE_TYPE.DownloadPosition, {
      ...defaultParams,
      clientid: exClientid,
      endno: 9999
    }, resp => {
      // console.log(resp);
      if (resp.errmessage) {
        exportDownModal(resp.errmessage);
      } else {
        message.error('导出失败');
      }
    });
  }
  const toShowCalc = record => {
    // console.log(record);
    const params = {
      ...defaultParams,
      exchangeid: record.exchangeid,
      clientid: record.clientid,
      instrumentid: record.instrumentid,
      svrid: paramId,
    };

    fetchFunc(MESSAGE_TYPE.MemQryMarginPrice, params, (resp) => {
      if (resp?.length > 0) {
        const respData = resp[0];
        const optionName = respData.instrumentname?.slice(6, 7);

        // C:认购期权, P:认沽期权
        if (optionName === 'P') {
          putOptionModal({
            intl: $t,
            respData: respData,
          });
        } else if (optionName === 'C') {
          callOptionModal({
            intl: $t,
            respData: respData,
          });
        } else {
          message.error($t('app.options.mem.margincalc.notopt'));
        }
      } else {
        message.error($t('app.options.mem.margincalc.error'));
      }
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (<>
    <PageContent
      title={$t('app.menu.mem.pos.position', {
        defaultMsg: '持仓汇总',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        actionBtns: [
          exportAllBtn(toExport),
        ],
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
        // footer: () => <FormattedMessage id={'app.options.postion.tips'} />,
      }}
    />

    <DrawerForm
      ref={drawerForm}
      formData={createAddForm}
    >
      <TabForm
        record={selectRecord}
        params={defaultParams}
        afterSubmit={afterSubmit}
      />
    </DrawerForm>
  </>);
}

const Position = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <PositionBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { Position };
