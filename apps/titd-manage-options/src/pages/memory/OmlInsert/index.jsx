import { useRef, useState, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, App } from 'antd';
import dayjs from 'dayjs';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  currency,
  getList,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { TableTag, IndexColumn, NegaNumber, TableBadgeDot, TableTitle, ErrorMsg } = TableFields;
const { ordStatusDict, exchangeAllDict, combDict, ownerTypeDict, legSideDict, coveredDict, ordActionTypeDict, ordStatusColor, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList, getErrorCodeMsg } = getList;
const { getFloat } = currency;
const { formatTime } = dateFormat;

const OmlInsertBase = props => {
  const { paramId } = props;
  const navigate = useNavigate();
  const queryForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [errorCodeMsg, setErrorCodeMsg] = useState([]);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);
  // const { get } = useFetch('');

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      {` [${record.userno}]`}
    </>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.order.cmdtype'),
    dataIndex: 'cmdtype',
    render: text => ordActionTypeDict[text] || text,
  }, {
    title: $t('app.options.order.ordtime'),
    dataIndex: 'ordtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
  //   title: '合约代码',
  //   dataIndex: 'instrumentid',
  // }, {
    title: $t('app.options.order.exrequestid'),
    dataIndex: 'exrequestid',
    render: (text, record) => <>
      <Text strong>{text}</Text>
      <Text>{` [${record.responsestr}]`}</Text>
    </>,
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno',
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
  }, {
    title: $t('app.options.order.ordresponsecode'),
    dataIndex: 'ordresponsecode',
    render: (text, record) => <ErrorMsg titdErrorCode={errorCodeMsg} errcode={text} exchangeid={record.exchangeid} />,
  }, {
  //   title: '交易编码类型',
  //   dataIndex: 'clienttype',
  //   render: text => TableBadgeDot(text, clientTypeDict, tagNormalColors, true),
  // }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.order.frontno'),
      $t('app.options.order.frontno.long')
    ),
    dataIndex: 'frontno',
  }, {
    title: $t('app.options.order.oml.side'),
    dataIndex: 'side',
    render: text => combDict[text] || text,
  }, {
    title: TableTitle(
      $t('app.options.postion.noleges'),
      $t('app.options.postion.noleges.long')
    ),
    dataIndex: 'noleges',
  }, {
    title: $t('app.options.order.volume'),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.securityid'),
    dataIndex: 'securityid',
  }, {
    title: TableTitle(
      $t('app.options.postion.combinstid'),
      $t('app.options.postion.combinstid.long')
    ),
    dataIndex: 'combinstid',
  }, {
    title: $t('app.options.order.rec2send'),
    dataIndex: 'rec2send',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.rec2exrsp'),
    dataIndex: 'rec2exrsp',
    render: text => getFloat(text, 4),
  }, {
  //   title: '',
  //   dataIndex: 'ordstatus1',
  // }, {
  //   title: '',
  //   dataIndex: 'sseexreqnum',
  // }, {
    title: $t('app.options.order.cpuid'),
    dataIndex: 'cpuid',
  }, {
  //   title: '原始报单请求编号',
  //   dataIndex: 'oriexrequestid',
  // }, {
  //   title: '订单撤销数量',
  //   dataIndex: 'cancelvolume',
  // }, {
    title: $t('app.options.order.ssepartyid'),
    dataIndex: 'ssepartyid',
  }, {
    title: $t('app.options.mem.account.frozenmargin'),
    dataIndex: 'bfrozenmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.order.bfrozenvolume'),
    dataIndex: 'bfrozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.bthawvolume'),
    dataIndex: 'bthawvolume',
    align: 'right',
    render: text => NegaNumber(text),
  // }, {
  //   title: '买方冻结',
  //   className: 'ti-group-border',
  //   children: [{
  //     title: '冻结保证金',
  //     dataIndex: 'bfrozenmargin',
  //     align: 'right',
  //     render: text => NegaNumber(text)
  //   }, {
  //     title: '冻结手续费',
  //     dataIndex: 'bfrozencommi',
  //     align: 'right',
  //     render: text => NegaNumber(text)
  //   }, {
  //     title: '冻结权利金',
  //     dataIndex: 'bfrozenpremium',
  //     align: 'right',
  //     render: text => NegaNumber(text)
  //   }, {
  //     title: '冻结数量',
  //     dataIndex: 'bfrozenvolume',
  //   }, {
  //     title: '已解冻数量',
  //     dataIndex: 'bthawvolume',
  //     className: 'ti-group-border',
  //   }]
  // }, {
  //   title: '卖方冻结',
  //   className: 'ti-group-border',
  //   children: [{
  //     title: '冻结保证金',
  //     dataIndex: 'sfrozenmargin',
  //     align: 'right',
  //     render: text => NegaNumber(text)
  //   }, {
  //     title: '冻结手续费',
  //     dataIndex: 'sfrozencommi',
  //     align: 'right',
  //     render: text => NegaNumber(text)
  //   }, {
  //     title: '冻结权利金',
  //     dataIndex: 'sfrozenpremium',
  //     align: 'right',
  //     render: text => NegaNumber(text)
  //   }, {
  //     title: '冻结数量',
  //     dataIndex: 'sfrozenvolume',
  //   }, {
  //     title: '已解冻数量',
  //     dataIndex: 'sthawvolume',
  //     className: 'ti-group-border',
  //   }]
  // }, {
  //   title: '',
  //   dataIndex: 'ordsessionno',
  // }, {
  //   title: '',
  //   dataIndex: 'ordclientno',
  // }, {
  //   title: '',
  //   dataIndex: 'ordinstrumentno',
  }, {
    title: $t('app.options.order.ownertype'),
    dataIndex: 'ownertype',
    className: 'ti-group-border',
    render: text => ownerTypeDict[text] || text,
  }, {
    title: $t('app.options.order.leg') + '1',
    className: 'ti-group-border',
    children: [{
      title: $t('app.options.instrumentid'),
      dataIndex: 'instrumentid1',
      render: (text, record) => <Text>{`${text} [${record.instrumentno1}]`}</Text>,
    }, {
      title: $t('app.options.order.oml.legside'),
      dataIndex: 'legside1',
      render: text => legSideDict[String.fromCharCode(text)] || text,
    }, {
      title: $t('app.options.postion.covereduncovered'),
      dataIndex: 'coveredoruncovered1',
      render: text => coveredDict[text] || text,
    }, {
      title: $t('app.options.postion.legorderqty'),
      dataIndex: 'legorderqty1',
      align: 'right',
      className: 'ti-group-border',
    }],
  }, {
    title: $t('app.options.order.leg') + '2',
    children: [{
      title: $t('app.options.instrumentid'),
      dataIndex: 'instrumentid2',
      render: (text, record) => <Text>{`${text} [${record.instrumentno2}]`}</Text>,
    }, {
      title: $t('app.options.order.oml.legside'),
      dataIndex: 'legside2',
      render: text => legSideDict[String.fromCharCode(text)] || text,
    }, {
      title: $t('app.options.postion.covereduncovered'),
      dataIndex: 'coveredoruncovered2',
      render: text => coveredDict[text] || text,
    }, {
      title: $t('app.options.postion.legorderqty'),
      dataIndex: 'legorderqty2',
      align: 'right',
    }],
  // }, {
  //   title: '合约代码3',
  //   dataIndex: 'instrumentno3',
  //   // render: (text, record) => <Text>{`${text} [${record.instrumentno3}]`}</Text>,
  // }, {
  //   title: '合约方向3',
  //   dataIndex: 'legside3',
  //   render: text => legSideDict[String.fromCharCode(text)] || text,
  // }, {
  //   title: '备兑标签3',
  //   dataIndex: 'coveredoruncovered3',
  //   render: text => coveredDict[text] || text,
  // }, {
  //   title: '申报数量3',
  //   dataIndex: 'legorderqty3',
  //   className: 'ti-group-border',
  // }, {
  //   title: '合约代码4',
  //   dataIndex: 'instrumentno4',
  //   // render: (text, record) => <Text>{`${text} [${record.instrumentno4}]`}</Text>,
  // }, {
  //   title: '合约方向4',
  //   dataIndex: 'legside4',
  //   render: text => legSideDict[String.fromCharCode(text)] || text,
  // }, {
  //   title: '备兑标签4',
  //   dataIndex: 'coveredoruncovered4',
  //   render: text => coveredDict[text] || text,
  // }, {
  //   title: '申报数量4',
  //   dataIndex: 'legorderqty4',
  }];

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false), [$t, userFocus, userClientGroup]);

  useMount(() => {
    getErrorCodeMsg(Request.get, setErrorCodeMsg);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryOmlInsert, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: params.beginno + item.rspseqno - 1,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
      begtime: values?.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values?.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
        !searchParams.userrange && (searchParams.userid = userclient[1]);
      } else {
        searchParams.clientid = values.userclient;
      }

      delete searchParams.userclient;
      delete searchParams.userrange;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.ord.omlinsert', {
        defaultMsg: '组合报单',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: loading,
        initValues: {
          userrange: 1,
          bssystemflag: 0,
        },
        resetSearch: false,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const OmlInsert = props => {
  const param = useParams();

  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <OmlInsertBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { OmlInsert };
