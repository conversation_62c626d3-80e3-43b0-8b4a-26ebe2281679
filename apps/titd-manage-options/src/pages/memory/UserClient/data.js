// import { Spin } from 'antd';
import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict, clientChgStatusDict } = dicts;
const { selectOptions, selectExchange } = formOptions;

export const searchForm = ($t, userFocus, userOptions, loading, clientOptions, exchanges) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.userid'),
  fieldProps: {
    onFocus: userFocus,
    options: userOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'autocomplete',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    options: clientOptions,
  }
}, {
//   valueType: 'select',
//   name: 'status',
//   label: '交易状态',
//   fieldProps: {
//     options: selectOptions(clientStatusDict)
//   }
// }, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
// }, {
//   valueType: 'number',
//   name: 'clientno',
//   label: '客户序号'
}];

export const chgStatusForm = $t => [{
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    open: false,
    bordered: false,
    allowClear: false,
    showArrow: false,
    options: selectOptions(exchangeDict)
  }
}, {
  valueType: 'text',
  name: 'clientid',
  label: $t('app.options.clientid'),
}, {
  valueType: 'radio',
  name: 'status',
  label: $t('app.options.mem.userclient.status'),
  rules: [{ required: true }],
  fieldProps: {
    optionType: 'button',
    buttonStyle: 'solid',
    options: selectOptions(clientChgStatusDict)
  }
}];
