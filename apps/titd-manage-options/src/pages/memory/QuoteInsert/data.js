// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { consts, dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { DEFAULT_CONFIGS } = consts;
const { selectOptions, selectObjOptions } = formOptions;
const { ordStatusDict, ordStatusExtraEict } = dicts;

export const searchForm = ($t, userFocus, userClientOptions, loading, isRequired = true) => {
  const extraOptions = DEFAULT_CONFIGS.SHOW_CHILD ? selectObjOptions(ordStatusExtraEict, { isNum: true }) : [];

  return [{
    valueType: 'autocomplete',
    name: 'userclient',
    label: TableTitle(
      $t('app.options.userclient'),
      $t('app.options.userclient.tip'),
    ),
    rules: [{
      required: isRequired,
      message: $t('app.general.please') + $t('app.options.userclient'),
    }],
    fieldProps: {
      onFocus: userFocus,
      options: userClientOptions,
      // notFoundContent: loading && (<Spin size="small" />),
    }
  }, {
    valueType: 'number',
    name: 'sessionno',
    label: $t('app.options.sessionno'),
  }, {
    valueType: 'input',
    name: 'instrumentid',
    label: $t('app.options.instrumentid'),
  }, {
    valueType: 'input',
    name: 'ordersysid',
    label: TableTitle(
      $t('app.options.order.ordersysid'),
      $t('app.options.order.ordersysid.long')
    ),
  }, {
    valueType: 'select',
    name: 'ordstatus',
    label: $t('app.options.order.bordstatus'),
    fieldProps: {
      options: selectOptions(ordStatusDict).concat(extraOptions)
    }
  }, {
    valueType: 'select',
    name: 'ordstatus1',
    label: $t('app.options.order.sordstatus'),
    fieldProps: {
      options: selectOptions(ordStatusDict)
    }
  }, {
    valueType: 'input',
    name: 'quotereqid',
    label: $t('app.options.order.quotereqid'),
  }];
}
