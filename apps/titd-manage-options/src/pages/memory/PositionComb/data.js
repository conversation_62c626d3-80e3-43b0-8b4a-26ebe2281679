// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { exchangeDict, combIdDict } = dicts;
const { selectOptionsStr, selectExchange } = formOptions;

export const searchForm = ($t, userFocus, userClientOptions, loading, exchanges, isRequired = true) => [{
  valueType: 'autocomplete',
  name: 'userclient',
  label: TableTitle(
    $t('app.options.userclient'),
    $t('app.options.userclient.tip'),
  ),
  rules: [{
    required: isRequired,
    message: $t('app.general.please') + $t('app.options.userclient'),
  }],
  fieldProps: {
    onFocus: userFocus,
    options: userClientOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: $t('app.options.exchangeid'),
  fieldProps: {
    options: selectExchange(exchangeDict, exchanges)
  }
}, {
  valueType: 'input',
  name: 'combinstid',
  label: TableTitle(
    $t('app.options.postion.combinstid'),
    $t('app.options.postion.combinstid.long')
  )
}, {
  valueType: 'select',
  name: 'omlid',
  label: $t('app.options.postion.omlid'),
  fieldProps: {
    options: selectOptionsStr(combIdDict),
  }
}];
