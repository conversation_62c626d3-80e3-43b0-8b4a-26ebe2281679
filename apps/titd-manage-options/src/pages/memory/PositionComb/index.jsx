import { useRef, useState, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, App } from 'antd';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount } from 'ahooks';

import { useRecoilValue } from 'recoil';
import { exchangeState } from '@titd/publics/states';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, NegaNumber, TableBadgeDot, TableTitle } = TableFields;
const { exchangeAllDict, legSideDict, coveredDict, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientList } = getList;

const PositionCombBase = props => {
  const { paramId } = props;
  const navigate = useNavigate();
  const queryForm = useRef(null);
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  // 获取交易所信息
  const exchangeList = useRecoilValue(exchangeState);
  const exchanges = exchangeList && exchangeList[paramId];

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: TableTitle(
      $t('app.options.postion.combinstid'),
      $t('app.options.postion.combinstid.long')
    ),
    dataIndex: 'combinstid',
  }, {
    title: $t('app.options.postion.omlid'),
    dataIndex: 'omlid',
  }, {
    title: $t('app.options.postion.volume'),
    dataIndex: 'volume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: TableTitle(
      $t('app.options.postion.frozenvolume'),
      $t('app.options.postion.frozenvolume.long')
    ),
    dataIndex: 'frozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.postion.legmargin'),
    dataIndex: 'margin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.postion.noleges'),
    dataIndex: 'noleges',
    align: 'right',
    render: text => NegaNumber(text),
    className: 'ti-group-border',
  }, {
    title: $t('app.options.postion.legsecurityid') + '1',
    dataIndex: 'legsecurityid1',
    render: (text, record) => text !== '' ? <Text>{`${text} [${record.legsecurityno1}]`}</Text> : '',
  }, {
    title: $t('app.options.postion.legside') + '1',
    dataIndex: 'legside1',
    render: text => legSideDict[String.fromCharCode(text)] || text,
  }, {
    title: $t('app.options.postion.covereduncovered') + '1',
    dataIndex: 'coveredoruncovered1',
    render: text => coveredDict[text] || text,
  }, {
    title: $t('app.options.postion.legorderqty') + '1',
    dataIndex: 'legorderqty1',
    align: 'right',
    render: text => NegaNumber(text),
    className: 'ti-group-border',
  }, {
    title: $t('app.options.postion.legsecurityid') + '2',
    dataIndex: 'legsecurityid2',
    render: (text, record) => text !== '' ? <Text>{`${text} [${record.legsecurityno2}]`}</Text> : '',
  }, {
    title: $t('app.options.postion.legside') + '2',
    dataIndex: 'legside2',
    render: text => legSideDict[String.fromCharCode(text)] || text,
  }, {
    title: $t('app.options.postion.covereduncovered') + '2',
    dataIndex: 'coveredoruncovered2',
    render: text => coveredDict[text] || text,
  }, {
    title: $t('app.options.postion.legorderqty') + '2',
    dataIndex: 'legorderqty2',
    align: 'right',
    render: text => NegaNumber(text),
  // }, {
  //   title: '合约编码3',
  //   dataIndex: 'legsecurityid3',
  //   render: (text, record) => text !== '' ? <Text>{`${text} [${record.legsecurityno3}]`}</Text> : ''
  // }, {
  //   title: '持仓方向3',
  //   dataIndex: 'legside3',
  //   render: text => legSideDict[String.fromCharCode(text)] || text,
  // }, {
  //   title: '备兑标签3',
  //   dataIndex: 'coveredoruncovered3',
  //   render: text => coveredDict[text] || text,
  // }, {
  //   title: '申报数量3',
  //   dataIndex: 'legorderqty3',
  //   className: 'ti-group-border',
  // }, {
  //   title: '合约编码4',
  //   dataIndex: 'legsecurityid4',
  //   render: (text, record) => text !== '' ? <Text>{`${text} [${record.legsecurityno4}]`}</Text> : ''
  // }, {
  //   title: '持仓方向4',
  //   dataIndex: 'legside4',
  //   render: text => legSideDict[String.fromCharCode(text)] || text,
  // }, {
  //   title: '备兑标签4',
  //   dataIndex: 'coveredoruncovered4',
  //   render: text => coveredDict[text] || text,
  // }, {
  //   title: '申报数量4',
  //   dataIndex: 'legorderqty4',
  }];

  const [userClientGroup, setUserClientGroup] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientList(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userClientGroup, false, exchanges), [$t, userFocus, userClientGroup, exchanges]);

  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryPositionComb, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: item.rspseqno,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
    };

    // 用户代码、交易编码
    if (values?.userclient) {
      const userclient = values.userclient?.split(':');
      if (userclient.length > 0) {
        searchParams.clientid = userclient[0];
      } else {
        searchParams.clientid = values.userclient;
      }
      delete searchParams.userclient;
    }

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.pos.positioncomb', {
        defaultMsg: '组合持仓',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        initValues: {
          exchangeid: exchanges?.length === 1 ? Number(exchanges[0]) : null,
        },
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const PositionComb = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <PositionCombBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { PositionComb };
