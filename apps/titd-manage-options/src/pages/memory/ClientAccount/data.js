import { dicts, formOptions } from '@titd/publics/utils';

const { exchangeDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = (userOptions, clientOptions) => [{
  valueType: 'select',
  name: 'userid',
  label: '交易用户代码',
  tooltip: '筛选“交易编码”用，不做实际查询',
  fieldProps: {
    showSearch: true,
    options: userOptions,
  }
}, {
  valueType: 'select',
  name: 'clientid',
  label: '交易编码',
  fieldProps: {
    showSearch: true,
    options: clientOptions,
  }
}, {
  valueType: 'input',
  name: 'accountid',
  label: '资金账户',
}, {
  valueType: 'select',
  name: 'exchangeid',
  label: '交易所',
  fieldProps: {
    options: selectOptions(exchangeDict)
  }
}];
