// import { Spin } from 'antd';
import { TableFields } from '@titd/publics/components';
import { dicts, formOptions } from '@titd/publics/utils';

const { TableTitle } = TableFields;
const { ordStatusDict } = dicts;
const { selectOptions } = formOptions;

export const searchForm = ($t, userFocus, userOptions, loading, clientOptions, isRequired = true) => [{
  valueType: 'autocomplete',
  name: 'userid',
  label: $t('app.options.userid'),
  rules: [{ required: isRequired }],
  fieldProps: {
    onFocus: userFocus,
    options: userOptions,
    // notFoundContent: loading && (<Spin size="small" />),
  }
}, {
  valueType: 'autocomplete',
  name: 'clientid',
  label: $t('app.options.clientid'),
  fieldProps: {
    options: clientOptions,
  }
}, {
  valueType: 'input',
  name: 'instrumentid',
  label: $t('app.options.instrumentid'),
}, {
  valueType: 'input',
  name: 'ordersysid',
  label: TableTitle(
    $t('app.options.order.ordersysid'),
    $t('app.options.order.ordersysid.long')
  ),
}, {
  valueType: 'number',
  name: 'begseqno',
  label: TableTitle(
    $t('app.options.rtn.begseqno'),
    $t('app.options.rtn.begseqno.long')
  ),
}, {
  valueType: 'number',
  name: 'endseqno',
  label: TableTitle(
    $t('app.options.rtn.endseqno'),
    $t('app.options.rtn.endseqno.long')
  ),
}, {
  valueType: 'picker',
  name: 'begtime',
  label: $t('app.options.order.begtime'),
  fieldProps: {
    type: 'time',
  }
}, {
  valueType: 'picker',
  name: 'endtime',
  label: $t('app.options.order.endtime'),
  fieldProps: {
    type: 'time'
  }
}, {
//   valueType: 'select',
//   name: 'side',
//   label: $t('app.options.postion.side'),
//   fieldProps: {
//     options: selectOptions(sideDict)
//   }
// }, {
  valueType: 'select',
  name: 'ordstatus',
  label: $t('app.options.order.ordstatus'),
  fieldProps: {
    options: selectOptions(ordStatusDict)
  }
}, {
  valueType: 'input',
  name: 'bidorderid',
  label: TableTitle(
    $t('app.options.rtn.bidorderid'),
    $t('app.options.rtn.bidorderid.long')
  ),
}, {
  valueType: 'input',
  name: 'askorderid',
  label: TableTitle(
    $t('app.options.rtn.askorderid'),
    $t('app.options.rtn.askorderid.long')
  ),
}];
