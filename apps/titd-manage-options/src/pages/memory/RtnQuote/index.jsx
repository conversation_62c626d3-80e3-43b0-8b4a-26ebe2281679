import { useRef, useState, useEffect, useMemo, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Typography, App } from 'antd';
import dayjs from 'dayjs';
// import KeepAlive from 'react-activation';
import { useRequest, useUnmount, useDebounceFn } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  dateFormat,
  optDiffTwo,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { TableTitle, IndexColumn, TableTag, NegaNumber, TableBadgeDot } = TableFields;
const { exchangeAllDict, ordStatusDict, ownerTypeDict, offsetFlagDict, ordStatusColor, tagNormalColors } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getUserClientGroup, getClientByUser } = getList;
const { formatTime } = dateFormat;

const RtnQuoteBase = props => {
  const { paramId } = props;

  const queryForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const [request, , loading] = useFetch(SITE_URL.BASE);

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
  //   title: '交易所回报序号',
  //   dataIndex: 'exsequenceno',
  //   render: text => <IndexColumn border>{text}</IndexColumn>,
  // }, {
    title: $t('app.options.rtn.sequenceno'),
    dataIndex: 'sequenceno',
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.rtn.transtime'),
    dataIndex: 'transtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno'
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
  }, {
    title: $t('app.options.exchangeid'),
    dataIndex: 'exchangeid',
    render: text => TableBadgeDot(text, exchangeAllDict, tagNormalColors, true),
  }, {
    title: $t('app.options.order.quotereqid'),
    dataIndex: 'quotereqid',
  }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    className: 'ti-group-border',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.buy'),
    className: 'ti-group-border',
    children: [{
      title: TableTitle(
        $t('app.options.rtn.bidorderid'),
        $t('app.options.rtn.bidorderid.long')
      ),
      dataIndex: 'bidorderid',
    }, {
      title: $t('app.options.order.quote.px'),
      dataIndex: 'bidpx',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }, {
      title: $t('app.options.order.volume'),
      dataIndex: 'bidsize',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.order.quote.offsetflag'),
      dataIndex: 'bidoffsetflag',
      className: 'ti-group-border',
      render: text => offsetFlagDict[text] || text,
    }]
  }, {
    title: $t('app.options.order.sell'),
    className: 'ti-group-border',
    children: [{
      title: TableTitle(
        $t('app.options.rtn.askorderid'),
        $t('app.options.rtn.askorderid.long')
      ),
      dataIndex: 'askorderid',
    }, {
      title: $t('app.options.order.quote.px'),
      dataIndex: 'askpx',
      align: 'right',
      render: text => NegaNumber(text, 4, 4),
    }, {
      title: $t('app.options.order.volume'),
      dataIndex: 'asksize',
      align: 'right',
      render: text => NegaNumber(text),
    }, {
      title: $t('app.options.order.quote.offsetflag'),
      dataIndex: 'askoffsetflag',
      className: 'ti-group-border',
      render: text => offsetFlagDict[text] || text,
    }]
  }, {
    title: $t('app.options.order.ownertype'),
    dataIndex: 'ownertype',
    render: text => ownerTypeDict[text] || text,
  }, {
    title: TableTitle(
      $t('app.options.userid'),
      $t('app.options.rtn.userid.long')
    ),
    dataIndex: 'userid',
  }];

  const [userOptions, setUserOptions] = useState([]);
  const [userClientGroup, setUserClientGroup] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);

  const userFocus = useCallback(() => {
    getUserClientGroup(Request.post, defaultParams, setUserClientGroup);
  }, [defaultParams]);

  const createSearchForm = useMemo(() => searchForm($t, userFocus, userOptions, false, clientOptions), [$t, userFocus, userOptions, clientOptions]);

  useUnmount(() => cancel());

  useEffect(() => {
    const users = optDiffTwo(userClientGroup);

    setUserOptions(users);
  }, [userClientGroup]);

  const getTableData = async ({ noDataShow, ...params }) => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryRtnQuote, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: params.beginno + item.rspseqno - 1,
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading: isLoading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
      // instrumentid: values?.instrumentid?.trim(),
      begtime: values?.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values?.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }
    // 重置股东代码
    setClientOptions([]);

    run(null);
  }

  const { run: clientRun } = useDebounceFn(async (value) => {
    if (value) {
      const clientGroup = getClientByUser(userClientGroup, value);

      setClientOptions(clientGroup);
    } else {
      setClientOptions([]);
    }

    // 交易编码赋值
    queryForm.current?.set({
      'clientid': undefined,
    });
  }, { wait: 500 });
  const formValueChange = changedValues => {
    if ('userid' in changedValues) {
      const value = changedValues.userid;
      clientRun(value);
    }
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.rtn.rtnquote', {
        defaultMsg: '报价回报',
      })}
      filterProps={{
        ref: queryForm,
        formData: createSearchForm,
        isLoading: isLoading,
        onValuesChange: formValueChange,
        onSearch: onSearch,
        onReset: onReset,
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: isLoading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const RtnQuote = props => {
  const param = useParams();

  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <RtnQuoteBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { RtnQuote };
