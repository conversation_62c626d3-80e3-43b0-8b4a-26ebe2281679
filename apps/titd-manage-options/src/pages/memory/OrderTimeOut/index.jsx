import { useRef, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';
import { Typography, Button, App } from 'antd';
import dayjs from 'dayjs';
// import KeepAlive from 'react-activation';
import { useRequest, useMount, useUnmount } from 'ahooks';

import {
  PageContent,
  TableFields,
} from '@titd/publics/components';
import { searchForm } from './data';

import {
  consts,
  sessionParams,
  useFormattedMessage,
  Request,
  dicts,
  tools,
  getList,
  currency,
  dateFormat,
  errorResp,
} from '@titd/publics/utils';

const { Text } = Typography;
const { IndexColumn, TableTag, NegaNumber, ErrorMsg, TableTextNoTrim } = TableFields;
const { ordStatusDict, ordActionTypeDict, ordStatusColor } = dicts;
const { DEFAULT_CONFIGS, SITE_URL, MESSAGE_TYPE, TABLE_WIDTH } = consts;
const { DEFAULT_SESSION_PARAMS } = sessionParams;
const { objFilter } = tools;
const { getErrorCodeMsg } = getList;
const { getFloat } = currency;
const { formatTime } = dateFormat;

const OrderTimeOutBase = props => {
  const { paramId } = props;

  const queryForm = useRef(null);
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { $t } = useFormattedMessage();

  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(DEFAULT_CONFIGS.PAGE);
  const [limit, setLimit] = useState(DEFAULT_CONFIGS.LIMIT);

  const [errorCodeMsg, setErrorCodeMsg] = useState([]);

  const defaultParams = useMemo(() => ({
    ...DEFAULT_SESSION_PARAMS(),
    beginno: DEFAULT_CONFIGS.PAGE,
    endno: limit,
    svrid: paramId,
  }), [paramId, limit]);

  // const { post, response, loading: isLoading, abort } = useFetch(SITE_URL.BASE);
  // const { get } = useFetch('');

  const fetchFunc = async (type, params, func) => {
    const resp = await Request.post(SITE_URL.EXTEND(type), params);

    if (resp.status === 200) {
      func && func(resp.data);
    } else {
      errorResp(resp.data, navigate, message);
    }
  }

  const columns = [{
    dataIndex: 'id',
    width: TABLE_WIDTH.ID,
    align: 'center',
    fixed: 'left',
    render: text => <IndexColumn border>{text}</IndexColumn>,
  }, {
    title: $t('app.options.userid'),
    dataIndex: 'userid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.accountid'),
    dataIndex: 'accountid',
    fixed: 'left',
    render: text => <Text strong>{text}</Text>
  }, {
    title: $t('app.options.clientid'),
    dataIndex: 'clientid',
    fixed: 'left',
    render: (text, record) => <Text>{`${text} [${record.clientno}]`}</Text>,
  }, {
    title: $t('app.options.sessionno'),
    dataIndex: 'sessionno',
  }, {
    title: $t('app.options.order.cmdtype'),
    dataIndex: 'cmdtype',
    render: text => ordActionTypeDict[text] || text,
  }, {
    title: $t('app.options.order.ordtime'),
    dataIndex: 'ordtime',
    render: text => <Text strong>{formatTime(text)}</Text>,
  }, {
    title: $t('app.options.instrumentid'),
    dataIndex: 'instrumentid',
    render: (text, record) => <Text>{`${text} [${record.instrumentno}]`}</Text>,
  }, {
    title: $t('app.options.order.exrequestid'),
    dataIndex: 'exrequestid',
  }, {
    title: $t('app.options.order.ordersysid.long'),
    dataIndex: 'ordersysid',
    render: text => TableTextNoTrim(text),
  }, {
    title: $t('app.options.order.ordrequestno'),
    dataIndex: 'ordrequestno',
  }, {
    title: $t('app.options.order.localorderno'),
    dataIndex: 'localorderno',
  }, {
    title: $t('app.options.order.ordresponsecode'),
    dataIndex: 'ordresponsecode',
    render: (text, record) => <ErrorMsg titdErrorCode={errorCodeMsg} errcode={text} exchangeid={record.exchangeid} />,
  }, {
    title: $t('app.options.order.ordstatus'),
    dataIndex: 'ordstatus',
    render: text => TableTag(text, ordStatusDict, ordStatusColor),
  }, {
    title: $t('app.options.order.rec2send'),
    dataIndex: 'rec2send',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.rec2exrsp'),
    dataIndex: 'rec2exrsp',
    render: text => getFloat(text, 4),
  }, {
    title: $t('app.options.order.cpuid'),
    dataIndex: 'cpuid',
  }, {
    title: $t('app.options.order.cancelvolume'),
    dataIndex: 'cancelvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.ssepartyid'),
    dataIndex: 'ssepartyid',
  }, {
    title: $t('app.options.mem.account.frozenmargin'),
    dataIndex: 'bfrozenmargin',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.mem.account.frozencommi'),
    dataIndex: 'bfrozencommi',
    align: 'right',
    render: text => NegaNumber(text, 4, 4),
  }, {
    title: $t('app.options.mem.account.frozenpremium'),
    dataIndex: 'bfrozenpremium',
    align: 'right',
    render: text => NegaNumber(text, 2, 2),
  }, {
    title: $t('app.options.order.bfrozenvolume'),
    dataIndex: 'bfrozenvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }, {
    title: $t('app.options.order.bthawvolume'),
    dataIndex: 'bthawvolume',
    align: 'right',
    render: text => NegaNumber(text),
  }];

  useMount(() => {
    getErrorCodeMsg(Request.get, setErrorCodeMsg);
  });
  useUnmount(() => cancel());

  const getTableData = async ({ noDataShow, ...params }) => {
    if (!params) return [];

    const resp = await Request.post(SITE_URL.EXTEND(MESSAGE_TYPE.MemQryOrderTimeOut, params.svrid), params);

    // 运行一次，不刷新数据
    if (noDataShow) return [];

    const { data: respData } = resp;
    if (respData?.length > 0) {
      const tableData = respData.map(item => ({
        ...item,
        id: params.beginno + item.rspseqno - 1,
        rec2send: getFloat(item.rec2send, 4),
        rec2exrsp: getFloat(item.rec2exrsp, 4),
        bfrozenmargin: getFloat(item.bfrozenmargin, 4),
        bfrozenpremium: getFloat(item.bfrozenpremium, 4),
      }));

      setTotal(respData[0].rsptotnum);
      return tableData;
    } else {
      setTotal(0);
      return [];
    }
  }

  const {
    data: dataSource = [],
    loading,
    params: prevParams,
    run,
    refresh,
    mutate,
    cancel,
  } = useRequest(getTableData, {
    manual: true,
    onError: err => {
      const { response: errResp } = err;
      errorResp(errResp.data, navigate, message);
    },
  });

  const onRefresh = () => {
    if (page === DEFAULT_CONFIGS.PAGE) {
      refresh();
    } else {
      const copyParams = prevParams[0];

      // 重置搜索
      run({
        ...copyParams,
        beginno: DEFAULT_CONFIGS.PAGE,
        noDataShow: true,
      });
      // 重新获取当前页
      setTimeout(() => run({...copyParams}), 50);
    }
  }
  const onSearch = values => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    const searchParams = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
      // instrumentid: values.instrumentid?.trim(),
      begtime: values.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };

    run(searchParams);
  }
  const onReset = () => {
    // 重置页码
    if (page !== DEFAULT_CONFIGS.PAGE) {
      setPage(DEFAULT_CONFIGS.PAGE);
    }

    run(null);
  }

  const toTimeout = values => {
    const params = {
      ...defaultParams,
      ...objFilter(values),
      endno: limit,
      begtime: values.begtime ? Number(dayjs(values.begtime).format('Hmmss')) : 0,
      endtime: values.endtime ? Number(dayjs(values.endtime).format('Hmmss')) : 0,
    };
    // console.log('TimeOut', params);

    fetchFunc(MESSAGE_TYPE.MemUpdOrderTimeOut, params, resp => {
      message.success(resp[0]?.responsestr);
    });
  }

  const pageChange = (newPage, size) => {
    if (newPage !== page) {
      setPage(newPage);
    }

    if (limit !== size) {
      if (limit > size) {
        // 清空 dataSource，不然会报与 pageSize 数量不同
        mutate([]);
      }

      setLimit(size);
    }

    run({
      ...prevParams[0],
      beginno: size * (newPage - 1) + 1,
      endno: size * newPage,
    });
  }

  return (
    <PageContent
      title={$t('app.menu.mem.ord.ordertimeout', {
        defaultMsg: '报单超时',
      })}
      filterProps={{
        ref: queryForm,
        formData: searchForm($t),
        isLoading: loading,
        onSearch: onSearch,
        onReset: onReset,
        otherBtns: (
          <Button
            className="ti-btn-warning"
            onClick={() => queryForm.current?.submit(toTimeout)}
          >{$t('app.general.timeout')}</Button>
        ),
      }}
      tableProps={{
        columns: columns,
        dataSource: dataSource,
        isLoading: loading,
        onRefresh: onRefresh,
        checkable: false,
        page: page,
        total: total,
        pageChange: pageChange,
      }}
    />
  );
}

const OrderTimeOut = props => {
  const param = useParams();
  const paramId = param?.id || '';

  return (
    // <KeepAlive name={keepName + paramId} key={keepName + paramId}>
    <OrderTimeOutBase paramId={paramId} {...props} />
    // </KeepAlive>
  );
}

export { OrderTimeOut };
