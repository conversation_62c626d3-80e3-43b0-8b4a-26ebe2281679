import { useEffect } from 'react';
import { Outlet, Link, useLocation } from 'react-router';
import { CalendarOutlined } from '@ant-design/icons';
import { Provider } from 'use-http';

import { useRecoilState } from 'recoil';
import { checkDateState } from '@states/state';

import { SITE_URL } from '@utils/consts';
import { DEFAULT_SESSION } from '@utils/session';
import { HISTORY_DATE, TiLocalStorage } from '@utils/storage';

import './index.less';

const HistoryLayout = () => {
  const location = useLocation();

  const localDate = TiLocalStorage.get(HISTORY_DATE);
  const [checkDate, setCheckDate] = useRecoilState(checkDateState);

  useEffect(() => {
    setCheckDate(localDate);
  }, [localDate]); // eslint-disable-line react-hooks/exhaustive-deps

  const loginUser = DEFAULT_SESSION();
  const options = {
    headers: {
      Authorization: loginUser.sessions,
    }
  }

  return (<>
    <div className="ti-checkdate-content">
      <Link className="ti-checkdate-btn"
        to="/hist/checkdate"
        state={{ from: location, back: true }}
      >
        <CalendarOutlined />
        <span>{checkDate}</span>
      </Link>
    </div>
    <Provider url={SITE_URL.HISTORY} options={options}>
      <Outlet />
    </Provider>
  </>);
}

export default HistoryLayout;
