import { Navigate } from 'react-router';

import HistoryLayout from '@/layouts/History';
import RequireDate from '@/pages/sessionParams,/RequireDate';

import HistUserInfo from '@/pages/history/UserInfo';
import HistUserClient from '@/pages/history/UserClient';
import HistSession from '@/pages/history/Session';
import HistAccount from '@/pages/history/Account';
import HistCommiMargin from '@/pages/history/CommiMargin';
import HistRisk from '@/pages/history/Risk';
import HistInstrument from '@/pages/history/Instrument';
import HistPosition from '@/pages/history/Position';
import HistPositionDtl from '@/pages/history/PositionDtl';
import HistPositionComb from '@/pages/history/PositionComb';
import HistOrderInsert from '@/pages/history/OrderInsert';
import HistOrderAction from '@/pages/history/OrderAction';
import HistOmlInsert from '@/pages/history/OmlInsert';
import HistExerc from '@/pages/history/Exerc';
import HistCombExerc from '@/pages/history/CombExerc';
import HistQuoteInsert from '@/pages/history/QuoteInsert';
import HistTradeInfo from '@/pages/history/TradeInfo';
import HistRtnOrder from '@/pages/history/RtnOrder';
import HistRtnExerc from '@/pages/history/RtnExerc';
import HistRtnOml from '@/pages/history/RtnOml';
import HistRtnQuote from '@/pages/history/RtnQuote';
import HistRtnTrade from '@/pages/history/RtnTrade';
import HistRtnWithDraw from '@/pages/history/RtnWithDraw';
import HistLoginLog from '@/pages/history/LoginLog';
import HistExSubmit from '@/pages/history/ExSubmit';
import CheckDate from '@/pages/history/CheckDate';

export const routeHistPart = [{
  path: 'hist',
  element: (
    <RequireDate>
      <HistoryLayout />
    </RequireDate>
  ),
  children: [{
    index: true,
    element: <Navigate replace to="/hist/user/huserinfo" />
  }, {
    path: 'user',
    children: [{
      path: 'huserinfo',
      element: <HistUserInfo keepName="huserinfo" />
    }, {
      path: 'huserclient',
      element: <HistUserClient keepName="huserclient" />
    }, {
      path: 'hsession',
      element: <HistSession keepName="hsession" />
    }, {
      path: 'haccount',
      element: <HistAccount keepName="haccount" />
    }, {
      path: 'hcommimargin',
      element: <HistCommiMargin keepName="hcommimargin" />
    }, {
      path: 'hrisk',
      element: <HistRisk keepName="hrisk" />
    }]
  }, {
    path: 'hinstrument',
    element: <HistInstrument keepName="hinstrument" />
  }, {
    path: 'pos',
    children: [{
      path: 'hposition',
      element: <HistPosition keepName="hposition" />
    }, {
      path: 'hpositiondtl',
      element: <HistPositionDtl keepName="hpositiondtl" />
    }, {
      path: 'hpositioncomb',
      element: <HistPositionComb keepName="hpositioncomb" />
    }]
  }, {
    path: 'ord',
    children: [{
      path: 'horderinsert',
      element: <HistOrderInsert keepName="horderinsert" />
    }, {
      path: 'horderaction',
      element: <HistOrderAction keepName="horderaction" />
    }, {
      path: 'homlinsert',
      element: <HistOmlInsert keepName="homlinsert" />
    }, {
      path: 'hexerc',
      element: <HistExerc keepName="hexerc" />
    }, {
      path: 'hcombexerc',
      element: <HistCombExerc keepName="hcombexerc" />
    }, {
      path: 'hquoteinsert',
      element: <HistQuoteInsert keepName="hquoteinsert" />
    }]
  }, {
    path: 'htradeinfo',
    element: <HistTradeInfo keepName="htradeinfo" />
  }, {
    path: 'rtn',
    children: [{
      path: 'hrtnorder',
      element: <HistRtnOrder keepName="hrtnorder" />
    }, {
      path: 'hrtnexerc',
      element: <HistRtnExerc keepName="hrtnexerc" />
    }, {
      path: 'hrtnoml',
      element: <HistRtnOml keepName="hrtnoml" />
    }, {
      path: 'hrtnquote',
      element: <HistRtnQuote keepName="hrtnquote" />
    }, {
      path: 'hrtntrade',
      element: <HistRtnTrade keepName="hrtntrade" />
    }, {
      path: 'hrtnwithdraw',
      element: <HistRtnWithDraw keepName="hrtnwithdraw" />
    }]
  }, {
    path: 'hloginlog',
    element: <HistLoginLog keepName="hloginlog" />
  }, {
    path: 'hexsubmit',
    element: <HistExSubmit keepName="hexsubmit" />
  }, {
    path: 'checkdate',
    element: <CheckDate />
  }]
}];
