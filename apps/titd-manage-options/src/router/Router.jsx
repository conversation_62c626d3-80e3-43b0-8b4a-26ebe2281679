import { Suspense } from 'react';
import { RouterProvider } from 'react-router/dom';
import { createHashRouter, Navigate } from 'react-router';

import { AdminLayout, AuthLayout } from '@titd/publics/layouts';
import {
  pubmemory,
  pubsystem,
  RequireAuth,
  Login,
  PageLoading,
  NoAuthPage,
  NotFoundPage,
  ErrorPage,
} from '@titd/publics/pages';
import {
  Dashboard,
  database,
  fund,
  memory,
} from '@/pages';

const {
  UserInfo,
  UserClient,
  AppId,
  TradeTpl,
  TradeRight,
  AssignFunds,
  FrozenFunds,
  ImportLog,
  DataCompare,
  UserRisk,
  ExSubmit,
  SubAccount,
  SubClient,
  SubAssignFunds,
  OpenCloseTrans,
  SelfTradeTrans,
  Gateway,
} = database;
const {
  MemUserInfo,
  MemUserClient,
  Session,
  Account,
  CommiMargin,
  Risk,
  MemGateway,
  Instrument,
  Position,
  PositionDtl,
  PositionComb,
  OrderInsert,
  OrderAction,
  OmlInsert,
  Exerc,
  CombExerc,
  QuoteInsert,
  OrderTimeOut,
  TradeInfo,
  RtnOrder,
  RtnTrade,
  RtnExerc,
  RtnOml,
  RtnQuote,
} = memory;
const { RtnWithDraw, SelfTrade } = pubmemory;
const { SysLog, AdminUser, Roles } = pubsystem;
const { Amount, TransLog } = fund;

// import Settings from '@/pages/system/Settings';
// // import AdminLog from '@/pages/system/AdminLog';
// // import LoginLog from '@/pages/system/LoginLog';

// import Unlock from '@/pages/sessionParams,/Unlock';
// // import Register from '@/pages/sessionParams,/Register';
// // import UpdatePwd from '@/pages/sessionParams,/UpdatePwd';

// import { routeHistPart } from './routePart';

// const AdminLayout = lazy(() => import('@/layouts/Admin'));
// const AuthLayout = lazy(() => import('@/layouts/Auth'));

const routers = [{
  path: '/',
  element: (
    <RequireAuth>
      <AdminLayout />
    </RequireAuth>
  ),
  children: [{
    index: true,
    element: <Navigate replace to="/dashboard" />
  }, {
    path: 'dashboard',
    element: <Dashboard />
  // }, {
  //   path: 'settings',
  //   element: <Settings keepName="settings" />
  }, { // 场下管理
    path: 'db',
    children: [{
      index: true,
      element: <Navigate replace to="/db/userinfo" />
    }, {
      path: 'userinfo',
      element: <UserInfo keepName="userinfo" />
    }, {
      path: 'userrisk',
      element: <UserRisk keepName="userrisk" />
    }, {
      path: 'userclient',
      element: <UserClient keepName="userclient" />
    }, {
      path: 'appid',
      element: <AppId keepName="appid" />
    }, {
      path: 'tradetpl',
      element: <TradeTpl keepName="tradetpl" />
    }, {
      path: 'traderight',
      element: <TradeRight keepName="traderight" />
    }, {
      path: 'assignfunds',
      element: <AssignFunds keepName="assignfunds" />
    }, {
      path: 'frozenfunds',
      element: <FrozenFunds keepName="frozenfunds" />
    }, {
      path: 'importlog',
      element: <ImportLog keepName="importlog" />
    }, {
      path: 'datacompare',
      element: <DataCompare keepName="datacompare" />
    }, {
      path: 'exsubmit',
      element: <ExSubmit keepName="exsubmit" />
    }, {
      path: 'sub',
      children: [{
        path: 'saccount',
        element: <SubAccount keepName="saccount" />
      }, {
        path: 'sclient',
        element: <SubClient keepName="sclient" />
      }, {
        path: 'sassignfunds',
        element: <SubAssignFunds keepName="sassignfunds" />
      }]
    }, {
      path: 'limit',
      children: [{
        path: 'openclosetrans',
        element: <OpenCloseTrans keepName="openclosetrans" />
      }, {
        path: 'selftradetrans',
        element: <SelfTradeTrans keepName="selftradetrans" />
      }]
    }, {
      path: 'gateway',
      element: <Gateway keepName="gateway" />
    }]
  }, { // 场上管理
    path: 'mem',
    children: [{
      path: ':id',
      children: [{
        path: 'user',
        children: [{
          path: 'muserinfo',
          element: <MemUserInfo keepName="muserinfo" />
        }, {
          path: 'muserclient',
          element: <MemUserClient keepName="muserclient" />
        }, {
          path: 'session',
          element: <Session keepName="session" />
        }, {
          path: 'account',
          element: <Account keepName="account" />
        }, {
          path: 'commimargin',
          element: <CommiMargin keepName="commimargin" />
        }, {
          path: 'risk',
          element: <Risk keepName="risk" />
        }, {
          path: 'mgateway',
          element: <MemGateway keepName="mgateway" />
        }]
      }, {
        path: 'instrument',
        element: <Instrument keepName="instrument" />
      }, {
        path: 'pos',
        children: [{
          path: 'position',
          element: <Position keepName="position" />
        }, {
          path: 'positiondtl',
          element: <PositionDtl keepName="positiondtl" />
        }, {
          path: 'positioncomb',
          element: <PositionComb keepName="positioncomb" />
        }]
      }, {
        path: 'ord',
        children: [{
          path: 'orderinsert',
          element: <OrderInsert keepName="orderinsert" />
        }, {
          path: 'orderaction',
          element: <OrderAction keepName="orderaction" />
        }, {
          path: 'omlinsert',
          element: <OmlInsert keepName="omlinsert" />
        }, {
          path: 'exerc',
          element: <Exerc keepName="exerc" />
        }, {
          path: 'combexerc',
          element: <CombExerc keepName="combexerc" />
        }, {
          path: 'quoteinsert',
          element: <QuoteInsert keepName="quoteinsert" />
        }, {
          path: 'ordertimeout',
          element: <OrderTimeOut keepName="ordertimeout" />
         }]
      }, {
        path: 'tradeinfo',
        element: <TradeInfo keepName="tradeinfo" />
      }, {
        path: 'rtn',
        children: [{
          path: 'rtnorder',
          element: <RtnOrder keepName="rtnorder" />
        }, {
          path: 'rtntrade',
          element: <RtnTrade keepName="rtntrade" />
        }, {
          path: 'rtnexerc',
          element: <RtnExerc keepName="rtnexerc" />
        }, {
          path: 'rtnoml',
          element: <RtnOml keepName="rtnoml" />
        }, {
          path: 'rtnquote',
          element: <RtnQuote keepName="rtnquote" />
        }, {
          path: 'rtnwithdraw',
          element: <RtnWithDraw keepName="rtnwithdraw" />
        }]
      }, {
        path: 'err',
        children: [{
          path: 'selftrade',
          element: <SelfTrade keepName="selftrade" />
       }]
      }]
    }]
  }, { // 资金管理
    path: 'fund',
    children: [{
      index: true,
      element: <Navigate replace to="/fund/amount" />
    }, {
      path: 'amount',
      element: <Amount keepName="amount" />
    }, {
      path: 'translog',
      element: <TransLog keepName="translog" />
    }]
  }, { // 系统管理
    path: 'sys',
    children: [{
      index: true,
      element: <Navigate replace to="/sys/syslog" />
    }, {
      path: 'syslog',
      element: <SysLog keepName="syslog" />
    }, {
      path: 'adminuser',
      element: <AdminUser keepName="adminuser" />
    }, {
      path: 'roles',
      element: <Roles keepName="roles" />
    }]
//   }, ...routeHistPart, {
  }, {
    path: '*',
    element: <Navigate replace to="/exceptions/404" />
  }]
}, { // 登录相关
  path: 'auth',
  element: <AuthLayout />,
  children: [{
    index: true,
    element: <Navigate replace to="/auth/login" />
  }, {
    path: 'login',
    element: <Login />
  // }, {
  //   path: 'updatepwd',
  //   element: <UpdatePwd />
  // }, {
  //   path: 'unlock',
  //   element: <Unlock />
  }]
}, { // 页面错误相关
  path: 'exceptions',
  children: [{
    path: '403',
    element: <NoAuthPage />
  }, {
    path: '404',
    element: <NotFoundPage />
  }, {
    path: '500',
    element: <ErrorPage />
  }]
}, {
  path: '*',
  element: <Navigate replace to="/exceptions/404" />
}];

export const Router = () => (
  <Suspense fallback={<PageLoading />}>
    <RouterProvider router={createHashRouter(routers)} />
  </Suspense>
);
