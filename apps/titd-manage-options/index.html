<!doctype html>
<html lang="zh-cmn-<PERSON>">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />
    <meta name="theme-color" content="#fff" />
    <meta name="description" content="速子信息TITD柜台系统" />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>TITD 柜台 - 速子信息</title>
  </head>
  <body>
    <noscript>
      <div class="noscript-container">
        Hi there! Please
        <span class="noscript-enableJS">
          <a href="https://www.enablejavascript.io/en" target="_blank" rel="noopener noreferrer">
            <b>enable Javascript</b>
          </a>
        </span>
        in your browser to use TachyonINFO web apps!
      </div>
    </noscript>
    <div id="root">
      <style>
        html,body,#root{height:100%;margin:0;padding:0}#root{background-repeat:no-repeat;background-size:100% auto}.noscript-container{padding:30px;text-align:center;font-size:20px;font-family:'Lucida Sans','Lucida Sans Regular','Lucida Grande','Lucida Sans Unicode',Geneva,Verdana,sans-serif}.noscript-enableJS{padding-right:3px;padding-left:3px}.page-loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;min-height:420px}.page-loading-warp{display:flex;align-items:center;justify-content:center;padding:98px}.page-logo{margin-bottom:32px}.ant-spin{position:absolute;display:none;-webkit-box-sizing:border-box;box-sizing:border-box;margin:0;padding:0;color:rgba(0,0,0,0.65);color:#1890ff;font-size:14px;font-variant:tabular-nums;line-height:1.5;text-align:center;list-style:none;opacity:0;-webkit-transition:-webkit-transform .3s cubic-bezier(0.78,0.14,0.15,0.86);transition:-webkit-transform .3s cubic-bezier(0.78,0.14,0.15,0.86);transition:transform .3s cubic-bezier(0.78,0.14,0.15,0.86);transition:transform .3s cubic-bezier(0.78,0.14,0.15,0.86),-webkit-transform .3s cubic-bezier(0.78,0.14,0.15,0.86);-webkit-font-feature-settings:'tnum';font-feature-settings:'tnum'}.ant-spin-spinning{position:static;display:inline-block;opacity:1}.ant-spin-dot{position:relative;display:inline-block;width:20px;height:20px;font-size:20px}.ant-spin-dot-item{position:absolute;display:block;width:9px;height:9px;background-color:#1890ff;border-radius:100%;-webkit-transform:scale(0.75);-ms-transform:scale(0.75);transform:scale(0.75);-webkit-transform-origin:50% 50%;-ms-transform-origin:50% 50%;transform-origin:50% 50%;opacity:.3;-webkit-animation:antspinmove 1s infinite linear alternate;animation:antSpinMove 1s infinite linear alternate}.ant-spin-dot-item:nth-child(1){top:0;left:0}.ant-spin-dot-item:nth-child(2){top:0;right:0;-webkit-animation-delay:.4s;animation-delay:.4s}.ant-spin-dot-item:nth-child(3){right:0;bottom:0;-webkit-animation-delay:.8s;animation-delay:.8s}.ant-spin-dot-item:nth-child(4){bottom:0;left:0;-webkit-animation-delay:1.2s;animation-delay:1.2s}.ant-spin-dot-spin{-webkit-transform:rotate(45deg);-ms-transform:rotate(45deg);transform:rotate(45deg);-webkit-animation:antrotate 1.2s infinite linear;animation:antRotate 1.2s infinite linear}.ant-spin-lg .ant-spin-dot{width:32px;height:32px;font-size:32px}.ant-spin-lg .ant-spin-dot i{width:14px;height:14px}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.ant-spin-blur{background:#fff;opacity:.5}}@-webkit-keyframes antSpinMove{to{opacity:1}}@keyframes antSpinMove{to{opacity:1}}@-webkit-keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}
      </style>
      <div class="page-loading-container">
        <div class="page-logo">
          <img src="/logo.svg" width="256" />
        </div>
        <div class="page-loading-wrap">
          <div class="ant-icon ant-spin-log ant-spin-spinning">
            <span class="ant-spin-dot ant-spin-dot-spin">
              <i class="ant-spin-dot-item"></i>
              <i class="ant-spin-dot-item"></i>
              <i class="ant-spin-dot-item"></i>
              <i class="ant-spin-dot-item"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
